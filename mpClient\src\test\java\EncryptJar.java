import io.xjar.XCryptos;
import org.apache.commons.codec.digest.DigestUtils;

import java.io.File;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;

public class EncryptJar {

    static final Path base = Paths.get("");

    public static void main(String[] args) throws Exception {
        File input = base.resolve("out").resolve("artifacts").resolve("world_v5_jar").resolve("client.jar").toFile();

        Path test = base.resolve("src").resolve("test");

        Path outputPath = test.resolve("build_output")
                .resolve("lib.jar");

        File output = outputPath.toFile();

        Path javaw = test.resolve("build_output")
                .resolve("runtime")
                .resolve("runtime")
                .resolve("bin")
                .resolve("javaw.exe");

        // 计算java可执行文件的md5
        File file = javaw.toFile();
        InputStream is = Files.newInputStream(file.toPath());
        String md5 = DigestUtils.md5Hex(is);

        XCryptos.encryption()
                .from(input.getAbsolutePath())
                .use("twomiles_2024")
                .exclude("/7.21/*")
                .include("/env/*")
                .include("/mgr/*")
                .include("/net/**/*")
                .include("/tool/*")
                .include("/view/*")
                .include("/launcher/*")
                .include("/msg/*")
                .include("/game/*")
                .include("/game/*/*")
                .include("/game/*/*/*")
                .include("/app_ios/*")
                .include("/app_ios/*/*")
                .include("/app_ios/*/*/*")
                .md5Hex(md5)
                .withArg("\"-Dfile.encoding=UTF-8\"")
                .disableAttachMechanism("./runtime/bin/javaw.exe", "./runtime/bin/lib.jar")
                .to(output.getAbsolutePath());


        Path targetJar = test.resolve("build_output")
                .resolve("runtime")
                .resolve("runtime")
                .resolve("bin")
                .resolve("lib.jar");
        Files.move(outputPath, targetJar, StandardCopyOption.REPLACE_EXISTING);
        String cmd = "go build -ldflags=\"-H=windowsgui -w -s\" -o 世界ol.exe";
        File cmdPath = test.resolve("build_output").toFile();
        PackTool.execCmd(cmd, cmdPath);
        // 移动文件
        Path sourceExe = test.resolve("build_output").resolve("世界ol.exe");
        Path targetExe = test.resolve("build_output").resolve("runtime").resolve("世界ol.exe");
        Files.move(sourceExe, targetExe, StandardCopyOption.REPLACE_EXISTING);
        System.out.println("Done !!!");
    }

}
