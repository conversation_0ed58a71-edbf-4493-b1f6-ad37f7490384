package net.client;

import io.netty.bootstrap.Bootstrap;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import io.netty.channel.Channel;
import io.netty.channel.ChannelFuture;
import io.netty.channel.ChannelOption;
import io.netty.channel.EventLoopGroup;
import io.netty.channel.nio.NioEventLoopGroup;
import io.netty.channel.socket.nio.NioSocketChannel;
import net.server.Server;

import java.net.InetSocketAddress;
import java.nio.charset.StandardCharsets;

public class Client {

    Channel channel;

    public void run() {
        EventLoopGroup group = new NioEventLoopGroup();
        Bootstrap bootstrap = new Bootstrap()
                .group(group)
                .channel(NioSocketChannel.class)
                .option(ChannelOption.TCP_NODELAY, true)
                .handler(new ClientInitializer());
        try {
            ChannelFuture future = bootstrap.connect(new InetSocketAddress(Server.SERVER_HOST, Server.getPort())).sync();
            channel = future.channel();
            future.channel().closeFuture().sync();
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            group.shutdownGracefully();
        }
    }

    /**
     * 进行指令发送
     *
     * @param code
     * @param msg
     */
    public void sendCmd(int code, String msg) {
        if (this.channel == null) return;
        ByteBuf buffer = Unpooled.buffer();
        buffer.writeInt(code);
        buffer.writeBytes(msg.getBytes(StandardCharsets.UTF_8));
        channel.writeAndFlush(buffer);
    }

}
