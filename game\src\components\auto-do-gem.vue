<template>
  <div id="gem-div">
    <n-space class="line-space">
      <n-text>宝石选择</n-text>
      <select style="width: 160px" v-model="selected">
        <option v-for="item in options" :key="item.key" :value="item.value">{{ item.label }}</option>
      </select>
    </n-space>
    <n-space class="line-space">
      <n-text>最小间隔</n-text>
      <input placeholder="默认值100(0.1秒)" v-model="min"/>
    </n-space>
    <n-space class="line-space">
      <n-text>最长间隔</n-text>
      <input placeholder="默认值200(0.2秒)" v-model="max"/>
    </n-space>
    <n-space class="line-space">
      <n-text>自动停止</n-text>
      <input placeholder="宝石数超过此值则停止" v-model="stop"/>
    </n-space>
  </div>
</template>

<script setup lang="ts">


interface Props {
  callback: any,
}

const props = withDefaults(defineProps<Props>(), {callback: null})

const options = ref([])
const selected = ref(-1)
const defaultOptions = {label: "背包没发现宝石", value: -1, cnt: 0, key: -1}
const min = ref("")
const max = ref("")
const stop = ref("")

onMounted(() => {
  //@ts-ignore
  const gems = window.AutoDoGem.convertGemTypes()
  if (Object.keys(gems).length) {
    for (const key of Object.keys(gems)) {
      options.value.push({
        label: `${gems[key].name}-${gems[key]["quantity"]}个`, key,
        value: key,
        cnt: gems[key]["quantity"],
      })
    }
    selected.value = options.value[0].value
  } else {
    options.value.push(defaultOptions)
  }
  min.value = localStorage.getItem("do-gem-min") || ""
  max.value = localStorage.getItem("do-gem-max") || ""
  props.callback.value = () => {
    localStorage.setItem("do-gem-min", min.value)
    localStorage.setItem("do-gem-max", max.value)
    // 停止
    if (eval("window.AutoDoGem.run")) {
      eval("window.AutoDoGem.run=false")
      return void window.$message.success("已停止。")
    }
    // 开始
    if (selected.value == -1) {
      return void window.$message.error("请选择宝石")
    }
    try {
      if (Number(min.value) <= 0) {
        return void window.$message.error("最小间隔必须大于0")
      }
      if (Number(max.value) <= 0) {
        return void window.$message.error("最大间隔必须大于0")
      }
      if (Number(stop.value) < 4 || Number(stop.value) > 30) {
        return void window.$message.error("宝石数量范围是4-30")
      }
    } catch (e) {
      return void window.$message.error("输入的数值有误!")
    }
    let forgeScene = eval("PanelManager.getPanel(ForgeScene, !1)");
    if (!forgeScene || (forgeScene && !forgeScene["stage"])) {
      return void window.$message.error("请先进入装备打宝石面板!")
    }
    const item = options.value.find(e => e.value == selected.value)
    console.log(selected.value, options.value, item)
    eval(`window.AutoDoGem.start(${Number(stop.value)}, ${item.value}, ${Number(min.value)}, ${Number(max.value)})`)
  }
})

</script>

<style scoped>
#gem-div {

}

input {
  width: 160px;
  background-color: transparent;
  border: 1px solid hsla(240, 1%, 72%, 0.25);
  color: white;
}

.line-space {
  width: 100%;
  margin-top: 4px;
}

.n-text {
  color: white;
  font-weight: bolder;
}

</style>
