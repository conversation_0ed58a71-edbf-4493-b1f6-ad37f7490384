console.log("logic.js");

(async function () {
    const sid = setInterval(() => {
        if (loadGameScript && window.REQ_JAVA_LOAD_LOGIC != null) {
            window.REQ_JAVA_LOAD_LOGIC();
        }
        const moduleCache = window.moduleCache;
        if (!moduleCache) return;
        const mod = moduleCache.get("lib/hdsdk");
        if (!mod) return;
        const i = moduleCache.get("lib/dn_sdk_minigame_1.5.3");
        const n = {
            default: moduleCache.get("lib/gravityengine.mg.wx.min")
        };
        mod["login"] = function (e) {
            const t = this;
            t.init();
            const handle = function (o) {
                if (0 == o.status) {
                    t.uid = o.result.uid,
                        t.token = o.result.token,
                        t.openId = o.result.openid,
                        t.unionid = o.result.unionid,
                        t.username = o.result.username,
                        t.userInfo = o.result;
                    var a = o.result.platformid
                        , d = o.result.register
                        , l = o.result.session_key;
                    t.video_ad_id && wx.setStorageSync("partner_vedio_ad_id", t.video_ad_id),
                        console.log("hd login", o),
                        wx.setStorage({
                            key: "hd_access_token",
                            data: t.token
                        }),
                        t.getAdyStrategy();
                    var c = 1;
                    if (console.log("标记首次天数: " + c),
                        "{}" !== JSON.stringify(t.hd_adcode1) && t.request("user/loginext/mini", s, (function (e) {
                            console.log("wx nextlogin", e)
                        }
                        )),
                        t.wxAction_id) {
                        var u = new i.SDK({
                            user_action_set_id: t.wxAction_id,
                            secret_key: t.wxSecret_key,
                            appid: t.wxAppid
                        });
                        i.SDK.setDebug(!0),
                            u.onAppStart(),
                            u.setOpenId(t.openId + ""),
                            1 == d && u.onRegister(),
                            t.dnsdk = u,
                            t.shareInit()
                    }
                    if (t.gekey) {
                        var h = {
                            accessToken: t.gekey,
                            clientId: t.openId,
                            autoTrack: {
                                appLaunch: !0,
                                appShow: !0,
                                appHide: !0
                            },
                            name: "ge"
                        }
                            , g = new n.default(h);
                        g.setupAndStart(),
                            t.ge = g,
                            g.initialize({
                                name: t.username,
                                version: 1,
                                openid: t.openId,
                                enable_sync_attribution: !1
                            }).then((function (e) {
                                console.log("initialize success " + e)
                            }
                            )).catch((function (e) {
                                console.log("initialize failed, error is " + e)
                            }
                            )),
                            setTimeout((function () {
                                0 == d || g.registerEvent()
                            }
                            ), 5e3)
                    }
                    setTimeout((function () {
                        t.initquery(),
                            "{}" === t.hd_adcode1 && t.request("user/loginext/mini", s, (function (e) {
                                e.status
                            }
                            ))
                    }
                    ), 5e3),
                        e && e({
                            state_code: 1,
                            msg: "登录成功",
                            data: {
                                uid: t.uid,
                                token: t.token,
                                session_id: t.openId,
                                session_key: l,
                                unionid: t.unionid,
                                platformid: a
                            }
                        }),
                        o.result.share_title && o.result.share_image && (t.shareTitle = o.result.share_title,
                            t.shareImg = o.result.share_image),
                        1 == o.result.shift && o.result.shift_url && wx.showModal({
                            title: "福利领取，复制浏览器打开",
                            content: o.result.shift_url,
                            showCancel: !0,
                            confirmText: "复制文本",
                            success: function (e) {
                                e.confirm && wx.setClipboardData({
                                    data: o.result.shift_url,
                                    success: function (e) {
                                        wx.getClipboardData({
                                            success: function (e) {
                                                console.log(e.data)
                                            }
                                        })
                                    }
                                })
                            }
                        })
                } else
                    wx.showToast({
                        title: "登录失败！请联系客服",
                        icon: "fail",
                        duration: 2e3
                    }),
                        e && e({
                            state_code: -1,
                            msg: "登录失败",
                            data: {}
                        })
            };
            handle({
                "status": 0,
                "info": "成功",
                "result": {
                    "notice_title": null,
                    "attestation": 0,
                    "unionid": "omfgwwwqoYJgB0Ioklc9k_qCYfqY",
                    "notice_content": null,
                    "openid": "oGuju60GOOc5Ier4mfB-3ZNMrimc",
                    "shift": 0,
                    "share_image": "https://cdn.hardtime.cn/icon/wx0c747e2a0ec57737_1.jpg",
                    "platformid": "wx",
                    "type": 0,
                    "notice_stopdate": null,
                    "token": "108d9db5c00e4c7f95970c7c2e11cc93",
                    "uid": 5940572,
                    "share_title": "上马不喊话 三开战猎萨！",
                    "phone": null,
                    "shift_url": "",
                    "nickname": "游客5940572",
                    "name": "",
                    "session_key": "2RixxMwPb19nuzpoUowrlA==",
                    "account": "2RixxMwPb19nuzpoUowrlA==",
                    "age": "",
                    "username": "游客5940572",
                    "register": 0
                },
                "result2": null
            })
        };
        clearInterval(sid);
    }, 50)
})();

const delay = async function (time) {
    return new Promise(resolve => setTimeout(() => {
        resolve(0)
    }, time))
};
// 监听SystemJs导出
const listenExport = async () => {
    return new Promise((resolve, reject) => {
        const sid = setInterval(() => {
            if (typeof System == 'undefined') return;
            if (typeof System.registerRegistry == 'undefined') return;
            const keys = Object.keys(System.registerRegistry);
            const key = keys.find(key => key.includes('AIManager.ts'));
            if (!key) return;
            clearInterval(sid);
            resolve(key);
        }, 50);
    });
};
const makeExport = async () => {
    window.clazz = {};
    return new Promise((resolve, reject) => {
        const sid = setInterval(() => {
            const symbols = Object.getOwnPropertySymbols(System);
            if (!symbols || !symbols.length) return;
            const value = System[symbols[0]];
            Object.keys(value).forEach(key => {
                const data = value[key];
                clazz[key] = data;
                if (!key.startsWith("chunks:///_virtual/")) return;
                if (!key.endsWith(".ts")) return;
                const name = key.replace("chunks:///_virtual/", "").replace(".ts", "");
                for (const val of Object.values(data)) {
                    if (!val || typeof val != 'object') continue;
                    if (val[Symbol.toStringTag] == "Module") {
                        const vTag = Object.keys(val);
                        vTag.forEach(tag => {
                            let objName = tag;
                            if (objName == "default") {
                                objName = name;
                            }
                            if (window[objName]) return;
                            Object.defineProperty(window, objName, {
                                value: val[tag],
                                writable: true,
                                configurable: false
                            });
                        })
                    }
                }

            });
            clearInterval(sid);
            resolve(true);
        }, 50);
    });
}
const isEnterGame = () => !!GlobalData.playerData.id;
const hookConsole = () => {
    if (DEBUG) return;
    console.log = function () { };
    console.error = function () { };
    console.warn = function () { };
    console.info = function () { };
    (function () {
        location.reload();
        debugger;
    })();
    eval("(function(){location.reload();debugger})();");
}
const hookNet = () => {
    const ignoreId = [111, 13, 14];
    const orgSendMsg = NetClient.sendMsg;
    NetClient.sendMsg = function () {
        if (typeof window.printNet != 'undefined' && printNet) {
            const arg = Array.from(arguments);
            if (!ignoreId.includes(arg[0])) {
                console.log("sendMsg", ...arg);
            }
        }
        orgSendMsg.call(NetClient, ...arguments);
    }
    const sendMsgCallBack = NetClient.sendMsgCallBack;
    NetClient.sendMsgCallBack = function () {
        if (typeof window.printNet != 'undefined' && printNet) {
            const arg = Array.from(arguments);
            if (!ignoreId.includes(arg[0])) {
                console.log("sendMsgCallBack", ...arg);
            }
        }
        sendMsgCallBack.call(NetClient, ...arguments);
    }
}
function random(min, max) {
    if (max === undefined) {
        max = min;
        min = 0;
    }
    if (min >= max) {
        return min;
    }
    return Math.floor(Math.random() * (Math.max(max - min, 0) + 1)) + min;
}
window.random = random;
// 副本自动准备
window.fbAutoReady = false;
const hookAutoReady = () => {
    const orgFunc = FubenEnterDlg.prototype.onStartTime
    FubenEnterDlg.prototype.onStartTime = function (t) {
        if (window.fbAutoReady) {
            return void this.onReady();
        }
        orgFunc.call(this, t);
    }
}
// 副本界面相关
const hookFbView = () => {
    // 大秘境更新杀怪数消息
    const orgUpdateMiJingKillCOunt = FubenView.prototype.onUpdateMiJingKillCOunt;
    FubenView.prototype.onUpdateMiJingKillCOunt = function (data) {
        orgUpdateMiJingKillCOunt.call(this, data);
        mj.bossCnt = data.it1;
        mj.monsterCnt = data.it2;
    }
}
const updateCall = [];
const init = async () => {
    await listenExport();
    await makeExport();
    if (!GlobalData) {
        return void parent.$message.error("环境初始化失败,游戏无法启动!", { duration: 10000 });
    }
    hookConsole();
    hookNet();
    const orgUpdate = Main.prototype.update;
    Main.prototype.update = function (dt) {
        orgUpdate.call(this, dt);
        updateCall.forEach(({ cb, obj }) => cb.call(obj, dt));
    };
    hookAutoReady();
    hookFbView();
    window.mj = mj.init();
    window.zc = zc.init();
};

var mj = {
    run: false,
    current: null,
    bossCnt: 0,
    monsterCnt: 0,
    loopResolve: null,
    init: function () {
        this.run = false;
        updateCall.push({ cb: this.update, obj: this });
        return this;
    },
    start: async function () {
        this.run = true;
        window.fbAutoReady = true;
        this.bossCnt = 0;
        this.monsterCnt = 0;
        this.loopResolve = null;
        this.logic();
        return true;
    },

    stop: function () {
        this.run = false;
        this.current = null;
    },
    update(dt) {
        if (!this.loopResolve) return;
        if (this.isBossDone() && this.isMonsterDone()) {
            return void this.loopResolve();
        }
        // 需要优先杀小怪 然后再是boss 虽然会浪费时间 但是可以保证条件达成和存活率
        if (!this.isMonsterDone()) {
            
        }

        if (!this.isBossDone()) {

        }
    },
    async logic() {
        if (!this.run) return;

        // 加载数据
        const fb = await this.loadInfo();
        if (!fb) return this.stop();
        this.current = fb;
        // 创建房间
        if (!await this.createRoom()) {
            return this.stop();
        }
        // 检查房间创建 点击开始
        if (!await this.checkRoom()) {
            parent.$message.error("房间检测失败,结束!", { duration: 10000 });
            return this.stop();
        }
        // 点击开始
        if (!await this.enterFbMap()) {
            parent.$message.error("进入地图等待超时,结束!", { duration: 10000 });
            return this.stop();
        }
        // 等待fubenView界面
        await this.readyFbView();
        // 主要逻辑
        await new Promise(resolve => {
            this.loopResolve = resolve;
        })
    },
    getFbView() {
        return MainUI.instance._subViewMap.get(DialogName.FubenView);
    },
    isBossDone() {
        const cfg = this.current.cfg.boss.length;
        return this.bossCnt >= cfg;
    },
    isMonsterDone() {
        return this.monsterCnt >= this.current.cfg.monsterCount;
    },
    async readyFbView() {
        while (this.getFbView() == null) {
            await delay(100);
        }
    },
    async enterFbMap() {
        const mapId = FubenManager.getInstance().myRoomData.mapId;
        const roomId = FubenManager.getInstance().myRoomData.roomId;
        NetClient.sendMsg(MsgId.C2S_797, { mapId, roomId })

        let maxWaitCnt = 600;
        while (MapManager._instance.CurMapId != mapId && maxWaitCnt > 0) {
            await delay(100);
            maxWaitCnt--;
        }
        return maxWaitCnt > 0;
    },
    async checkRoom() {
        const dlg = LayerManager.instance.getDlgInfo(DialogName.FubenRoomDlg);
        let maxWaitCnt = 100;
        while (dlg.state != DialogState.Opened && maxWaitCnt > 0) {
            await delay(100);
            maxWaitCnt--;
        }
        return maxWaitCnt > 0;
    },
    async createRoom() {
        if (FubenManager.getInstance().myRoomData) {
            parent.$message.error("请先解散现有房间再开始!", { duration: 5000 });
            return false;
        }
        // 检查门票数量
        let need = Number(this.current.cfg.tickets[1]);
        const itemCfg = ItemManager.getItemConfigById("********");
        const itemInfo = ItemManager.getItemInfoById(itemCfg.tempId, itemCfg.packageType);
        if (!itemInfo || itemInfo.count < need) {
            parent.$message.error("门票不足,结束!", { duration: 5000 });
            return false;
        }
        const req = {
            allocationType: 4,
            ceng: 5,
            isChallenge: 0,
            join: 2,
            mapId: this.current.id,
            pwd: "" + random(1001, 9999),
            teamMode: 0,
            zhanli: 1
        }
        const { data, code } = await new Promise(resolve => {
            NetClient.sendMsgCallBack(MsgId.C2S_782, req, (data, code) => resolve({ data, code }))
        })
        if (code != 0) {
            this.stop()
            return void parent.$message.error("创建房间失败,结束!", { duration: 10000 });
        }
        FubenManager.getInstance().myRoomData = data;
        FubenManager.getInstance().myRoomData.isDamijing = true;
        EventCenter.instance.emit(EventType.FB_ROOM, 1);
        FubenManager.getInstance().addFloating();
        LayerManager.instance.showDlg(DialogName.FubenRoomDlg);
        return true
    },
    async loadInfo() {
        const { data, code } = await new Promise(resolve => {
            NetClient.sendMsgCallBack(MsgId.C2S_781, null, (data, code) => resolve({ data, code }))
        })
        const result = {
            unGet: 0,
            list: []
        }
        if (code != 0) return void parent.$message.error("数据获取失败,无法开始!", { duration: 5000 });
        let base = FubenManager.getMiJingConf("mijingbiMax");
        let rate = GlobalData.playerData.isMysteryDiantang ? 2 : 1;
        base *= rate;
        let get = data.miJingBiCount;
        base += data.lastWeekMJBCount;
        result.unGet = base - get;

        const fubenCfg = ConfigManager.getAllData(DataFileName.FubenConfig);
        const opened = data.damijinglist[0].list.filter(l => l.intValue == 1).map(l => {
            return {
                id: l.strValue,
                cfg: fubenCfg.find(f => f.id == l.strValue),
                floor: l.intValue2,
                challenge: data.dmjChallengelist[0].list.find(c => c.strValue == l.strValue).intValue2,
                double: "0" == l.strValue2,
            }
        });
        if (opened.length == 0) return void parent.$message.error("数据0 ?无法开始!", { duration: 5000 });
        result.list = opened.sort((a, b) => {
            if (a.double == b.double) {
                return Number(a.cfg.tickets[1]) - Number(b.cfg.tickets[1]);
            }
            return a.double ? -1 : 1;
        })
        return result.list.shift();
    },

};

var zc = {
    strongholdStr: [{ key: "redFarm", name: "红方农场" }, { key: "blueFarm", name: "蓝方农场" }, { key: "smithy", name: "铁匠铺" }, { key: "logging", name: "伐木场" }, { key: "mine", name: "矿洞" }],
    red: 0,
    blue: 0,
    time: 0,
    lastTeamDataReqTime: 0,
    teamData: null,
    isRed: false,

    proxy: null,
    moveToTarget: null,
    run: false,

    init: function () {
        this.run = false;
        const proxy = EventProxy.create();
        proxy.addEvent(MsgId.S2C_1156, this.onGetInfo, this);
        proxy.addEvent(MsgId.S2C_1155, this.onGetTeamInfo, this);
        proxy.addEvent(EventType.AUTOMOVE_END, this.onReachTarget, this);

        updateCall.push({ cb: this.update, obj: this });
        this.proxy = proxy;
        return this;
    },
    start: function () { this.run = true; },
    stop: function () { this.run = false; },
    update(dt) {
        if (!this.run) return;
        if (MapManager._instance.CurMapId != "fmxzc10001") return;
        if (this.time <= 0) return
        this.time -= dt * 1000;
        if (GlobalData.player.isDead()) {
            return;
        }
        let changeTarget = false;
        // 不存在 或者已经被占领，重找目标
        if (this.moveToTarget == null || this.isBelongToMine(this.moveToTarget)) {
            const hold = this.getNotBelongMine()
            if (!hold) return;
            changeTarget = true;
            this.moveToTarget = hold;
        }
        // 目标改变 或者 没有在移动 就继续寻路过去
        if (changeTarget || !GlobalData.player.mAutoMove) {
            const cfg = ConfigManager.getData(DataFileName.moxiZhanChangInfoData, "mxzc");
            const key = this.moveToTarget.key + "Flag"
            const x = Number(cfg[key][0]);
            const y = Number(cfg[key][1]);
            this.moveToTarget.x = x;
            this.moveToTarget.y = y;

            GlobalData.playerData.isFubenAutoFight = false;
            PlayerControl.getInstance().startAutoMove("fmxzc10001", x, y, AUTOMOVE_TYPE.move_to_pos);
        }
    },
    // 到达目标后
    onReachTarget: function (x, y, flag) {
        if (x != this.moveToTarget.x && y != this.moveToTarget.y) return;
        // 执行采集
        const thing = ThingManager.instance.getThingAtPos(x, y, ThingType.THING_COLLECT);
        if (!thing || !thing.length) {
            return;
        }
        const target = thing[0];
        if (Singleton.getInstance().isCollecting || Singleton.getInstance().isReqCollect) {
            return;
        }
        if (SkillControl.isCollectThing(target.id)) {

        }
        SkillControl.checkDoCollect(target.id);
    },
    // 判断建筑属不属于我方
    isBelongToMine: function (hold) {
        return hold.belong == (this.isRed ? 1 : 2);
    },
    // 获取不属于我方的建筑
    getNotBelongMine: function () {
        const ary = this.strongholdStr.filter(h => !this.isBelongToMine(h));
        return ary.pop();
    },
    onGetInfo: function (code, data) {
        if (code != 0) return
        for (const hold of this.strongholdStr) {
            const { key, name } = hold;
            hold.belong = data[key];
        }
        this.red = data.score.it1;
        this.blue = data.score.it2;
        this.time = Number(data.leftime);
        if (!this.teamData && this.needReqTeamData()) {
            NetClient.sendMsg(MsgId.C2S_1155);
        }
    },
    // 需不需要请求队伍数据
    needReqTeamData: function () {
        const now = Date.now();
        if (now - this.lastTeamDataReqTime < 3000) return false;
        this.lastTeamDataReqTime = now;
        return true;
    },
    onGetTeamInfo: function (code, data) {
        if (code != 0) return
        this.teamData = [];
        for (const { teamData, type, isExpanded } of data.battlefieldInfoList) {
            for (const unit of teamData) {
                if (unit.playerInfo.playerMsg.id == GlobalData.playerData.id) {
                    this.isRed = type == 1;
                }
            }
            this.teamData.push({
                teamData,
                type,
                isExpanded,
            })
        }
    },

}


init();