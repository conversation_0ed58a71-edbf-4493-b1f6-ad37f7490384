import type Handler from "@/core/Handler";

export default class Evt {
    public static evts: { [key: string]: Handler[] } = {}

    /**
     * 注册事件监听
     * @param key
     * @param call
     * @param single 是否只监听一次
     */
    public static on(key: string, call: Handler, single: boolean = false) {
        const list = this.evts[key] || []
        if (single) {
            list.length = 0
        }
        list.push(call)
        this.evts[key] = list
    }

    public static emit(key: string, ...args: any[]) {
        const list = this.evts[key]
        if (list) {
            list.forEach(l => l.exec(args))
        }
    }

    public static off(key: string) {
        delete this.evts[key]
    }
}
//@ts-ignore
window.Evt = Evt
