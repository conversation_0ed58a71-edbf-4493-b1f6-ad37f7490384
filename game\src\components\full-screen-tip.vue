<template>
  <div id="full">
    <slot id="content" name="content"></slot>
  </div>
</template>
<script setup lang="ts">

interface Props {

}

const props = withDefaults(defineProps<Props>(), {})

onMounted(() => {

})

</script>
<style scoped>
#full {
  height: 100vh;
  width: 100vw;
  position: absolute;
  left: 0;
  top: 0;
  z-index: 19;
  backdrop-filter: blur(1px);
  -webkit-backdrop-filter: blur(1px);
  background-color: rgba(33, 32, 32, 0.5);
  box-shadow: rgba(0, 0, 0, 0.3) 8px 8px 8px 8px;
  border: 0 rgba(255, 255, 255, 0.4) solid;
  border-bottom: 0 rgba(40, 40, 40, 0.35) solid;
  border-right: 0 rgba(40, 40, 40, 0.35) solid;
  display: flex;
  justify-content: center;
  align-items: center;
}

</style>
