<script lang="ts" setup>

import {onMounted, type Ref, ref} from "vue";
import {useTipStore} from "@/stores/tip";
import {useUserStore} from "@/stores/user";


interface Props {
  addEvent: () => void,
  myEvent: () => void,
  setEvent: () => void,
}

const props = withDefaults(defineProps<Props>(), {})

const tip = useTipStore()
const user = useUserStore()
const dv = ref<any>(null)

onMounted(() => {
  document.addEventListener('dragover', (e) => e.preventDefault())
})

const click = (e: any) => {

}

let offsetX = 0
let offsetY = 0
const start = (e: any) => {
  offsetX = e.clientX - dv.value.offsetLeft;
  offsetY = e.clientY - dv.value.offsetTop;
}
const move = (e: any) => {
  dv.value.style.display = "none"
  let x = e.clientX - offsetX
  let y = e.clientY - offsetY
  x = Math.min(document.documentElement.clientWidth - 28, x)
  y = Math.min(document.documentElement.clientHeight - 28, y)
  x = Math.max(0, x)
  y = Math.max(0, y)
  tip.move(x, y)
}

const end = (e: any) => {
  dv.value.style.display = ""
}

const help = () => window.$message.info('敬请期待')
const update = () => {
  if (!user.checkForceUpdate()) {
    window.$message.info('暂无更新~')
  }
}

</script>

<template>
  <div id="main" class="navbar" :style="{left: `${tip.x}px`, top: `${tip.y}px`}" ref="dv" draggable="true"
       @dragstart="start"
       @dragend="end"
       @drag="move">
    <input type="checkbox" @click="click" v-bind:checked="tip.active" @change="tip.changeActive">
    <span></span>
    <span></span>
    <ul>
      <li><a class="" @click="props.addEvent">添加</a></li>
      <li><a class="" @click="props.myEvent">我的</a></li>
      <li><a class="" @click="props.setEvent">设置</a></li>
      <li><a class="" @click="help">帮助</a></li>
      <li><a class="" @click="update">更新</a></li>
    </ul>
  </div>
</template>


<style scoped>

#main {
  position: fixed;
  z-index: 995;
  border-radius: 8px;
  backdrop-filter: blur(16px);
  -webkit-backdrop-filter: blur(16px);
  background-color: var(--button-group-hover-color);
  box-shadow: rgba(0, 0, 0, 0.3) 8px 8px 8px 8px;
  border: 0 rgba(255, 255, 255, 0.4) solid;
  border-bottom: 0 rgba(40, 40, 40, 0.35) solid;
  border-right: 0 rgba(40, 40, 40, 0.35) solid;
}

.navbar {
  display: flex;
  justify-content: center;
  align-items: center;
}

ul {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0 !important;
}

.navbar {
  /* 相对定位 */
  position: relative;
  padding: 4px;
  border-radius: 50px;
}

.navbar input {
  width: 22px;
  height: 22px;
  /* 不透明度设置为0，直接变透明 */
  opacity: 0;
  /* 鼠标变小手 */
  cursor: pointer;
}

.navbar span {
  /* 绝对定位 */
  position: absolute;
  left: 7px;
  /* 现将两条线都放到一起，然后单独设置第二条线的位置，calc方法可以自动计算数值 */
  top: calc(50% - 6px);
  width: 15px;
  height: 4px;
  border-radius: 15px;
  background-color: #4cd137;
  /* 现在span覆盖着复选框，鼠标放到span上是点不中复选框的，这个属性就能解决这个问题，即便鼠标放到span上点击也能选中或取消选中复选框 */
  pointer-events: none;
  /* 收回来的时候过渡刚好是相反的 */
  transition: transform 0.3s ease-in-out, top 0.3s ease-in-out 0.3s;
}

/* 因为第二条线在navbar这个元素内的第三个位置，所这里写3 */
.navbar span:nth-child(3) {
  top: calc(50% + 2px);
}

.navbar ul {
  width: 0;
  /* 溢出隐藏 */
  overflow: hidden;
  /* 现在展开和收起速度太快了，加个过渡 */
  transition: all 0.5s;
  /* 现在导航栏收起的时候，这个圆是扁的 */
  /* 这个问题是因为文字被换行显示了，两个汉字竖着排列了，所以盒子被撑大了，下面这个属性就可以解决这个问题，让文字不换行显示 */
  white-space: nowrap;
}

.navbar ul li {
  list-style: none;
  margin: 0 6px;
}

.navbar ul li a {
  /* 取消下划线 */
  text-decoration: none;
  font-size: 14px;
  font-weight: 700;
  cursor: pointer;
  transition-property: color;
  transition-duration: .3s;
}

.navbar ul li a:hover {
  color: #fb7299;
}

/* :checked是当复选框被选中的时候，~是兄弟选择器，查找同一级的ul */
.navbar input:checked ~ ul {
  width: 200px;
}

.navbar input:checked ~ span:nth-child(2) {
  top: calc(50% - 2px);
  transform: rotate(-45deg);
  background-color: #fb7299;
  /* 这里先执行top，然后0.3秒后执行transform */
  transition: top 0.3s ease-in-out, transform 0.3s ease-in-out 0.3s;
}

.navbar input:checked ~ span:nth-child(3) {
  top: calc(50% - 2px);
  transform: rotate(45deg);
  background-color: #fb7299;
  transition: top 0.3s ease-in-out, transform 0.3s ease-in-out 0.3s;
}


</style>
