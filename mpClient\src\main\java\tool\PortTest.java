package tool;

import java.io.IOException;
import java.net.InetSocketAddress;
import java.net.Socket;

public class PortTest {

    private final static String DEFAULT_HOST = "localhost";


    public static boolean isHostConnectable(int port) {
        return isHostConnectable(DEFAULT_HOST, port);
    }

    public static boolean isHostConnectable(String host, int port) {
        Socket socket = new Socket();
        try {
            socket.connect(new InetSocketAddress(host, port));
        } catch (IOException e) {
            return false;
        } finally {
            try {
                socket.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return true;
    }
}
