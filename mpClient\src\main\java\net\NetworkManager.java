package net;

import env.Global;
import javafx.application.Platform;
import javafx.scene.control.Alert;
import net.client.Client;
import net.server.Server;
import tool.PortTest;

/**
 * 同端网络控制器
 *
 * <AUTHOR>
 */
public class NetworkManager {

    private static final Object lock = new Object();

    private Server server;
    private boolean isServer;
    private Client client;
    private boolean isClient;

    private static final class NetworkManagerHolder {
        static final NetworkManager networkManager = new NetworkManager();
    }

    public static NetworkManager getMe() {
        return NetworkManagerHolder.networkManager;
    }


    public boolean isNettyServerRunning() {
        return PortTest.isHostConnectable(Server.SERVER_HOST, Server.getPort());
    }

    public void startAsServer() {
        if (isNettyServerRunning()) {
            System.out.println("重复启动server");
            return;
        }
        //System.out.println("作为服务器启动.");
        this.server = new Server();
        server.run();
        this.isServer = true;
    }

    public void startAsClient() {
        if (!isNettyServerRunning()) {
            System.out.println("server未被启动");
            return;
        }
        //System.out.println("作为客户端启动.");
        this.client = new Client();
        this.isClient = true;
        client.run();
    }


    public void doNetworkCmd(int code, String msg) {
        //System.out.println("code:" + code + ",msg:" + msg);
        switch (code) {
            case 101:
                Platform.runLater(() -> {
                    Global.dashboard.show();
                    Global.dashboard.stage.setAlwaysOnTop(true);
                    Global.dashboard.stage.setAlwaysOnTop(false);
                });
                break;
        }
    }

    public Server getServer() {
        return server;
    }

    public boolean isServer() {
        return isServer;
    }

    public Client getClient() {
        return client;
    }

    public boolean isClient() {
        return isClient;
    }
}
