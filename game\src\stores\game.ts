import {ref, computed} from 'vue'
import type {Ref} from 'vue'
import {defineStore} from 'pinia'
import {GameMsg} from "@/core/z";
import type {FunctionType} from "@/core/z";
import Evt from "@/core/evt";

export const useGameStore = defineStore('game', () => {
    const init = ref(false)
    const inject = ref(false)

    const key = ref<any[]>()

    function setInjectSuccess() {
        inject.value = true
    }

    function onMsg(code: GameMsg) {
        console.log('onMsg', code);
        switch (code) {
            case GameMsg.LOGIC_INJECT_DONE:
                setInjectSuccess();
                break;
        }
    }

    // 从dashboard获取快捷键数据
    function initQuickKey() {
        if (key.value) return
        const keyData = window.DASHBOARD_EXEC("localStorage.getItem('_dashboard_keys')") || ""
        if (!keyData) return
        key.value = JSON.parse(keyData) as any[]
    }

    function getKey(keyCode: string) {
        //@ts-ignore
        return key.value.find((key) => key.name == keyCode)
    }

    window.ON_UNKNOWN_KEY = (keyCode: string) => {
        initQuickKey()
        const target = keyCode.replace("KEY_CODE_", "")
        const data = getKey(target)
        if (!data) return
        switch (data.sort) {
            case 4:
                Evt.emit('EVT_OPEN_MAP_TOOL')
                break;
            case 6:
                Evt.emit('EVT_BACK_TO_CITY')
                break;
        }
    }

    const funcs: Ref<FunctionType[]> = ref<FunctionType[]>([])

    return {inject, onMsg, funcs}
})
