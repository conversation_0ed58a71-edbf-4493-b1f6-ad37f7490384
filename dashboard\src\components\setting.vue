<template>
  <div style="width: 96%">
    <div>
      <div style="margin-left: 20px;line-height: 32px;display: flex;align-items: center;">
        <n-text> 主题跟随系统</n-text>
        &nbsp;&nbsp;
        <n-switch v-model:value="user.themeAuto" size="large" :on-update:value="user.onThemeAuto">
          <template #checked-icon>
            <n-icon>
              <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 24 24">
                <path fill="currentColor"
                      d="M12 22c5.523 0 10-4.477 10-10S17.523 2 12 2S2 6.477 2 12s4.477 10 10 10m0-1.5v-17a8.5 8.5 0 0 1 0 17"/>
              </svg>
            </n-icon>
          </template>
          <template #unchecked-icon>
            <n-icon>
              <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 24 24">
                <path fill="currentColor"
                      d="M12 22c5.523 0 10-4.477 10-10S17.523 2 12 2S2 6.477 2 12s4.477 10 10 10m0-1.5v-17a8.5 8.5 0 0 1 0 17"/>
              </svg>
            </n-icon>
          </template>
        </n-switch>
      </div>
      <div style="margin-left: 20px;line-height: 32px;display: flex;align-items: center;" v-if="!user.themeAuto">
        <n-switch v-model:value="user.themeType" size="large" :on-update:value="user.onThemeType">
          <template #checked-icon>
            <n-icon>
              <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 24 24">
                <g fill="none" stroke="currentColor" stroke-width="2">
                  <circle cx="12" cy="12" r="4" stroke-linejoin="round"/>
                  <path stroke-linecap="round"
                        d="M20 12h1M3 12h1m8 8v1m0-18v1m5.657 13.657l.707.707M5.636 5.636l.707.707m0 11.314l-.707.707M18.364 5.636l-.707.707"/>
                </g>
              </svg>
            </n-icon>
          </template>
          <template #checked>
            亮色主题
          </template>
          <template #unchecked-icon>
            <n-icon>
              <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 48 48">
                <defs>
                  <mask id="ipSDarkMode0">
                    <g fill="none" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10"
                       stroke-width="4">
                      <path fill="#fff" stroke="#fff"
                            d="m24.003 4l5.27 5.27h9.457v9.456l5.27 5.27l-5.27 5.278v9.456h-9.456L24.004 44l-5.278-5.27H9.27v-9.456L4 23.997l5.27-5.27V9.27h9.456z"/>
                      <path fill="#000" stroke="#000" d="M27 17c0 8-5 9-10 9c0 4 6.5 8 12 4s2-13-2-13"/>
                    </g>
                  </mask>
                </defs>
                <path fill="currentColor" d="M0 0h48v48H0z" mask="url(#ipSDarkMode0)"/>
              </svg>
            </n-icon>
          </template>
          <template #unchecked>
            暗色主题
          </template>
        </n-switch>
      </div>
    </div>

    <n-grid x-gap="2" y-gap="6" :cols="24">
      <n-gi v-for="(item,index) in key.keyData" :span="index % 2== 0 ? 8 : 4"
            :key="(index % 2== 0 ? 'text' : 'btn') + '_tag' + index">
        <n-text v-if="index % 2== 0">{{ item.name }}</n-text>
        <n-tooltip trigger="hover" v-else>
          <template #trigger>
            <n-button size="tiny" :loading="!!item['editing']" :type="!!item['editing'] ? 'success' : 'error'" secondary
                      @click="changeKey(item)">
              {{ item.name }}
            </n-button>
          </template>
          <div>
            {{ item.tip }}
            <div v-for="(channel, idx) in channels" style="display: flex">
              <div :style="{ color : item.status[idx] ? '#2ed573' : '#ea2027', lineHeight: '24px'}">
                <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 24 24">
                  <path fill="currentColor" d="M12 7a5 5 0 1 1-4.995 5.217L7 12l.005-.217A5 5 0 0 1 12 7"/>
                </svg>
              </div>
              <div style="font-size: 12px">{{ channel.name }}</div>
            </div>
          </div>
        </n-tooltip>
      </n-gi>
    </n-grid>

  </div>
</template>
<script setup lang="ts">

import {useKeyStore} from "@/stores/keys";
import type {Key} from "@/core/z";
import {Channels} from "@/core/z";
import {useUserStore} from "@/stores/user";

const key = useKeyStore()
const user = useUserStore()

const channels = computed(() => Channels.filter(item => !!item.url))


const changeKey = (item: Key) => {
  if (key.listenerInputKey) {
    return void window.$message.error('请先完成上一个按键的修改')
  }
  if (!item.modify) return void window.$message.error('该按键不支持修改')

  key.listenerInputKey = true
  key.changeKeyEvent = (targetKey: string) => {
    item.name = targetKey
    delete item['editing']
  }

  window.$message.warning(`请按下要替换的按键`)
  item['editing'] = true
}

onBeforeUnmount(() => {
  key.listenerInputKey = false
  key.changeKeyEvent = null
})

</script>
<style scoped>

</style>
