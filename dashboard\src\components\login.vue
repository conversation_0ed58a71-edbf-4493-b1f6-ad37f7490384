<script setup lang="ts">

import type {FormItemRule} from "naive-ui";
import {useUserStore} from "@/stores/user";

const user = useUserStore()
const model = user.model

const rules = {
  username: [
    {
      required: true,
      validator(rule: FormItemRule, value: string) {
        if (!value) {
          return new Error('请输入用户名')
        } else if (!/^[a-zA-Z0-9]+$/.test(value)) {
          return new Error('只能由数字大小写字母组成')
        } else if (value.length < 4) {
          return new Error('用户名长度需要大于4')
        }
        return true
      },
      trigger: ['input', 'blur']
    }
  ],
  password: [
    {
      required: true,
      validator(rule: FormItemRule, value: string) {
        if (!value) {
          return new Error('请输入密码')
        } else if (!/^[a-zA-Z0-9]+$/.test(value)) {
          return new Error('只能由数字大小写字母组成')
        } else if (value.length < 6) {
          return new Error('密码长度需要大于6')
        }
        return true
      },
      trigger: ['input', 'blur']
    }
  ]
}

onMounted(() => {
  model.username = localStorage.getItem("_dashboard_username") || ""
  model.password = localStorage.getItem("_dashboard_password") || ""
  model.token = localStorage.getItem("_dashboard_token") || ""

  if (model.token) {
    user.loginByToken()
  }
})

const handleRegister = () => {
  user.isRegister = true
  user.isLogin = false
}

const handleForget = () => {
  window.$message.error("稍后开放")
}

</script>

<template>

  <div id="login-panel">
    <n-form
        style="width: 80%;"
        :model="model"
        :rules="rules"
        label-placement="left"
        :label-width="80"
    >
      <n-grid :cols="24" :x-gap="1">
        <n-form-item-gi :span="24" label="用户名" path="username">
          <n-input v-model:value="model.username" size="tiny" clearable
                   placeholder="请输入用户名" style="width: 95%"/>
        </n-form-item-gi>
        <n-form-item-gi :span="24" label="密码" path="password">
          <n-input v-model:value="model.password" size="tiny" clearable show-password-on="mousedown"
                   placeholder="请输入密码" style="width: 95%" type="password"/>
        </n-form-item-gi>
        <n-form-item-gi :span="24" label="" path="">
          <div style="width: 100%">
            <n-button style="width: calc(100% - 12px);margin-left: 6px;"
                      :loading="user.loginLoading"
                      @click="user.loginRequest"
                      size="small"
                      type="success">登录
            </n-button>
            <div style="display: flex;justify-content: flex-end;">
              <n-text class="btn-a" @click="handleRegister">注册</n-text>
              <n-text class="btn-a" @click="handleForget">忘记密码</n-text>
            </div>
          </div>

        </n-form-item-gi>
      </n-grid>


    </n-form>
  </div>

</template>

<style scoped>

#login-panel {
  position: fixed;
  left: 0;
  top: 0;
  bottom: 0;
  right: 0;
  height: 100vh;
  width: 100vw;
  z-index: 996;
  backdrop-filter: blur(16px);
  -webkit-backdrop-filter: blur(16px);
  box-shadow: rgba(0, 0, 0, 0.3) 8px 8px 8px 8px;
  border: 0 rgba(255, 255, 255, 0.4) solid;
  border-bottom: 0 rgba(40, 40, 40, 0.35) solid;
  border-right: 0 rgba(40, 40, 40, 0.35) solid;
  background-color: var(--dialog-bg-color, rgba(255, 255, 255, 0.5));
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
}

.btn-a {
  font-size: 12px;
  text-decoration: underline;
  cursor: pointer;
  margin-right: 12px;
}

.btn-a:hover {
  color: #2ecc71;
}

</style>
