/**
 * 工具函数模块
 * 提供文件操作、目录管理等通用功能
 */

const fs = require('fs');
const path = require('path');

/**
 * 确保目录存在，如果不存在则创建
 * @param {string} dirPath - 目录路径
 */
async function ensureDir(dirPath) {
    try {
        await fs.promises.access(dirPath);
    } catch (error) {
        if (error.code === 'ENOENT') {
            await fs.promises.mkdir(dirPath, { recursive: true });
        } else {
            throw error;
        }
    }
}

/**
 * 在指定目录中查找所有 .wxapkg 文件
 * @param {string} dirPath - 目录路径
 * @returns {string[]} - 找到的 .wxapkg 文件路径数组
 */
function findWxapkgFiles(dirPath) {
    const files = [];
    
    function scanDirectory(currentPath) {
        const items = fs.readdirSync(currentPath);
        
        for (const item of items) {
            const fullPath = path.join(currentPath, item);
            const stat = fs.statSync(fullPath);
            
            if (stat.isFile() && path.extname(item) === '.wxapkg') {
                files.push(fullPath);
            } else if (stat.isDirectory()) {
                // 递归扫描子目录
                scanDirectory(fullPath);
            }
        }
    }
    
    scanDirectory(dirPath);
    return files;
}

/**
 * 读取文件内容为 Buffer
 * @param {string} filePath - 文件路径
 * @returns {Promise<Buffer>} - 文件内容
 */
async function readFileBuffer(filePath) {
    try {
        return await fs.promises.readFile(filePath);
    } catch (error) {
        throw new Error(`读取文件失败 ${filePath}: ${error.message}`);
    }
}

/**
 * 写入 Buffer 到文件
 * @param {string} filePath - 文件路径
 * @param {Buffer} buffer - 要写入的数据
 */
async function writeFileBuffer(filePath, buffer) {
    try {
        // 确保目录存在
        await ensureDir(path.dirname(filePath));
        await fs.promises.writeFile(filePath, buffer);
    } catch (error) {
        throw new Error(`写入文件失败 ${filePath}: ${error.message}`);
    }
}

/**
 * 删除文件（如果存在）
 * @param {string} filePath - 文件路径
 */
async function deleteFile(filePath) {
    try {
        await fs.promises.unlink(filePath);
    } catch (error) {
        if (error.code !== 'ENOENT') {
            throw new Error(`删除文件失败 ${filePath}: ${error.message}`);
        }
    }
}

/**
 * 删除目录及其内容
 * @param {string} dirPath - 目录路径
 */
async function deleteDirectory(dirPath) {
    try {
        await fs.promises.rm(dirPath, { recursive: true, force: true });
    } catch (error) {
        throw new Error(`删除目录失败 ${dirPath}: ${error.message}`);
    }
}

/**
 * 检查文件是否存在
 * @param {string} filePath - 文件路径
 * @returns {boolean} - 文件是否存在
 */
function fileExists(filePath) {
    try {
        fs.accessSync(filePath);
        return true;
    } catch {
        return false;
    }
}

/**
 * 获取文件大小
 * @param {string} filePath - 文件路径
 * @returns {number} - 文件大小（字节）
 */
function getFileSize(filePath) {
    try {
        const stat = fs.statSync(filePath);
        return stat.size;
    } catch (error) {
        throw new Error(`获取文件大小失败 ${filePath}: ${error.message}`);
    }
}

/**
 * 格式化文件大小为可读字符串
 * @param {number} bytes - 字节数
 * @returns {string} - 格式化后的大小字符串
 */
function formatFileSize(bytes) {
    const units = ['B', 'KB', 'MB', 'GB'];
    let size = bytes;
    let unitIndex = 0;
    
    while (size >= 1024 && unitIndex < units.length - 1) {
        size /= 1024;
        unitIndex++;
    }
    
    return `${size.toFixed(1)} ${units[unitIndex]}`;
}

module.exports = {
    ensureDir,
    findWxapkgFiles,
    readFileBuffer,
    writeFileBuffer,
    deleteFile,
    deleteDirectory,
    fileExists,
    getFileSize,
    formatFileSize
};
