/**
 * 微信小程序包解压缩模块
 * 基于 wxappUnpacker 项目的解压缩逻辑
 */

const fs = require('fs');
const path = require('path');
const utils = require('./utils');
const formatter = require('./formatter');

/**
 * 解析 wxapkg 文件头信息
 * @param {Buffer} buffer - 文件头数据（前14字节）
 * @returns {Object} - 包含文件列表长度和数据长度的对象
 */
function parseHeader(buffer) {
    if (buffer.length < 14) {
        throw new Error('文件头数据不完整');
    }
    
    const firstMark = buffer.readUInt8(0);
    const unknownInfo = buffer.readUInt32BE(1);
    const infoListLength = buffer.readUInt32BE(5);
    const dataLength = buffer.readUInt32BE(9);
    const lastMark = buffer.readUInt8(13);
    
    // 验证魔数
    if (firstMark !== 0xbe || lastMark !== 0xed) {
        throw new Error(`文件格式不正确，魔数验证失败: 0x${firstMark.toString(16)}, 0x${lastMark.toString(16)}`);
    }
    
    return {
        firstMark,
        unknownInfo,
        infoListLength,
        dataLength,
        lastMark
    };
}

/**
 * 解析文件列表信息
 * @param {Buffer} buffer - 文件列表数据
 * @returns {Array} - 文件信息数组
 */
function parseFileList(buffer) {
    const fileCount = buffer.readUInt32BE(0);
    const fileInfo = [];
    let offset = 4;
    
    for (let i = 0; i < fileCount; i++) {
        const info = {};
        
        // 读取文件名长度
        const nameLength = buffer.readUInt32BE(offset);
        offset += 4;
        
        // 读取文件名
        info.name = buffer.toString('utf8', offset, offset + nameLength);
        offset += nameLength;
        
        // 读取文件偏移量
        info.offset = buffer.readUInt32BE(offset);
        offset += 4;
        
        // 读取文件大小
        info.size = buffer.readUInt32BE(offset);
        offset += 4;
        
        fileInfo.push(info);
    }
    
    return fileInfo;
}

/**
 * 提取并保存文件
 * @param {string} outputDir - 输出目录
 * @param {Buffer} dataBuffer - 包含所有文件数据的缓冲区
 * @param {Array} fileList - 文件信息列表
 */
async function extractFiles(outputDir, dataBuffer, fileList) {
    console.log(`开始提取 ${fileList.length} 个文件...`);
    
    for (const fileInfo of fileList) {
        try {
            // 处理文件路径，确保以相对路径保存
            let filePath = fileInfo.name;
            if (filePath.startsWith('/')) {
                filePath = '.' + filePath;
            }
            
            const fullPath = path.resolve(outputDir, filePath);
            
            // 提取文件数据
            const fileData = dataBuffer.slice(fileInfo.offset, fileInfo.offset + fileInfo.size);
            
            // 保存文件
            await utils.writeFileBuffer(fullPath, fileData);
            
        } catch (error) {
            console.error(`提取文件失败 ${fileInfo.name}: ${error.message}`);
        }
    }
}

/**
 * 检查是否为小程序主包
 * @param {string} dir - 解压后的目录
 * @returns {boolean} - 是否为主包
 */
function isMainPackage(dir) {
    return fs.existsSync(path.resolve(dir, 'app-service.js'));
}

/**
 * 检查是否为小游戏包
 * @param {string} dir - 解压后的目录
 * @returns {boolean} - 是否为小游戏包
 */
function isGamePackage(dir) {
    return fs.existsSync(path.resolve(dir, 'game.js'));
}

/**
 * 处理小程序主包的特殊文件
 * @param {string} dir - 解压后的目录
 */
async function processMainPackage(dir) {
    console.log('检测到小程序主包，处理特殊文件...');
    
    // 处理 app-config.json
    const configPath = path.resolve(dir, 'app-config.json');
    if (fs.existsSync(configPath)) {
        try {
            const configData = await fs.promises.readFile(configPath, 'utf8');
            const config = JSON.parse(configData);
            
            // 保存为 app.json
            const appJsonPath = path.resolve(dir, 'app.json');
            await fs.promises.writeFile(appJsonPath, JSON.stringify(config, null, 2));
            
            console.log('✓ 处理 app.json 完成');
        } catch (error) {
            console.error(`处理配置文件失败: ${error.message}`);
        }
    }
    
    // 注意：这里简化了处理逻辑
    // 原项目中还包括 JS 分割、WXML/WXSS 还原等复杂处理
    // 为了保持代码简洁，这里只做基本的文件提取
    console.log('✓ 主包基本处理完成（简化版本）');
}

/**
 * 处理小游戏包的特殊文件
 * @param {string} dir - 解压后的目录
 */
async function processGamePackage(dir) {
    console.log('检测到小游戏包，处理特殊文件...');
    
    // 处理 app-config.json -> game.json
    const configPath = path.resolve(dir, 'app-config.json');
    if (fs.existsSync(configPath)) {
        try {
            const configData = await fs.promises.readFile(configPath, 'utf8');
            const config = JSON.parse(configData);
            
            // 移除 subContext（如果存在）
            if (config.subContext) {
                delete config.subContext;
            }
            
            // 保存为 game.json
            const gameJsonPath = path.resolve(dir, 'game.json');
            await fs.promises.writeFile(gameJsonPath, JSON.stringify(config, null, 2));
            
            console.log('✓ 处理 game.json 完成');
        } catch (error) {
            console.error(`处理游戏配置文件失败: ${error.message}`);
        }
    }
}

/**
 * 解压缩 wxapkg 文件
 * @param {string} inputPath - 输入文件路径
 * @param {string} outputDir - 输出目录路径
 */
async function unpackFile(inputPath, outputDir) {
    try {
        console.log(`开始解压缩: ${inputPath}`);
        
        // 读取文件内容
        const fileBuffer = await utils.readFileBuffer(inputPath);
        
        if (fileBuffer.length < 14) {
            throw new Error('文件太小，不是有效的 wxapkg 文件');
        }
        
        // 解析文件头
        const headerInfo = parseHeader(fileBuffer.slice(0, 14));
        
        // 解析文件列表
        const fileListBuffer = fileBuffer.slice(14, 14 + headerInfo.infoListLength);
        const fileList = parseFileList(fileListBuffer);

        // 确保输出目录存在
        await utils.ensureDir(outputDir);

        // 提取文件 - 传递整个文件缓冲区，因为偏移量是绝对的
        await extractFiles(outputDir, fileBuffer, fileList);
        
        // 根据包类型进行后处理
        if (isMainPackage(outputDir)) {
            await processMainPackage(outputDir);
        } else if (isGamePackage(outputDir)) {
            await processGamePackage(outputDir);
        } else {
            console.log('检测到分包或其他类型包，跳过特殊处理');
        }

        // 格式化JS和JSON文件
        console.log('开始格式化JS和JSON文件...');
        const formatResult = await formatter.format(outputDir);
        if (formatResult.total > 0) {
            console.log(`✓ 格式化完成: ${formatResult.formatted}/${formatResult.total} 个文件`);
        } else {
            console.log('未找到需要格式化的文件');
        }

        console.log(`✓ 解压缩完成: ${outputDir}`);
        
    } catch (error) {
        throw new Error(`解压缩文件失败 ${inputPath}: ${error.message}`);
    }
}

module.exports = {
    unpackFile,
    parseHeader,
    parseFileList,
    isMainPackage,
    isGamePackage
};
