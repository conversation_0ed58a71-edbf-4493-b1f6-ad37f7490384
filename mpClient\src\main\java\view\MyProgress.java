package view;

import env.Global;
import javafx.animation.KeyFrame;
import javafx.animation.KeyValue;
import javafx.animation.Timeline;
import javafx.application.Platform;
import javafx.beans.binding.Bindings;
import javafx.beans.property.DoubleProperty;
import javafx.beans.property.SimpleDoubleProperty;
import javafx.beans.property.SimpleStringProperty;
import javafx.beans.property.StringProperty;
import javafx.geometry.Pos;
import javafx.scene.Node;
import javafx.scene.Scene;
import javafx.scene.control.Label;
import javafx.scene.control.ProgressIndicator;
import javafx.scene.layout.Background;
import javafx.scene.layout.BorderPane;
import javafx.scene.layout.VBox;
import javafx.scene.paint.Color;
import javafx.stage.Modality;
import javafx.stage.Stage;
import javafx.stage.StageStyle;
import javafx.util.Duration;

public class MyProgress {

    private final StringProperty text;

    private BorderPane root;
    private Node last;
    private VBox loading;

    public MyProgress(BorderPane root) {
        this.root = root;
        ProgressIndicator progressIndicator = new ProgressIndicator();
        // progress bar
        Label label = new Label("数据加载中, 请稍后...");
        text = label.textProperty();
        label.setTextFill(Color.RED);

        // 创建一个Timeline动画
        Timeline timeline = new Timeline();
        // 创建一个KeyFrame，改变label的透明度
        KeyValue kv1 = new KeyValue(label.opacityProperty(), 0.0);
        KeyValue kv2 = new KeyValue(label.opacityProperty(), 1.0);
        KeyFrame kf1 = new KeyFrame(Duration.millis(0), kv1);
        KeyFrame kf2 = new KeyFrame(Duration.millis(2000), kv2);
        KeyFrame kf3 = new KeyFrame(Duration.millis(4000), kv1);
        // 将KeyFrame添加到Timeline中
        timeline.getKeyFrames().add(kf1);
        timeline.getKeyFrames().add(kf2);
        timeline.getKeyFrames().add(kf3);
        // 设置Timeline无限循环
        timeline.setCycleCount(Timeline.INDEFINITE);
        // 开始动画
        timeline.play();

        progressIndicator.setProgress(-1f);

        Global.appendAndReplace(progressIndicator, "-fx-progress-color: #17ead9;");

        // // 这个渐变动画  让旋转效果不明显，放弃
//        double sh = 175.17;
//        double ss = 90.17;
//        double sb = 91.76;
//        double eh = 229.57;
//        double es = 58.97;
//        double eb = 91.76;
//
//        final DoubleProperty a = new SimpleDoubleProperty(sh);
//        final DoubleProperty a1 = new SimpleDoubleProperty(ss);
//        final DoubleProperty a2 = new SimpleDoubleProperty(sb);
//
//        progressIndicator.styleProperty().bind(Bindings.concat(
//                "-fx-progress-color: hsb(", a.asString(), ", ", a1.asString(), "%, ", a2.asString(), "%);"
//        ));
//
//        Timeline anim_a = new Timeline(
//                new KeyFrame(Duration.seconds(5), new KeyValue(a, eh)),
//                new KeyFrame(Duration.seconds(5), new KeyValue(a, sh))
//        );
//        anim_a.setCycleCount(Timeline.INDEFINITE);
//        anim_a.setAutoReverse(true);
//        anim_a.play();
//
//        Timeline anim_a1 = new Timeline(
//                new KeyFrame(Duration.seconds(5), new KeyValue(a1, es)),
//                new KeyFrame(Duration.seconds(5), new KeyValue(a1, ss))
//        );
//        anim_a1.setCycleCount(Timeline.INDEFINITE);
//        anim_a1.setAutoReverse(true);
//        anim_a1.play();
//
//        Timeline anim_a2 = new Timeline(
//                new KeyFrame(Duration.seconds(5), new KeyValue(a2, eb)),
//                new KeyFrame(Duration.seconds(5), new KeyValue(a2, sb))
//        );
//        anim_a2.setCycleCount(Timeline.INDEFINITE);
//        anim_a2.setAutoReverse(true);
//        anim_a2.play();


        loading = new VBox();
        loading.setSpacing(10);
        loading.setAlignment(Pos.CENTER);
        loading.setBackground(Background.EMPTY);
        loading.getChildren().addAll(progressIndicator, label);

    }

    public void activateProgressBar() {
        this.last = this.root.getCenter();
        Platform.runLater(() -> this.root.setCenter(this.loading));
    }

    public void cancelProgressBar() {
        Platform.runLater(() -> this.root.setCenter(this.last));
    }

    public void setText(String v) {
        Platform.runLater(() -> this.text.set(v));
    }

    public boolean hasLast() {
        return this.last != null;
    }

    public void setLast(Node node) {
        if (this.hasLast()) {
            System.out.println("last node is null !!!");
            return;
        }
        this.last = node;
    }

}
