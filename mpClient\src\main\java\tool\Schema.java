package tool;

import com.teamdev.jxbrowser.net.HttpHeader;
import com.teamdev.jxbrowser.net.HttpStatus;
import com.teamdev.jxbrowser.net.UrlRequestJob;
import com.teamdev.jxbrowser.net.callback.InterceptUrlRequestCallback;
import env.Global;
import mgr.BrowserManager;
import org.apache.commons.io.FileUtils;

import javax.annotation.CheckForNull;
import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.ByteBuffer;
import java.nio.charset.StandardCharsets;
import java.nio.file.Path;
import java.nio.file.Paths;

public class Schema implements InterceptUrlRequestCallback {

    String type;

    public Schema(String type) {
        this.type = type;
    }


    @CheckForNull
    @Override
    public Response on(Params params) {
        UrlRequestJob.Options.Builder builder = UrlRequestJob.Options
                .newBuilder(HttpStatus.OK);
        UrlRequestJob job = null;
        try {
            String url = params.urlRequest().url();
            url = url.replace(this.type + "://index.html/", "");
            String property = System.getProperty("user.dir");
            Path path = Paths.get(property);
            if (url.equals("")) {
                path = path.resolve("src/test/remote/index.html");
            } else {
                if (!url.contains("src/test/remote/")) {
                    path = path.resolve("src/test/remote/");
                }
                path = path.resolve(url);
            }

            byte[] data = FileUtils.readFileToByteArray(path.toFile());
            String mimeType = getMimeType(url);
            builder.addHttpHeader(HttpHeader.of("Content-Type", mimeType));
            job = params.newUrlRequestJob(builder.build());
            job.write(data);
            job.complete();
        } catch (Exception e) {
            System.out.println("Schema error:" + this.type + "=> " + params.urlRequest().url());
            throw new RuntimeException(e);
        }
        return InterceptUrlRequestCallback.Response.intercept(job);
    }

    private static String getMimeType(String path) {
        if (path.contains(".html")) {
            return "text/html";
        }
        if (path.contains(".css")) {
            return "text/css";
        }
        if (path.contains(".js")) {
            return "text/javascript";
        }
        if (path.contains(".png")) {
            return "image/png";
        }
        if (path.contains(".jpg")) {
            return "image/jpg";
        }
        return "text/html";
    }
}
