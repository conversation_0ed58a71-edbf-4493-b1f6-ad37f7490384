<template>
  <div></div>
</template>

<script lang="ts" setup>
import {useMessage} from 'naive-ui';

window.$message = useMessage();
</script>

<style>
.n-message-container {
  z-index: 2048 !important;
}

.n-message {
  color: white !important;
  backdrop-filter: blur(16px);
  -webkit-backdrop-filter: blur(16px);
  box-shadow: rgba(0, 0, 0, 0.3) 8px 8px 8px 8px;
  border: 0 rgba(255, 255, 255, 0.4) solid;
  border-bottom: 0 rgba(40, 40, 40, 0.35) solid;
  border-right: 0 rgba(40, 40, 40, 0.35) solid;
  position: relative;
  background: rgba(0, 0, 0, 0.8);
  border-radius: 20px;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
}


.n-message:before {
  content: "";
  position: absolute;
  width: 120%;
  height: 40%;
  background: linear-gradient(#00ccff, #d500f9);
  animation: rotate 4s linear infinite;
}

.n-message:after {
  content: "";
  position: absolute;
  background: rgba(14, 21, 56, 1);
  backdrop-filter: blur(16px);
  -webkit-backdrop-filter: blur(16px);
  inset: 1px;
  border-radius: 20px
}


@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@-webkit-keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.n-message__icon {
  z-index: 1;
  user-select: none;
  -webkit-user-select: none;
}

.n-message__content {
  z-index: 1;
  user-select: none;
  -webkit-user-select: none;
}

</style>
