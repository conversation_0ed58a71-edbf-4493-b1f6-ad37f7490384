import {defineStore} from "pinia"

const init = () => {
    let x: number = Number(localStorage.getItem("tip_x") || "0")
    let y: number = Number(localStorage.getItem("tip_y") || "0")
    x = x || 60
    y = y || 430
    x >= document.documentElement.clientWidth && (x = 0)
    y >= document.documentElement.clientHeight && (y = 0)
    const val = localStorage.getItem("_tip_active") || 'false'
    return {
        _x: x,
        _y: y,
        _active: val === 'true',
        _delay: -1
    }
}

export const useTipStore = defineStore("tip", {
    state: () => init(),
    getters: {
        x: (state) => state._x,
        y: (state) => state._y,
        active: (state) => state._active
    },
    actions: {
        move(x: number, y: number) {
            this._x = x
            this._y = y
            localStorage.setItem("tip_x", this._x + "")
            localStorage.setItem("tip_y", this._y + "")
        },
        changeActive() {
            if (this._delay != -1) clearTimeout(this._delay)
            this._active = !this._active
            localStorage.setItem("_tip_active", this.active + "")
        },
        delayChangeActive(delay: number = 1000) {
            if (this._delay != -1) return
            this._delay = setTimeout(() => {
                this.changeActive()
                this._delay = -1
            }, delay)
        }

    }
})
