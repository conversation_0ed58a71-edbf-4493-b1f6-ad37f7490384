package net.server;

import io.netty.buffer.ByteBuf;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.SimpleChannelInboundHandler;
import net.NetworkManager;

import java.nio.charset.StandardCharsets;

public class ServerHandler extends SimpleChannelInboundHandler<ByteBuf> {
    @Override
    protected void channelRead0(ChannelHandlerContext ctx, ByteBuf msg) throws Exception {
        int code = msg.readInt();
        byte[] able = new byte[msg.readableBytes()];
        msg.readBytes(able);
        String content = new String(able, StandardCharsets.UTF_8);
        NetworkManager.getMe().doNetworkCmd(code, content);
    }

    @Override
    public void channelActive(ChannelHandlerContext ctx) throws Exception {
        // 新链接
        super.channelActive(ctx);
    }

    @Override
    public void handlerRemoved(ChannelHandlerContext ctx) {
        try {
            super.handlerRemoved(ctx);
        } catch (Exception e) {
        }
    }

    @Override
    public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) throws Exception {
    }
}
