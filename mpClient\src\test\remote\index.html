<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>微信小游戏 - wx0c747e2a0ec57737</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: #000;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }

        #loading {
            color: white;
            font-family: Arial, sans-serif;
            text-align: center;
        }
    </style>

  <script type="module" crossorigin>
var zt=Object.defineProperty;var Ht=(e,t,o)=>t in e?zt(e,t,{enumerable:!0,configurable:!0,writable:!0,value:o}):e[t]=o;var Ft=(e,t)=>()=>(t||e((t={exports:{}}).exports,t),t.exports);var ze=(e,t,o)=>(Ht(e,typeof t!="symbol"?t+"":t,o),o);var Lt=Ft((exports,module)=>{(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const n of document.querySelectorAll('link[rel="modulepreload"]'))r(n);new MutationObserver(n=>{for(const i of n)if(i.type==="childList")for(const a of i.addedNodes)a.tagName==="LINK"&&a.rel==="modulepreload"&&r(a)}).observe(document,{childList:!0,subtree:!0});function o(n){const i={};return n.integrity&&(i.integrity=n.integrity),n.referrerPolicy&&(i.referrerPolicy=n.referrerPolicy),n.crossOrigin==="use-credentials"?i.credentials="include":n.crossOrigin==="anonymous"?i.credentials="omit":i.credentials="same-origin",i}function r(n){if(n.ep)return;n.ep=!0;const i=o(n);fetch(n.href,i)}})();function makeMap(e,t){const o=Object.create(null),r=e.split(",");for(let n=0;n<r.length;n++)o[r[n]]=!0;return t?n=>!!o[n.toLowerCase()]:n=>!!o[n]}const EMPTY_OBJ={},EMPTY_ARR=[],NOOP=()=>{},NO=()=>!1,isOn=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&(e.charCodeAt(2)>122||e.charCodeAt(2)<97),isModelListener=e=>e.startsWith("onUpdate:"),extend=Object.assign,remove=(e,t)=>{const o=e.indexOf(t);o>-1&&e.splice(o,1)},hasOwnProperty$a=Object.prototype.hasOwnProperty,hasOwn=(e,t)=>hasOwnProperty$a.call(e,t),isArray$2=Array.isArray,isMap=e=>toTypeString(e)==="[object Map]",isSet=e=>toTypeString(e)==="[object Set]",isFunction$1=e=>typeof e=="function",isString=e=>typeof e=="string",isSymbol$1=e=>typeof e=="symbol",isObject$1=e=>e!==null&&typeof e=="object",isPromise=e=>(isObject$1(e)||isFunction$1(e))&&isFunction$1(e.then)&&isFunction$1(e.catch),objectToString$1=Object.prototype.toString,toTypeString=e=>objectToString$1.call(e),toRawType=e=>toTypeString(e).slice(8,-1),isPlainObject$2=e=>toTypeString(e)==="[object Object]",isIntegerKey=e=>isString(e)&&e!=="NaN"&&e[0]!=="-"&&""+parseInt(e,10)===e,isReservedProp=makeMap(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),cacheStringFunction=e=>{const t=Object.create(null);return o=>t[o]||(t[o]=e(o))},camelizeRE=/-(\w)/g,camelize=cacheStringFunction(e=>e.replace(camelizeRE,(t,o)=>o?o.toUpperCase():"")),hyphenateRE=/\B([A-Z])/g,hyphenate=cacheStringFunction(e=>e.replace(hyphenateRE,"-$1").toLowerCase()),capitalize=cacheStringFunction(e=>e.charAt(0).toUpperCase()+e.slice(1)),toHandlerKey=cacheStringFunction(e=>e?`on${capitalize(e)}`:""),hasChanged=(e,t)=>!Object.is(e,t),invokeArrayFns=(e,t)=>{for(let o=0;o<e.length;o++)e[o](t)},def=(e,t,o)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,value:o})},looseToNumber=e=>{const t=parseFloat(e);return isNaN(t)?e:t},toNumber=e=>{const t=isString(e)?Number(e):NaN;return isNaN(t)?e:t};let _globalThis;const getGlobalThis=()=>_globalThis||(_globalThis=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:{});function normalizeStyle(e){if(isArray$2(e)){const t={};for(let o=0;o<e.length;o++){const r=e[o],n=isString(r)?parseStringStyle(r):normalizeStyle(r);if(n)for(const i in n)t[i]=n[i]}return t}else if(isString(e)||isObject$1(e))return e}const listDelimiterRE=/;(?![^(]*\))/g,propertyDelimiterRE=/:([^]+)/,styleCommentRE=/\/\*[^]*?\*\//g;function parseStringStyle(e){const t={};return e.replace(styleCommentRE,"").split(listDelimiterRE).forEach(o=>{if(o){const r=o.split(propertyDelimiterRE);r.length>1&&(t[r[0].trim()]=r[1].trim())}}),t}function normalizeClass(e){let t="";if(isString(e))t=e;else if(isArray$2(e))for(let o=0;o<e.length;o++){const r=normalizeClass(e[o]);r&&(t+=r+" ")}else if(isObject$1(e))for(const o in e)e[o]&&(t+=o+" ");return t.trim()}const specialBooleanAttrs="itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly",isSpecialBooleanAttr=makeMap(specialBooleanAttrs);function includeBooleanAttr(e){return!!e||e===""}const toDisplayString=e=>isString(e)?e:e==null?"":isArray$2(e)||isObject$1(e)&&(e.toString===objectToString$1||!isFunction$1(e.toString))?JSON.stringify(e,replacer,2):String(e),replacer=(e,t)=>t&&t.__v_isRef?replacer(e,t.value):isMap(t)?{[`Map(${t.size})`]:[...t.entries()].reduce((o,[r,n],i)=>(o[stringifySymbol(r,i)+" =>"]=n,o),{})}:isSet(t)?{[`Set(${t.size})`]:[...t.values()].map(o=>stringifySymbol(o))}:isSymbol$1(t)?stringifySymbol(t):isObject$1(t)&&!isArray$2(t)&&!isPlainObject$2(t)?String(t):t,stringifySymbol=(e,t="")=>{var o;return isSymbol$1(e)?`Symbol(${(o=e.description)!=null?o:t})`:e};let activeEffectScope;class EffectScope{constructor(t=!1){this.detached=t,this._active=!0,this.effects=[],this.cleanups=[],this.parent=activeEffectScope,!t&&activeEffectScope&&(this.index=(activeEffectScope.scopes||(activeEffectScope.scopes=[])).push(this)-1)}get active(){return this._active}run(t){if(this._active){const o=activeEffectScope;try{return activeEffectScope=this,t()}finally{activeEffectScope=o}}}on(){activeEffectScope=this}off(){activeEffectScope=this.parent}stop(t){if(this._active){let o,r;for(o=0,r=this.effects.length;o<r;o++)this.effects[o].stop();for(o=0,r=this.cleanups.length;o<r;o++)this.cleanups[o]();if(this.scopes)for(o=0,r=this.scopes.length;o<r;o++)this.scopes[o].stop(!0);if(!this.detached&&this.parent&&!t){const n=this.parent.scopes.pop();n&&n!==this&&(this.parent.scopes[this.index]=n,n.index=this.index)}this.parent=void 0,this._active=!1}}}function effectScope(e){return new EffectScope(e)}function recordEffectScope(e,t=activeEffectScope){t&&t.active&&t.effects.push(e)}function getCurrentScope(){return activeEffectScope}function onScopeDispose(e){activeEffectScope&&activeEffectScope.cleanups.push(e)}const createDep=e=>{const t=new Set(e);return t.w=0,t.n=0,t},wasTracked=e=>(e.w&trackOpBit)>0,newTracked=e=>(e.n&trackOpBit)>0,initDepMarkers=({deps:e})=>{if(e.length)for(let t=0;t<e.length;t++)e[t].w|=trackOpBit},finalizeDepMarkers=e=>{const{deps:t}=e;if(t.length){let o=0;for(let r=0;r<t.length;r++){const n=t[r];wasTracked(n)&&!newTracked(n)?n.delete(e):t[o++]=n,n.w&=~trackOpBit,n.n&=~trackOpBit}t.length=o}},targetMap=new WeakMap;let effectTrackDepth=0,trackOpBit=1;const maxMarkerBits=30;let activeEffect;const ITERATE_KEY=Symbol(""),MAP_KEY_ITERATE_KEY=Symbol("");class ReactiveEffect{constructor(t,o=null,r){this.fn=t,this.scheduler=o,this.active=!0,this.deps=[],this.parent=void 0,recordEffectScope(this,r)}run(){if(!this.active)return this.fn();let t=activeEffect,o=shouldTrack;for(;t;){if(t===this)return;t=t.parent}try{return this.parent=activeEffect,activeEffect=this,shouldTrack=!0,trackOpBit=1<<++effectTrackDepth,effectTrackDepth<=maxMarkerBits?initDepMarkers(this):cleanupEffect(this),this.fn()}finally{effectTrackDepth<=maxMarkerBits&&finalizeDepMarkers(this),trackOpBit=1<<--effectTrackDepth,activeEffect=this.parent,shouldTrack=o,this.parent=void 0,this.deferStop&&this.stop()}}stop(){activeEffect===this?this.deferStop=!0:this.active&&(cleanupEffect(this),this.onStop&&this.onStop(),this.active=!1)}}function cleanupEffect(e){const{deps:t}=e;if(t.length){for(let o=0;o<t.length;o++)t[o].delete(e);t.length=0}}let shouldTrack=!0;const trackStack=[];function pauseTracking(){trackStack.push(shouldTrack),shouldTrack=!1}function resetTracking(){const e=trackStack.pop();shouldTrack=e===void 0?!0:e}function track(e,t,o){if(shouldTrack&&activeEffect){let r=targetMap.get(e);r||targetMap.set(e,r=new Map);let n=r.get(o);n||r.set(o,n=createDep()),trackEffects(n)}}function trackEffects(e,t){let o=!1;effectTrackDepth<=maxMarkerBits?newTracked(e)||(e.n|=trackOpBit,o=!wasTracked(e)):o=!e.has(activeEffect),o&&(e.add(activeEffect),activeEffect.deps.push(e))}function trigger$1(e,t,o,r,n,i){const a=targetMap.get(e);if(!a)return;let l=[];if(t==="clear")l=[...a.values()];else if(o==="length"&&isArray$2(e)){const s=Number(r);a.forEach((d,u)=>{(u==="length"||!isSymbol$1(u)&&u>=s)&&l.push(d)})}else switch(o!==void 0&&l.push(a.get(o)),t){case"add":isArray$2(e)?isIntegerKey(o)&&l.push(a.get("length")):(l.push(a.get(ITERATE_KEY)),isMap(e)&&l.push(a.get(MAP_KEY_ITERATE_KEY)));break;case"delete":isArray$2(e)||(l.push(a.get(ITERATE_KEY)),isMap(e)&&l.push(a.get(MAP_KEY_ITERATE_KEY)));break;case"set":isMap(e)&&l.push(a.get(ITERATE_KEY));break}if(l.length===1)l[0]&&triggerEffects(l[0]);else{const s=[];for(const d of l)d&&s.push(...d);triggerEffects(createDep(s))}}function triggerEffects(e,t){const o=isArray$2(e)?e:[...e];for(const r of o)r.computed&&triggerEffect(r);for(const r of o)r.computed||triggerEffect(r)}function triggerEffect(e,t){(e!==activeEffect||e.allowRecurse)&&(e.scheduler?e.scheduler():e.run())}function getDepFromReactive(e,t){var o;return(o=targetMap.get(e))==null?void 0:o.get(t)}const isNonTrackableKeys=makeMap("__proto__,__v_isRef,__isVue"),builtInSymbols=new Set(Object.getOwnPropertyNames(Symbol).filter(e=>e!=="arguments"&&e!=="caller").map(e=>Symbol[e]).filter(isSymbol$1)),arrayInstrumentations=createArrayInstrumentations();function createArrayInstrumentations(){const e={};return["includes","indexOf","lastIndexOf"].forEach(t=>{e[t]=function(...o){const r=toRaw(this);for(let i=0,a=this.length;i<a;i++)track(r,"get",i+"");const n=r[t](...o);return n===-1||n===!1?r[t](...o.map(toRaw)):n}}),["push","pop","shift","unshift","splice"].forEach(t=>{e[t]=function(...o){pauseTracking();const r=toRaw(this)[t].apply(this,o);return resetTracking(),r}}),e}function hasOwnProperty$9(e){const t=toRaw(this);return track(t,"has",e),t.hasOwnProperty(e)}class BaseReactiveHandler{constructor(t=!1,o=!1){this._isReadonly=t,this._shallow=o}get(t,o,r){const n=this._isReadonly,i=this._shallow;if(o==="__v_isReactive")return!n;if(o==="__v_isReadonly")return n;if(o==="__v_isShallow")return i;if(o==="__v_raw")return r===(n?i?shallowReadonlyMap:readonlyMap:i?shallowReactiveMap:reactiveMap).get(t)||Object.getPrototypeOf(t)===Object.getPrototypeOf(r)?t:void 0;const a=isArray$2(t);if(!n){if(a&&hasOwn(arrayInstrumentations,o))return Reflect.get(arrayInstrumentations,o,r);if(o==="hasOwnProperty")return hasOwnProperty$9}const l=Reflect.get(t,o,r);return(isSymbol$1(o)?builtInSymbols.has(o):isNonTrackableKeys(o))||(n||track(t,"get",o),i)?l:isRef(l)?a&&isIntegerKey(o)?l:l.value:isObject$1(l)?n?readonly(l):reactive(l):l}}class MutableReactiveHandler extends BaseReactiveHandler{constructor(t=!1){super(!1,t)}set(t,o,r,n){let i=t[o];if(!this._shallow){const s=isReadonly(i);if(!isShallow(r)&&!isReadonly(r)&&(i=toRaw(i),r=toRaw(r)),!isArray$2(t)&&isRef(i)&&!isRef(r))return s?!1:(i.value=r,!0)}const a=isArray$2(t)&&isIntegerKey(o)?Number(o)<t.length:hasOwn(t,o),l=Reflect.set(t,o,r,n);return t===toRaw(n)&&(a?hasChanged(r,i)&&trigger$1(t,"set",o,r):trigger$1(t,"add",o,r)),l}deleteProperty(t,o){const r=hasOwn(t,o);t[o];const n=Reflect.deleteProperty(t,o);return n&&r&&trigger$1(t,"delete",o,void 0),n}has(t,o){const r=Reflect.has(t,o);return(!isSymbol$1(o)||!builtInSymbols.has(o))&&track(t,"has",o),r}ownKeys(t){return track(t,"iterate",isArray$2(t)?"length":ITERATE_KEY),Reflect.ownKeys(t)}}class ReadonlyReactiveHandler extends BaseReactiveHandler{constructor(t=!1){super(!0,t)}set(t,o){return!0}deleteProperty(t,o){return!0}}const mutableHandlers=new MutableReactiveHandler,readonlyHandlers=new ReadonlyReactiveHandler,shallowReactiveHandlers=new MutableReactiveHandler(!0),toShallow=e=>e,getProto=e=>Reflect.getPrototypeOf(e);function get(e,t,o=!1,r=!1){e=e.__v_raw;const n=toRaw(e),i=toRaw(t);o||(hasChanged(t,i)&&track(n,"get",t),track(n,"get",i));const{has:a}=getProto(n),l=r?toShallow:o?toReadonly:toReactive;if(a.call(n,t))return l(e.get(t));if(a.call(n,i))return l(e.get(i));e!==n&&e.get(t)}function has(e,t=!1){const o=this.__v_raw,r=toRaw(o),n=toRaw(e);return t||(hasChanged(e,n)&&track(r,"has",e),track(r,"has",n)),e===n?o.has(e):o.has(e)||o.has(n)}function size$1(e,t=!1){return e=e.__v_raw,!t&&track(toRaw(e),"iterate",ITERATE_KEY),Reflect.get(e,"size",e)}function add(e){e=toRaw(e);const t=toRaw(this);return getProto(t).has.call(t,e)||(t.add(e),trigger$1(t,"add",e,e)),this}function set(e,t){t=toRaw(t);const o=toRaw(this),{has:r,get:n}=getProto(o);let i=r.call(o,e);i||(e=toRaw(e),i=r.call(o,e));const a=n.call(o,e);return o.set(e,t),i?hasChanged(t,a)&&trigger$1(o,"set",e,t):trigger$1(o,"add",e,t),this}function deleteEntry(e){const t=toRaw(this),{has:o,get:r}=getProto(t);let n=o.call(t,e);n||(e=toRaw(e),n=o.call(t,e)),r&&r.call(t,e);const i=t.delete(e);return n&&trigger$1(t,"delete",e,void 0),i}function clear(){const e=toRaw(this),t=e.size!==0,o=e.clear();return t&&trigger$1(e,"clear",void 0,void 0),o}function createForEach(e,t){return function(r,n){const i=this,a=i.__v_raw,l=toRaw(a),s=t?toShallow:e?toReadonly:toReactive;return!e&&track(l,"iterate",ITERATE_KEY),a.forEach((d,u)=>r.call(n,s(d),s(u),i))}}function createIterableMethod(e,t,o){return function(...r){const n=this.__v_raw,i=toRaw(n),a=isMap(i),l=e==="entries"||e===Symbol.iterator&&a,s=e==="keys"&&a,d=n[e](...r),u=o?toShallow:t?toReadonly:toReactive;return!t&&track(i,"iterate",s?MAP_KEY_ITERATE_KEY:ITERATE_KEY),{next(){const{value:f,done:m}=d.next();return m?{value:f,done:m}:{value:l?[u(f[0]),u(f[1])]:u(f),done:m}},[Symbol.iterator](){return this}}}}function createReadonlyMethod(e){return function(...t){return e==="delete"?!1:e==="clear"?void 0:this}}function createInstrumentations(){const e={get(i){return get(this,i)},get size(){return size$1(this)},has,add,set,delete:deleteEntry,clear,forEach:createForEach(!1,!1)},t={get(i){return get(this,i,!1,!0)},get size(){return size$1(this)},has,add,set,delete:deleteEntry,clear,forEach:createForEach(!1,!0)},o={get(i){return get(this,i,!0)},get size(){return size$1(this,!0)},has(i){return has.call(this,i,!0)},add:createReadonlyMethod("add"),set:createReadonlyMethod("set"),delete:createReadonlyMethod("delete"),clear:createReadonlyMethod("clear"),forEach:createForEach(!0,!1)},r={get(i){return get(this,i,!0,!0)},get size(){return size$1(this,!0)},has(i){return has.call(this,i,!0)},add:createReadonlyMethod("add"),set:createReadonlyMethod("set"),delete:createReadonlyMethod("delete"),clear:createReadonlyMethod("clear"),forEach:createForEach(!0,!0)};return["keys","values","entries",Symbol.iterator].forEach(i=>{e[i]=createIterableMethod(i,!1,!1),o[i]=createIterableMethod(i,!0,!1),t[i]=createIterableMethod(i,!1,!0),r[i]=createIterableMethod(i,!0,!0)}),[e,o,t,r]}const[mutableInstrumentations,readonlyInstrumentations,shallowInstrumentations,shallowReadonlyInstrumentations]=createInstrumentations();function createInstrumentationGetter(e,t){const o=t?e?shallowReadonlyInstrumentations:shallowInstrumentations:e?readonlyInstrumentations:mutableInstrumentations;return(r,n,i)=>n==="__v_isReactive"?!e:n==="__v_isReadonly"?e:n==="__v_raw"?r:Reflect.get(hasOwn(o,n)&&n in r?o:r,n,i)}const mutableCollectionHandlers={get:createInstrumentationGetter(!1,!1)},shallowCollectionHandlers={get:createInstrumentationGetter(!1,!0)},readonlyCollectionHandlers={get:createInstrumentationGetter(!0,!1)},reactiveMap=new WeakMap,shallowReactiveMap=new WeakMap,readonlyMap=new WeakMap,shallowReadonlyMap=new WeakMap;function targetTypeMap(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}function getTargetType(e){return e.__v_skip||!Object.isExtensible(e)?0:targetTypeMap(toRawType(e))}function reactive(e){return isReadonly(e)?e:createReactiveObject(e,!1,mutableHandlers,mutableCollectionHandlers,reactiveMap)}function shallowReactive(e){return createReactiveObject(e,!1,shallowReactiveHandlers,shallowCollectionHandlers,shallowReactiveMap)}function readonly(e){return createReactiveObject(e,!0,readonlyHandlers,readonlyCollectionHandlers,readonlyMap)}function createReactiveObject(e,t,o,r,n){if(!isObject$1(e)||e.__v_raw&&!(t&&e.__v_isReactive))return e;const i=n.get(e);if(i)return i;const a=getTargetType(e);if(a===0)return e;const l=new Proxy(e,a===2?r:o);return n.set(e,l),l}function isReactive(e){return isReadonly(e)?isReactive(e.__v_raw):!!(e&&e.__v_isReactive)}function isReadonly(e){return!!(e&&e.__v_isReadonly)}function isShallow(e){return!!(e&&e.__v_isShallow)}function isProxy(e){return isReactive(e)||isReadonly(e)}function toRaw(e){const t=e&&e.__v_raw;return t?toRaw(t):e}function markRaw(e){return def(e,"__v_skip",!0),e}const toReactive=e=>isObject$1(e)?reactive(e):e,toReadonly=e=>isObject$1(e)?readonly(e):e;function trackRefValue(e){shouldTrack&&activeEffect&&(e=toRaw(e),trackEffects(e.dep||(e.dep=createDep())))}function triggerRefValue(e,t){e=toRaw(e);const o=e.dep;o&&triggerEffects(o)}function isRef(e){return!!(e&&e.__v_isRef===!0)}function ref(e){return createRef(e,!1)}function shallowRef(e){return createRef(e,!0)}function createRef(e,t){return isRef(e)?e:new RefImpl(e,t)}class RefImpl{constructor(t,o){this.__v_isShallow=o,this.dep=void 0,this.__v_isRef=!0,this._rawValue=o?t:toRaw(t),this._value=o?t:toReactive(t)}get value(){return trackRefValue(this),this._value}set value(t){const o=this.__v_isShallow||isShallow(t)||isReadonly(t);t=o?t:toRaw(t),hasChanged(t,this._rawValue)&&(this._rawValue=t,this._value=o?t:toReactive(t),triggerRefValue(this))}}function unref(e){return isRef(e)?e.value:e}const shallowUnwrapHandlers={get:(e,t,o)=>unref(Reflect.get(e,t,o)),set:(e,t,o,r)=>{const n=e[t];return isRef(n)&&!isRef(o)?(n.value=o,!0):Reflect.set(e,t,o,r)}};function proxyRefs(e){return isReactive(e)?e:new Proxy(e,shallowUnwrapHandlers)}function toRefs(e){const t=isArray$2(e)?new Array(e.length):{};for(const o in e)t[o]=propertyToRef(e,o);return t}class ObjectRefImpl{constructor(t,o,r){this._object=t,this._key=o,this._defaultValue=r,this.__v_isRef=!0}get value(){const t=this._object[this._key];return t===void 0?this._defaultValue:t}set value(t){this._object[this._key]=t}get dep(){return getDepFromReactive(toRaw(this._object),this._key)}}class GetterRefImpl{constructor(t){this._getter=t,this.__v_isRef=!0,this.__v_isReadonly=!0}get value(){return this._getter()}}function toRef(e,t,o){return isRef(e)?e:isFunction$1(e)?new GetterRefImpl(e):isObject$1(e)&&arguments.length>1?propertyToRef(e,t,o):ref(e)}function propertyToRef(e,t,o){const r=e[t];return isRef(r)?r:new ObjectRefImpl(e,t,o)}class ComputedRefImpl{constructor(t,o,r,n){this._setter=o,this.dep=void 0,this.__v_isRef=!0,this.__v_isReadonly=!1,this._dirty=!0,this.effect=new ReactiveEffect(t,()=>{this._dirty||(this._dirty=!0,triggerRefValue(this))}),this.effect.computed=this,this.effect.active=this._cacheable=!n,this.__v_isReadonly=r}get value(){const t=toRaw(this);return trackRefValue(t),(t._dirty||!t._cacheable)&&(t._dirty=!1,t._value=t.effect.run()),t._value}set value(t){this._setter(t)}}function computed$1(e,t,o=!1){let r,n;const i=isFunction$1(e);return i?(r=e,n=NOOP):(r=e.get,n=e.set),new ComputedRefImpl(r,n,i||!n,o)}function warn$3(e,...t){}function callWithErrorHandling(e,t,o,r){let n;try{n=r?e(...r):e()}catch(i){handleError(i,t,o)}return n}function callWithAsyncErrorHandling(e,t,o,r){if(isFunction$1(e)){const i=callWithErrorHandling(e,t,o,r);return i&&isPromise(i)&&i.catch(a=>{handleError(a,t,o)}),i}const n=[];for(let i=0;i<e.length;i++)n.push(callWithAsyncErrorHandling(e[i],t,o,r));return n}function handleError(e,t,o,r=!0){const n=t?t.vnode:null;if(t){let i=t.parent;const a=t.proxy,l=o;for(;i;){const d=i.ec;if(d){for(let u=0;u<d.length;u++)if(d[u](e,a,l)===!1)return}i=i.parent}const s=t.appContext.config.errorHandler;if(s){callWithErrorHandling(s,null,10,[e,a,l]);return}}logError(e,o,n,r)}function logError(e,t,o,r=!0){console.error(e)}let isFlushing=!1,isFlushPending=!1;const queue=[];let flushIndex=0;const pendingPostFlushCbs=[];let activePostFlushCbs=null,postFlushIndex=0;const resolvedPromise=Promise.resolve();let currentFlushPromise=null;function nextTick(e){const t=currentFlushPromise||resolvedPromise;return e?t.then(this?e.bind(this):e):t}function findInsertionIndex(e){let t=flushIndex+1,o=queue.length;for(;t<o;){const r=t+o>>>1,n=queue[r],i=getId(n);i<e||i===e&&n.pre?t=r+1:o=r}return t}function queueJob(e){(!queue.length||!queue.includes(e,isFlushing&&e.allowRecurse?flushIndex+1:flushIndex))&&(e.id==null?queue.push(e):queue.splice(findInsertionIndex(e.id),0,e),queueFlush())}function queueFlush(){!isFlushing&&!isFlushPending&&(isFlushPending=!0,currentFlushPromise=resolvedPromise.then(flushJobs))}function invalidateJob(e){const t=queue.indexOf(e);t>flushIndex&&queue.splice(t,1)}function queuePostFlushCb(e){isArray$2(e)?pendingPostFlushCbs.push(...e):(!activePostFlushCbs||!activePostFlushCbs.includes(e,e.allowRecurse?postFlushIndex+1:postFlushIndex))&&pendingPostFlushCbs.push(e),queueFlush()}function flushPreFlushCbs(e,t,o=isFlushing?flushIndex+1:0){for(;o<queue.length;o++){const r=queue[o];if(r&&r.pre){if(e&&r.id!==e.uid)continue;queue.splice(o,1),o--,r()}}}function flushPostFlushCbs(e){if(pendingPostFlushCbs.length){const t=[...new Set(pendingPostFlushCbs)];if(pendingPostFlushCbs.length=0,activePostFlushCbs){activePostFlushCbs.push(...t);return}for(activePostFlushCbs=t,activePostFlushCbs.sort((o,r)=>getId(o)-getId(r)),postFlushIndex=0;postFlushIndex<activePostFlushCbs.length;postFlushIndex++)activePostFlushCbs[postFlushIndex]();activePostFlushCbs=null,postFlushIndex=0}}const getId=e=>e.id==null?1/0:e.id,comparator=(e,t)=>{const o=getId(e)-getId(t);if(o===0){if(e.pre&&!t.pre)return-1;if(t.pre&&!e.pre)return 1}return o};function flushJobs(e){isFlushPending=!1,isFlushing=!0,queue.sort(comparator);try{for(flushIndex=0;flushIndex<queue.length;flushIndex++){const t=queue[flushIndex];t&&t.active!==!1&&callWithErrorHandling(t,null,14)}}finally{flushIndex=0,queue.length=0,flushPostFlushCbs(),isFlushing=!1,currentFlushPromise=null,(queue.length||pendingPostFlushCbs.length)&&flushJobs()}}function emit(e,t,...o){if(e.isUnmounted)return;const r=e.vnode.props||EMPTY_OBJ;let n=o;const i=t.startsWith("update:"),a=i&&t.slice(7);if(a&&a in r){const u=`${a==="modelValue"?"model":a}Modifiers`,{number:f,trim:m}=r[u]||EMPTY_OBJ;m&&(n=o.map(v=>isString(v)?v.trim():v)),f&&(n=o.map(looseToNumber))}let l,s=r[l=toHandlerKey(t)]||r[l=toHandlerKey(camelize(t))];!s&&i&&(s=r[l=toHandlerKey(hyphenate(t))]),s&&callWithAsyncErrorHandling(s,e,6,n);const d=r[l+"Once"];if(d){if(!e.emitted)e.emitted={};else if(e.emitted[l])return;e.emitted[l]=!0,callWithAsyncErrorHandling(d,e,6,n)}}function normalizeEmitsOptions(e,t,o=!1){const r=t.emitsCache,n=r.get(e);if(n!==void 0)return n;const i=e.emits;let a={},l=!1;if(!isFunction$1(e)){const s=d=>{const u=normalizeEmitsOptions(d,t,!0);u&&(l=!0,extend(a,u))};!o&&t.mixins.length&&t.mixins.forEach(s),e.extends&&s(e.extends),e.mixins&&e.mixins.forEach(s)}return!i&&!l?(isObject$1(e)&&r.set(e,null),null):(isArray$2(i)?i.forEach(s=>a[s]=null):extend(a,i),isObject$1(e)&&r.set(e,a),a)}function isEmitListener(e,t){return!e||!isOn(t)?!1:(t=t.slice(2).replace(/Once$/,""),hasOwn(e,t[0].toLowerCase()+t.slice(1))||hasOwn(e,hyphenate(t))||hasOwn(e,t))}let currentRenderingInstance=null,currentScopeId=null;function setCurrentRenderingInstance(e){const t=currentRenderingInstance;return currentRenderingInstance=e,currentScopeId=e&&e.type.__scopeId||null,t}function withCtx(e,t=currentRenderingInstance,o){if(!t||e._n)return e;const r=(...n)=>{r._d&&setBlockTracking(-1);const i=setCurrentRenderingInstance(t);let a;try{a=e(...n)}finally{setCurrentRenderingInstance(i),r._d&&setBlockTracking(1)}return a};return r._n=!0,r._c=!0,r._d=!0,r}function markAttrsAccessed(){}function renderComponentRoot(e){const{type:t,vnode:o,proxy:r,withProxy:n,props:i,propsOptions:[a],slots:l,attrs:s,emit:d,render:u,renderCache:f,data:m,setupState:v,ctx:g,inheritAttrs:x}=e;let C,y;const I=setCurrentRenderingInstance(e);try{if(o.shapeFlag&4){const E=n||r,P=E;C=normalizeVNode(u.call(P,E,f,i,v,m,g)),y=s}else{const E=t;C=normalizeVNode(E.length>1?E(i,{attrs:s,slots:l,emit:d}):E(i,null)),y=t.props?s:getFunctionalFallthrough(s)}}catch(E){blockStack.length=0,handleError(E,e,1),C=createVNode(Comment)}let N=C;if(y&&x!==!1){const E=Object.keys(y),{shapeFlag:P}=N;E.length&&P&7&&(a&&E.some(isModelListener)&&(y=filterModelListeners(y,a)),N=cloneVNode(N,y))}return o.dirs&&(N=cloneVNode(N),N.dirs=N.dirs?N.dirs.concat(o.dirs):o.dirs),o.transition&&(N.transition=o.transition),C=N,setCurrentRenderingInstance(I),C}const getFunctionalFallthrough=e=>{let t;for(const o in e)(o==="class"||o==="style"||isOn(o))&&((t||(t={}))[o]=e[o]);return t},filterModelListeners=(e,t)=>{const o={};for(const r in e)(!isModelListener(r)||!(r.slice(9)in t))&&(o[r]=e[r]);return o};function shouldUpdateComponent(e,t,o){const{props:r,children:n,component:i}=e,{props:a,children:l,patchFlag:s}=t,d=i.emitsOptions;if(t.dirs||t.transition)return!0;if(o&&s>=0){if(s&1024)return!0;if(s&16)return r?hasPropsChanged(r,a,d):!!a;if(s&8){const u=t.dynamicProps;for(let f=0;f<u.length;f++){const m=u[f];if(a[m]!==r[m]&&!isEmitListener(d,m))return!0}}}else return(n||l)&&(!l||!l.$stable)?!0:r===a?!1:r?a?hasPropsChanged(r,a,d):!0:!!a;return!1}function hasPropsChanged(e,t,o){const r=Object.keys(t);if(r.length!==Object.keys(e).length)return!0;for(let n=0;n<r.length;n++){const i=r[n];if(t[i]!==e[i]&&!isEmitListener(o,i))return!0}return!1}function updateHOCHostEl({vnode:e,parent:t},o){for(;t&&t.subTree===e;)(e=t.vnode).el=o,t=t.parent}const NULL_DYNAMIC_COMPONENT=Symbol.for("v-ndc"),isSuspense=e=>e.__isSuspense;function queueEffectWithSuspense(e,t){t&&t.pendingBranch?isArray$2(e)?t.effects.push(...e):t.effects.push(e):queuePostFlushCb(e)}function watchEffect(e,t){return doWatch(e,null,t)}const INITIAL_WATCHER_VALUE={};function watch(e,t,o){return doWatch(e,t,o)}function doWatch(e,t,{immediate:o,deep:r,flush:n,onTrack:i,onTrigger:a}=EMPTY_OBJ){var l;const s=getCurrentScope()===((l=currentInstance)==null?void 0:l.scope)?currentInstance:null;let d,u=!1,f=!1;if(isRef(e)?(d=()=>e.value,u=isShallow(e)):isReactive(e)?(d=()=>e,r=!0):isArray$2(e)?(f=!0,u=e.some(E=>isReactive(E)||isShallow(E)),d=()=>e.map(E=>{if(isRef(E))return E.value;if(isReactive(E))return traverse(E);if(isFunction$1(E))return callWithErrorHandling(E,s,2)})):isFunction$1(e)?t?d=()=>callWithErrorHandling(e,s,2):d=()=>{if(!(s&&s.isUnmounted))return m&&m(),callWithAsyncErrorHandling(e,s,3,[v])}:d=NOOP,t&&r){const E=d;d=()=>traverse(E())}let m,v=E=>{m=I.onStop=()=>{callWithErrorHandling(E,s,4),m=I.onStop=void 0}},g;if(isInSSRComponentSetup)if(v=NOOP,t?o&&callWithAsyncErrorHandling(t,s,3,[d(),f?[]:void 0,v]):d(),n==="sync"){const E=useSSRContext();g=E.__watcherHandles||(E.__watcherHandles=[])}else return NOOP;let x=f?new Array(e.length).fill(INITIAL_WATCHER_VALUE):INITIAL_WATCHER_VALUE;const C=()=>{if(I.active)if(t){const E=I.run();(r||u||(f?E.some((P,F)=>hasChanged(P,x[F])):hasChanged(E,x)))&&(m&&m(),callWithAsyncErrorHandling(t,s,3,[E,x===INITIAL_WATCHER_VALUE?void 0:f&&x[0]===INITIAL_WATCHER_VALUE?[]:x,v]),x=E)}else I.run()};C.allowRecurse=!!t;let y;n==="sync"?y=C:n==="post"?y=()=>queuePostRenderEffect(C,s&&s.suspense):(C.pre=!0,s&&(C.id=s.uid),y=()=>queueJob(C));const I=new ReactiveEffect(d,y);t?o?C():x=I.run():n==="post"?queuePostRenderEffect(I.run.bind(I),s&&s.suspense):I.run();const N=()=>{I.stop(),s&&s.scope&&remove(s.scope.effects,I)};return g&&g.push(N),N}function instanceWatch(e,t,o){const r=this.proxy,n=isString(e)?e.includes(".")?createPathGetter(r,e):()=>r[e]:e.bind(r,r);let i;isFunction$1(t)?i=t:(i=t.handler,o=t);const a=currentInstance;setCurrentInstance(this);const l=doWatch(n,i.bind(r),o);return a?setCurrentInstance(a):unsetCurrentInstance(),l}function createPathGetter(e,t){const o=t.split(".");return()=>{let r=e;for(let n=0;n<o.length&&r;n++)r=r[o[n]];return r}}function traverse(e,t){if(!isObject$1(e)||e.__v_skip||(t=t||new Set,t.has(e)))return e;if(t.add(e),isRef(e))traverse(e.value,t);else if(isArray$2(e))for(let o=0;o<e.length;o++)traverse(e[o],t);else if(isSet(e)||isMap(e))e.forEach(o=>{traverse(o,t)});else if(isPlainObject$2(e))for(const o in e)traverse(e[o],t);return e}function withDirectives(e,t){const o=currentRenderingInstance;if(o===null)return e;const r=getExposeProxy(o)||o.proxy,n=e.dirs||(e.dirs=[]);for(let i=0;i<t.length;i++){let[a,l,s,d=EMPTY_OBJ]=t[i];a&&(isFunction$1(a)&&(a={mounted:a,updated:a}),a.deep&&traverse(l),n.push({dir:a,instance:r,value:l,oldValue:void 0,arg:s,modifiers:d}))}return e}function invokeDirectiveHook(e,t,o,r){const n=e.dirs,i=t&&t.dirs;for(let a=0;a<n.length;a++){const l=n[a];i&&(l.oldValue=i[a].value);let s=l.dir[r];s&&(pauseTracking(),callWithAsyncErrorHandling(s,o,8,[e.el,l,e,t]),resetTracking())}}const leaveCbKey=Symbol("_leaveCb"),enterCbKey$1=Symbol("_enterCb");function useTransitionState(){const e={isMounted:!1,isLeaving:!1,isUnmounting:!1,leavingVNodes:new Map};return onMounted(()=>{e.isMounted=!0}),onBeforeUnmount(()=>{e.isUnmounting=!0}),e}const TransitionHookValidator=[Function,Array],BaseTransitionPropsValidators={mode:String,appear:Boolean,persisted:Boolean,onBeforeEnter:TransitionHookValidator,onEnter:TransitionHookValidator,onAfterEnter:TransitionHookValidator,onEnterCancelled:TransitionHookValidator,onBeforeLeave:TransitionHookValidator,onLeave:TransitionHookValidator,onAfterLeave:TransitionHookValidator,onLeaveCancelled:TransitionHookValidator,onBeforeAppear:TransitionHookValidator,onAppear:TransitionHookValidator,onAfterAppear:TransitionHookValidator,onAppearCancelled:TransitionHookValidator},BaseTransitionImpl={name:"BaseTransition",props:BaseTransitionPropsValidators,setup(e,{slots:t}){const o=getCurrentInstance(),r=useTransitionState();let n;return()=>{const i=t.default&&getTransitionRawChildren(t.default(),!0);if(!i||!i.length)return;let a=i[0];if(i.length>1){for(const x of i)if(x.type!==Comment){a=x;break}}const l=toRaw(e),{mode:s}=l;if(r.isLeaving)return emptyPlaceholder(a);const d=getKeepAliveChild(a);if(!d)return emptyPlaceholder(a);const u=resolveTransitionHooks(d,l,r,o);setTransitionHooks(d,u);const f=o.subTree,m=f&&getKeepAliveChild(f);let v=!1;const{getTransitionKey:g}=d.type;if(g){const x=g();n===void 0?n=x:x!==n&&(n=x,v=!0)}if(m&&m.type!==Comment&&(!isSameVNodeType(d,m)||v)){const x=resolveTransitionHooks(m,l,r,o);if(setTransitionHooks(m,x),s==="out-in")return r.isLeaving=!0,x.afterLeave=()=>{r.isLeaving=!1,o.update.active!==!1&&o.update()},emptyPlaceholder(a);s==="in-out"&&d.type!==Comment&&(x.delayLeave=(C,y,I)=>{const N=getLeavingNodesForType(r,m);N[String(m.key)]=m,C[leaveCbKey]=()=>{y(),C[leaveCbKey]=void 0,delete u.delayedLeave},u.delayedLeave=I})}return a}}},BaseTransition=BaseTransitionImpl;function getLeavingNodesForType(e,t){const{leavingVNodes:o}=e;let r=o.get(t.type);return r||(r=Object.create(null),o.set(t.type,r)),r}function resolveTransitionHooks(e,t,o,r){const{appear:n,mode:i,persisted:a=!1,onBeforeEnter:l,onEnter:s,onAfterEnter:d,onEnterCancelled:u,onBeforeLeave:f,onLeave:m,onAfterLeave:v,onLeaveCancelled:g,onBeforeAppear:x,onAppear:C,onAfterAppear:y,onAppearCancelled:I}=t,N=String(e.key),E=getLeavingNodesForType(o,e),P=($,D)=>{$&&callWithAsyncErrorHandling($,r,9,D)},F=($,D)=>{const j=D[1];P($,D),isArray$2($)?$.every(K=>K.length<=1)&&j():$.length<=1&&j()},S={mode:i,persisted:a,beforeEnter($){let D=l;if(!o.isMounted)if(n)D=x||l;else return;$[leaveCbKey]&&$[leaveCbKey](!0);const j=E[N];j&&isSameVNodeType(e,j)&&j.el[leaveCbKey]&&j.el[leaveCbKey](),P(D,[$])},enter($){let D=s,j=d,K=u;if(!o.isMounted)if(n)D=C||s,j=y||d,K=I||u;else return;let z=!1;const ee=$[enterCbKey$1]=ae=>{z||(z=!0,ae?P(K,[$]):P(j,[$]),S.delayedLeave&&S.delayedLeave(),$[enterCbKey$1]=void 0)};D?F(D,[$,ee]):ee()},leave($,D){const j=String(e.key);if($[enterCbKey$1]&&$[enterCbKey$1](!0),o.isUnmounting)return D();P(f,[$]);let K=!1;const z=$[leaveCbKey]=ee=>{K||(K=!0,D(),ee?P(g,[$]):P(v,[$]),$[leaveCbKey]=void 0,E[j]===e&&delete E[j])};E[j]=e,m?F(m,[$,z]):z()},clone($){return resolveTransitionHooks($,t,o,r)}};return S}function emptyPlaceholder(e){if(isKeepAlive(e))return e=cloneVNode(e),e.children=null,e}function getKeepAliveChild(e){return isKeepAlive(e)?e.children?e.children[0]:void 0:e}function setTransitionHooks(e,t){e.shapeFlag&6&&e.component?setTransitionHooks(e.component.subTree,t):e.shapeFlag&128?(e.ssContent.transition=t.clone(e.ssContent),e.ssFallback.transition=t.clone(e.ssFallback)):e.transition=t}function getTransitionRawChildren(e,t=!1,o){let r=[],n=0;for(let i=0;i<e.length;i++){let a=e[i];const l=o==null?a.key:String(o)+String(a.key!=null?a.key:i);a.type===Fragment?(a.patchFlag&128&&n++,r=r.concat(getTransitionRawChildren(a.children,t,l))):(t||a.type!==Comment)&&r.push(l!=null?cloneVNode(a,{key:l}):a)}if(n>1)for(let i=0;i<r.length;i++)r[i].patchFlag=-2;return r}/*! #__NO_SIDE_EFFECTS__ */function defineComponent(e,t){return isFunction$1(e)?extend({name:e.name},t,{setup:e}):e}const isAsyncWrapper=e=>!!e.type.__asyncLoader,isKeepAlive=e=>e.type.__isKeepAlive;function onActivated(e,t){registerKeepAliveHook(e,"a",t)}function onDeactivated(e,t){registerKeepAliveHook(e,"da",t)}function registerKeepAliveHook(e,t,o=currentInstance){const r=e.__wdc||(e.__wdc=()=>{let n=o;for(;n;){if(n.isDeactivated)return;n=n.parent}return e()});if(injectHook(t,r,o),o){let n=o.parent;for(;n&&n.parent;)isKeepAlive(n.parent.vnode)&&injectToKeepAliveRoot(r,t,o,n),n=n.parent}}function injectToKeepAliveRoot(e,t,o,r){const n=injectHook(t,e,r,!0);onUnmounted(()=>{remove(r[t],n)},o)}function injectHook(e,t,o=currentInstance,r=!1){if(o){const n=o[e]||(o[e]=[]),i=t.__weh||(t.__weh=(...a)=>{if(o.isUnmounted)return;pauseTracking(),setCurrentInstance(o);const l=callWithAsyncErrorHandling(t,o,e,a);return unsetCurrentInstance(),resetTracking(),l});return r?n.unshift(i):n.push(i),i}}const createHook=e=>(t,o=currentInstance)=>(!isInSSRComponentSetup||e==="sp")&&injectHook(e,(...r)=>t(...r),o),onBeforeMount=createHook("bm"),onMounted=createHook("m"),onBeforeUpdate=createHook("bu"),onUpdated=createHook("u"),onBeforeUnmount=createHook("bum"),onUnmounted=createHook("um"),onServerPrefetch=createHook("sp"),onRenderTriggered=createHook("rtg"),onRenderTracked=createHook("rtc");function onErrorCaptured(e,t=currentInstance){injectHook("ec",e,t)}function renderList(e,t,o,r){let n;const i=o&&o[r];if(isArray$2(e)||isString(e)){n=new Array(e.length);for(let a=0,l=e.length;a<l;a++)n[a]=t(e[a],a,void 0,i&&i[a])}else if(typeof e=="number"){n=new Array(e);for(let a=0;a<e;a++)n[a]=t(a+1,a,void 0,i&&i[a])}else if(isObject$1(e))if(e[Symbol.iterator])n=Array.from(e,(a,l)=>t(a,l,void 0,i&&i[l]));else{const a=Object.keys(e);n=new Array(a.length);for(let l=0,s=a.length;l<s;l++){const d=a[l];n[l]=t(e[d],d,l,i&&i[l])}}else n=[];return o&&(o[r]=n),n}function renderSlot(e,t,o={},r,n){if(currentRenderingInstance.isCE||currentRenderingInstance.parent&&isAsyncWrapper(currentRenderingInstance.parent)&&currentRenderingInstance.parent.isCE)return t!=="default"&&(o.name=t),createVNode("slot",o,r&&r());let i=e[t];i&&i._c&&(i._d=!1),openBlock();const a=i&&ensureValidVNode$1(i(o)),l=createBlock(Fragment,{key:o.key||a&&a.key||`_${t}`},a||(r?r():[]),a&&e._===1?64:-2);return!n&&l.scopeId&&(l.slotScopeIds=[l.scopeId+"-s"]),i&&i._c&&(i._d=!0),l}function ensureValidVNode$1(e){return e.some(t=>isVNode(t)?!(t.type===Comment||t.type===Fragment&&!ensureValidVNode$1(t.children)):!0)?e:null}const getPublicInstance=e=>e?isStatefulComponent(e)?getExposeProxy(e)||e.proxy:getPublicInstance(e.parent):null,publicPropertiesMap=extend(Object.create(null),{$:e=>e,$el:e=>e.vnode.el,$data:e=>e.data,$props:e=>e.props,$attrs:e=>e.attrs,$slots:e=>e.slots,$refs:e=>e.refs,$parent:e=>getPublicInstance(e.parent),$root:e=>getPublicInstance(e.root),$emit:e=>e.emit,$options:e=>resolveMergedOptions(e),$forceUpdate:e=>e.f||(e.f=()=>queueJob(e.update)),$nextTick:e=>e.n||(e.n=nextTick.bind(e.proxy)),$watch:e=>instanceWatch.bind(e)}),hasSetupBinding=(e,t)=>e!==EMPTY_OBJ&&!e.__isScriptSetup&&hasOwn(e,t),PublicInstanceProxyHandlers={get({_:e},t){const{ctx:o,setupState:r,data:n,props:i,accessCache:a,type:l,appContext:s}=e;let d;if(t[0]!=="$"){const v=a[t];if(v!==void 0)switch(v){case 1:return r[t];case 2:return n[t];case 4:return o[t];case 3:return i[t]}else{if(hasSetupBinding(r,t))return a[t]=1,r[t];if(n!==EMPTY_OBJ&&hasOwn(n,t))return a[t]=2,n[t];if((d=e.propsOptions[0])&&hasOwn(d,t))return a[t]=3,i[t];if(o!==EMPTY_OBJ&&hasOwn(o,t))return a[t]=4,o[t];shouldCacheAccess&&(a[t]=0)}}const u=publicPropertiesMap[t];let f,m;if(u)return t==="$attrs"&&track(e,"get",t),u(e);if((f=l.__cssModules)&&(f=f[t]))return f;if(o!==EMPTY_OBJ&&hasOwn(o,t))return a[t]=4,o[t];if(m=s.config.globalProperties,hasOwn(m,t))return m[t]},set({_:e},t,o){const{data:r,setupState:n,ctx:i}=e;return hasSetupBinding(n,t)?(n[t]=o,!0):r!==EMPTY_OBJ&&hasOwn(r,t)?(r[t]=o,!0):hasOwn(e.props,t)||t[0]==="$"&&t.slice(1)in e?!1:(i[t]=o,!0)},has({_:{data:e,setupState:t,accessCache:o,ctx:r,appContext:n,propsOptions:i}},a){let l;return!!o[a]||e!==EMPTY_OBJ&&hasOwn(e,a)||hasSetupBinding(t,a)||(l=i[0])&&hasOwn(l,a)||hasOwn(r,a)||hasOwn(publicPropertiesMap,a)||hasOwn(n.config.globalProperties,a)},defineProperty(e,t,o){return o.get!=null?e._.accessCache[t]=0:hasOwn(o,"value")&&this.set(e,t,o.value,null),Reflect.defineProperty(e,t,o)}};function normalizePropsOrEmits(e){return isArray$2(e)?e.reduce((t,o)=>(t[o]=null,t),{}):e}let shouldCacheAccess=!0;function applyOptions(e){const t=resolveMergedOptions(e),o=e.proxy,r=e.ctx;shouldCacheAccess=!1,t.beforeCreate&&callHook$1(t.beforeCreate,e,"bc");const{data:n,computed:i,methods:a,watch:l,provide:s,inject:d,created:u,beforeMount:f,mounted:m,beforeUpdate:v,updated:g,activated:x,deactivated:C,beforeDestroy:y,beforeUnmount:I,destroyed:N,unmounted:E,render:P,renderTracked:F,renderTriggered:S,errorCaptured:$,serverPrefetch:D,expose:j,inheritAttrs:K,components:z,directives:ee,filters:ae}=t;if(d&&resolveInjections(d,r,null),a)for(const q in a){const ne=a[q];isFunction$1(ne)&&(r[q]=ne.bind(o))}if(n){const q=n.call(o,o);isObject$1(q)&&(e.data=reactive(q))}if(shouldCacheAccess=!0,i)for(const q in i){const ne=i[q],he=isFunction$1(ne)?ne.bind(o,o):isFunction$1(ne.get)?ne.get.bind(o,o):NOOP,ge=!isFunction$1(ne)&&isFunction$1(ne.set)?ne.set.bind(o):NOOP,ve=computed({get:he,set:ge});Object.defineProperty(r,q,{enumerable:!0,configurable:!0,get:()=>ve.value,set:te=>ve.value=te})}if(l)for(const q in l)createWatcher(l[q],r,o,q);if(s){const q=isFunction$1(s)?s.call(o):s;Reflect.ownKeys(q).forEach(ne=>{provide(ne,q[ne])})}u&&callHook$1(u,e,"c");function Z(q,ne){isArray$2(ne)?ne.forEach(he=>q(he.bind(o))):ne&&q(ne.bind(o))}if(Z(onBeforeMount,f),Z(onMounted,m),Z(onBeforeUpdate,v),Z(onUpdated,g),Z(onActivated,x),Z(onDeactivated,C),Z(onErrorCaptured,$),Z(onRenderTracked,F),Z(onRenderTriggered,S),Z(onBeforeUnmount,I),Z(onUnmounted,E),Z(onServerPrefetch,D),isArray$2(j))if(j.length){const q=e.exposed||(e.exposed={});j.forEach(ne=>{Object.defineProperty(q,ne,{get:()=>o[ne],set:he=>o[ne]=he})})}else e.exposed||(e.exposed={});P&&e.render===NOOP&&(e.render=P),K!=null&&(e.inheritAttrs=K),z&&(e.components=z),ee&&(e.directives=ee)}function resolveInjections(e,t,o=NOOP){isArray$2(e)&&(e=normalizeInject(e));for(const r in e){const n=e[r];let i;isObject$1(n)?"default"in n?i=inject(n.from||r,n.default,!0):i=inject(n.from||r):i=inject(n),isRef(i)?Object.defineProperty(t,r,{enumerable:!0,configurable:!0,get:()=>i.value,set:a=>i.value=a}):t[r]=i}}function callHook$1(e,t,o){callWithAsyncErrorHandling(isArray$2(e)?e.map(r=>r.bind(t.proxy)):e.bind(t.proxy),t,o)}function createWatcher(e,t,o,r){const n=r.includes(".")?createPathGetter(o,r):()=>o[r];if(isString(e)){const i=t[e];isFunction$1(i)&&watch(n,i)}else if(isFunction$1(e))watch(n,e.bind(o));else if(isObject$1(e))if(isArray$2(e))e.forEach(i=>createWatcher(i,t,o,r));else{const i=isFunction$1(e.handler)?e.handler.bind(o):t[e.handler];isFunction$1(i)&&watch(n,i,e)}}function resolveMergedOptions(e){const t=e.type,{mixins:o,extends:r}=t,{mixins:n,optionsCache:i,config:{optionMergeStrategies:a}}=e.appContext,l=i.get(t);let s;return l?s=l:!n.length&&!o&&!r?s=t:(s={},n.length&&n.forEach(d=>mergeOptions(s,d,a,!0)),mergeOptions(s,t,a)),isObject$1(t)&&i.set(t,s),s}function mergeOptions(e,t,o,r=!1){const{mixins:n,extends:i}=t;i&&mergeOptions(e,i,o,!0),n&&n.forEach(a=>mergeOptions(e,a,o,!0));for(const a in t)if(!(r&&a==="expose")){const l=internalOptionMergeStrats[a]||o&&o[a];e[a]=l?l(e[a],t[a]):t[a]}return e}const internalOptionMergeStrats={data:mergeDataFn,props:mergeEmitsOrPropsOptions,emits:mergeEmitsOrPropsOptions,methods:mergeObjectOptions,computed:mergeObjectOptions,beforeCreate:mergeAsArray,created:mergeAsArray,beforeMount:mergeAsArray,mounted:mergeAsArray,beforeUpdate:mergeAsArray,updated:mergeAsArray,beforeDestroy:mergeAsArray,beforeUnmount:mergeAsArray,destroyed:mergeAsArray,unmounted:mergeAsArray,activated:mergeAsArray,deactivated:mergeAsArray,errorCaptured:mergeAsArray,serverPrefetch:mergeAsArray,components:mergeObjectOptions,directives:mergeObjectOptions,watch:mergeWatchOptions,provide:mergeDataFn,inject:mergeInject};function mergeDataFn(e,t){return t?e?function(){return extend(isFunction$1(e)?e.call(this,this):e,isFunction$1(t)?t.call(this,this):t)}:t:e}function mergeInject(e,t){return mergeObjectOptions(normalizeInject(e),normalizeInject(t))}function normalizeInject(e){if(isArray$2(e)){const t={};for(let o=0;o<e.length;o++)t[e[o]]=e[o];return t}return e}function mergeAsArray(e,t){return e?[...new Set([].concat(e,t))]:t}function mergeObjectOptions(e,t){return e?extend(Object.create(null),e,t):t}function mergeEmitsOrPropsOptions(e,t){return e?isArray$2(e)&&isArray$2(t)?[...new Set([...e,...t])]:extend(Object.create(null),normalizePropsOrEmits(e),normalizePropsOrEmits(t??{})):t}function mergeWatchOptions(e,t){if(!e)return t;if(!t)return e;const o=extend(Object.create(null),e);for(const r in t)o[r]=mergeAsArray(e[r],t[r]);return o}function createAppContext(){return{app:null,config:{isNativeTag:NO,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let uid$1=0;function createAppAPI(e,t){return function(r,n=null){isFunction$1(r)||(r=extend({},r)),n!=null&&!isObject$1(n)&&(n=null);const i=createAppContext(),a=new WeakSet;let l=!1;const s=i.app={_uid:uid$1++,_component:r,_props:n,_container:null,_context:i,_instance:null,version,get config(){return i.config},set config(d){},use(d,...u){return a.has(d)||(d&&isFunction$1(d.install)?(a.add(d),d.install(s,...u)):isFunction$1(d)&&(a.add(d),d(s,...u))),s},mixin(d){return i.mixins.includes(d)||i.mixins.push(d),s},component(d,u){return u?(i.components[d]=u,s):i.components[d]},directive(d,u){return u?(i.directives[d]=u,s):i.directives[d]},mount(d,u,f){if(!l){const m=createVNode(r,n);return m.appContext=i,u&&t?t(m,d):e(m,d,f),l=!0,s._container=d,d.__vue_app__=s,getExposeProxy(m.component)||m.component.proxy}},unmount(){l&&(e(null,s._container),delete s._container.__vue_app__)},provide(d,u){return i.provides[d]=u,s},runWithContext(d){currentApp=s;try{return d()}finally{currentApp=null}}};return s}}let currentApp=null;function provide(e,t){if(currentInstance){let o=currentInstance.provides;const r=currentInstance.parent&&currentInstance.parent.provides;r===o&&(o=currentInstance.provides=Object.create(r)),o[e]=t}}function inject(e,t,o=!1){const r=currentInstance||currentRenderingInstance;if(r||currentApp){const n=r?r.parent==null?r.vnode.appContext&&r.vnode.appContext.provides:r.parent.provides:currentApp._context.provides;if(n&&e in n)return n[e];if(arguments.length>1)return o&&isFunction$1(t)?t.call(r&&r.proxy):t}}function hasInjectionContext(){return!!(currentInstance||currentRenderingInstance||currentApp)}function initProps(e,t,o,r=!1){const n={},i={};def(i,InternalObjectKey,1),e.propsDefaults=Object.create(null),setFullProps(e,t,n,i);for(const a in e.propsOptions[0])a in n||(n[a]=void 0);o?e.props=r?n:shallowReactive(n):e.type.props?e.props=n:e.props=i,e.attrs=i}function updateProps(e,t,o,r){const{props:n,attrs:i,vnode:{patchFlag:a}}=e,l=toRaw(n),[s]=e.propsOptions;let d=!1;if((r||a>0)&&!(a&16)){if(a&8){const u=e.vnode.dynamicProps;for(let f=0;f<u.length;f++){let m=u[f];if(isEmitListener(e.emitsOptions,m))continue;const v=t[m];if(s)if(hasOwn(i,m))v!==i[m]&&(i[m]=v,d=!0);else{const g=camelize(m);n[g]=resolvePropValue(s,l,g,v,e,!1)}else v!==i[m]&&(i[m]=v,d=!0)}}}else{setFullProps(e,t,n,i)&&(d=!0);let u;for(const f in l)(!t||!hasOwn(t,f)&&((u=hyphenate(f))===f||!hasOwn(t,u)))&&(s?o&&(o[f]!==void 0||o[u]!==void 0)&&(n[f]=resolvePropValue(s,l,f,void 0,e,!0)):delete n[f]);if(i!==l)for(const f in i)(!t||!hasOwn(t,f))&&(delete i[f],d=!0)}d&&trigger$1(e,"set","$attrs")}function setFullProps(e,t,o,r){const[n,i]=e.propsOptions;let a=!1,l;if(t)for(let s in t){if(isReservedProp(s))continue;const d=t[s];let u;n&&hasOwn(n,u=camelize(s))?!i||!i.includes(u)?o[u]=d:(l||(l={}))[u]=d:isEmitListener(e.emitsOptions,s)||(!(s in r)||d!==r[s])&&(r[s]=d,a=!0)}if(i){const s=toRaw(o),d=l||EMPTY_OBJ;for(let u=0;u<i.length;u++){const f=i[u];o[f]=resolvePropValue(n,s,f,d[f],e,!hasOwn(d,f))}}return a}function resolvePropValue(e,t,o,r,n,i){const a=e[o];if(a!=null){const l=hasOwn(a,"default");if(l&&r===void 0){const s=a.default;if(a.type!==Function&&!a.skipFactory&&isFunction$1(s)){const{propsDefaults:d}=n;o in d?r=d[o]:(setCurrentInstance(n),r=d[o]=s.call(null,t),unsetCurrentInstance())}else r=s}a[0]&&(i&&!l?r=!1:a[1]&&(r===""||r===hyphenate(o))&&(r=!0))}return r}function normalizePropsOptions(e,t,o=!1){const r=t.propsCache,n=r.get(e);if(n)return n;const i=e.props,a={},l=[];let s=!1;if(!isFunction$1(e)){const u=f=>{s=!0;const[m,v]=normalizePropsOptions(f,t,!0);extend(a,m),v&&l.push(...v)};!o&&t.mixins.length&&t.mixins.forEach(u),e.extends&&u(e.extends),e.mixins&&e.mixins.forEach(u)}if(!i&&!s)return isObject$1(e)&&r.set(e,EMPTY_ARR),EMPTY_ARR;if(isArray$2(i))for(let u=0;u<i.length;u++){const f=camelize(i[u]);validatePropName(f)&&(a[f]=EMPTY_OBJ)}else if(i)for(const u in i){const f=camelize(u);if(validatePropName(f)){const m=i[u],v=a[f]=isArray$2(m)||isFunction$1(m)?{type:m}:extend({},m);if(v){const g=getTypeIndex(Boolean,v.type),x=getTypeIndex(String,v.type);v[0]=g>-1,v[1]=x<0||g<x,(g>-1||hasOwn(v,"default"))&&l.push(f)}}}const d=[a,l];return isObject$1(e)&&r.set(e,d),d}function validatePropName(e){return e[0]!=="$"}function getType(e){const t=e&&e.toString().match(/^\s*(function|class) (\w+)/);return t?t[2]:e===null?"null":""}function isSameType(e,t){return getType(e)===getType(t)}function getTypeIndex(e,t){return isArray$2(t)?t.findIndex(o=>isSameType(o,e)):isFunction$1(t)&&isSameType(t,e)?0:-1}const isInternalKey=e=>e[0]==="_"||e==="$stable",normalizeSlotValue=e=>isArray$2(e)?e.map(normalizeVNode):[normalizeVNode(e)],normalizeSlot=(e,t,o)=>{if(t._n)return t;const r=withCtx((...n)=>normalizeSlotValue(t(...n)),o);return r._c=!1,r},normalizeObjectSlots=(e,t,o)=>{const r=e._ctx;for(const n in e){if(isInternalKey(n))continue;const i=e[n];if(isFunction$1(i))t[n]=normalizeSlot(n,i,r);else if(i!=null){const a=normalizeSlotValue(i);t[n]=()=>a}}},normalizeVNodeSlots=(e,t)=>{const o=normalizeSlotValue(t);e.slots.default=()=>o},initSlots=(e,t)=>{if(e.vnode.shapeFlag&32){const o=t._;o?(e.slots=toRaw(t),def(t,"_",o)):normalizeObjectSlots(t,e.slots={})}else e.slots={},t&&normalizeVNodeSlots(e,t);def(e.slots,InternalObjectKey,1)},updateSlots=(e,t,o)=>{const{vnode:r,slots:n}=e;let i=!0,a=EMPTY_OBJ;if(r.shapeFlag&32){const l=t._;l?o&&l===1?i=!1:(extend(n,t),!o&&l===1&&delete n._):(i=!t.$stable,normalizeObjectSlots(t,n)),a=t}else t&&(normalizeVNodeSlots(e,t),a={default:1});if(i)for(const l in n)!isInternalKey(l)&&a[l]==null&&delete n[l]};function setRef(e,t,o,r,n=!1){if(isArray$2(e)){e.forEach((m,v)=>setRef(m,t&&(isArray$2(t)?t[v]:t),o,r,n));return}if(isAsyncWrapper(r)&&!n)return;const i=r.shapeFlag&4?getExposeProxy(r.component)||r.component.proxy:r.el,a=n?null:i,{i:l,r:s}=e,d=t&&t.r,u=l.refs===EMPTY_OBJ?l.refs={}:l.refs,f=l.setupState;if(d!=null&&d!==s&&(isString(d)?(u[d]=null,hasOwn(f,d)&&(f[d]=null)):isRef(d)&&(d.value=null)),isFunction$1(s))callWithErrorHandling(s,l,12,[a,u]);else{const m=isString(s),v=isRef(s);if(m||v){const g=()=>{if(e.f){const x=m?hasOwn(f,s)?f[s]:u[s]:s.value;n?isArray$2(x)&&remove(x,i):isArray$2(x)?x.includes(i)||x.push(i):m?(u[s]=[i],hasOwn(f,s)&&(f[s]=u[s])):(s.value=[i],e.k&&(u[e.k]=s.value))}else m?(u[s]=a,hasOwn(f,s)&&(f[s]=a)):v&&(s.value=a,e.k&&(u[e.k]=a))};a?(g.id=-1,queuePostRenderEffect(g,o)):g()}}}const queuePostRenderEffect=queueEffectWithSuspense;function createRenderer(e){return baseCreateRenderer(e)}function baseCreateRenderer(e,t){const o=getGlobalThis();o.__VUE__=!0;const{insert:r,remove:n,patchProp:i,createElement:a,createText:l,createComment:s,setText:d,setElementText:u,parentNode:f,nextSibling:m,setScopeId:v=NOOP,insertStaticContent:g}=e,x=(b,w,R,H=null,L=null,k=null,O=!1,B=null,A=!!w.dynamicChildren)=>{if(b===w)return;b&&!isSameVNodeType(b,w)&&(H=le(b),te(b,L,k,!0),b=null),w.patchFlag===-2&&(A=!1,w.dynamicChildren=null);const{type:M,ref:Y,shapeFlag:J}=w;switch(M){case Text:C(b,w,R,H);break;case Comment:y(b,w,R,H);break;case Static:b==null&&I(w,R,H,O);break;case Fragment:z(b,w,R,H,L,k,O,B,A);break;default:J&1?P(b,w,R,H,L,k,O,B,A):J&6?ee(b,w,R,H,L,k,O,B,A):(J&64||J&128)&&M.process(b,w,R,H,L,k,O,B,A,ce)}Y!=null&&L&&setRef(Y,b&&b.ref,k,w||b,!w)},C=(b,w,R,H)=>{if(b==null)r(w.el=l(w.children),R,H);else{const L=w.el=b.el;w.children!==b.children&&d(L,w.children)}},y=(b,w,R,H)=>{b==null?r(w.el=s(w.children||""),R,H):w.el=b.el},I=(b,w,R,H)=>{[b.el,b.anchor]=g(b.children,w,R,H,b.el,b.anchor)},N=({el:b,anchor:w},R,H)=>{let L;for(;b&&b!==w;)L=m(b),r(b,R,H),b=L;r(w,R,H)},E=({el:b,anchor:w})=>{let R;for(;b&&b!==w;)R=m(b),n(b),b=R;n(w)},P=(b,w,R,H,L,k,O,B,A)=>{O=O||w.type==="svg",b==null?F(w,R,H,L,k,O,B,A):D(b,w,L,k,O,B,A)},F=(b,w,R,H,L,k,O,B)=>{let A,M;const{type:Y,props:J,shapeFlag:X,transition:ie,dirs:fe}=b;if(A=b.el=a(b.type,k,J&&J.is,J),X&8?u(A,b.children):X&16&&$(b.children,A,null,H,L,k&&Y!=="foreignObject",O,B),fe&&invokeDirectiveHook(b,null,H,"created"),S(A,b,b.scopeId,O,H),J){for(const me in J)me!=="value"&&!isReservedProp(me)&&i(A,me,null,J[me],k,b.children,H,L,U);"value"in J&&i(A,"value",null,J.value),(M=J.onVnodeBeforeMount)&&invokeVNodeHook(M,H,b)}fe&&invokeDirectiveHook(b,null,H,"beforeMount");const pe=needTransition(L,ie);pe&&ie.beforeEnter(A),r(A,w,R),((M=J&&J.onVnodeMounted)||pe||fe)&&queuePostRenderEffect(()=>{M&&invokeVNodeHook(M,H,b),pe&&ie.enter(A),fe&&invokeDirectiveHook(b,null,H,"mounted")},L)},S=(b,w,R,H,L)=>{if(R&&v(b,R),H)for(let k=0;k<H.length;k++)v(b,H[k]);if(L){let k=L.subTree;if(w===k){const O=L.vnode;S(b,O,O.scopeId,O.slotScopeIds,L.parent)}}},$=(b,w,R,H,L,k,O,B,A=0)=>{for(let M=A;M<b.length;M++){const Y=b[M]=B?cloneIfMounted(b[M]):normalizeVNode(b[M]);x(null,Y,w,R,H,L,k,O,B)}},D=(b,w,R,H,L,k,O)=>{const B=w.el=b.el;let{patchFlag:A,dynamicChildren:M,dirs:Y}=w;A|=b.patchFlag&16;const J=b.props||EMPTY_OBJ,X=w.props||EMPTY_OBJ;let ie;R&&toggleRecurse(R,!1),(ie=X.onVnodeBeforeUpdate)&&invokeVNodeHook(ie,R,w,b),Y&&invokeDirectiveHook(w,b,R,"beforeUpdate"),R&&toggleRecurse(R,!0);const fe=L&&w.type!=="foreignObject";if(M?j(b.dynamicChildren,M,B,R,H,fe,k):O||ne(b,w,B,null,R,H,fe,k,!1),A>0){if(A&16)K(B,w,J,X,R,H,L);else if(A&2&&J.class!==X.class&&i(B,"class",null,X.class,L),A&4&&i(B,"style",J.style,X.style,L),A&8){const pe=w.dynamicProps;for(let me=0;me<pe.length;me++){const ye=pe[me],ke=J[ye],Re=X[ye];(Re!==ke||ye==="value")&&i(B,ye,ke,Re,L,b.children,R,H,U)}}A&1&&b.children!==w.children&&u(B,w.children)}else!O&&M==null&&K(B,w,J,X,R,H,L);((ie=X.onVnodeUpdated)||Y)&&queuePostRenderEffect(()=>{ie&&invokeVNodeHook(ie,R,w,b),Y&&invokeDirectiveHook(w,b,R,"updated")},H)},j=(b,w,R,H,L,k,O)=>{for(let B=0;B<w.length;B++){const A=b[B],M=w[B],Y=A.el&&(A.type===Fragment||!isSameVNodeType(A,M)||A.shapeFlag&70)?f(A.el):R;x(A,M,Y,null,H,L,k,O,!0)}},K=(b,w,R,H,L,k,O)=>{if(R!==H){if(R!==EMPTY_OBJ)for(const B in R)!isReservedProp(B)&&!(B in H)&&i(b,B,R[B],null,O,w.children,L,k,U);for(const B in H){if(isReservedProp(B))continue;const A=H[B],M=R[B];A!==M&&B!=="value"&&i(b,B,M,A,O,w.children,L,k,U)}"value"in H&&i(b,"value",R.value,H.value)}},z=(b,w,R,H,L,k,O,B,A)=>{const M=w.el=b?b.el:l(""),Y=w.anchor=b?b.anchor:l("");let{patchFlag:J,dynamicChildren:X,slotScopeIds:ie}=w;ie&&(B=B?B.concat(ie):ie),b==null?(r(M,R,H),r(Y,R,H),$(w.children,R,Y,L,k,O,B,A)):J>0&&J&64&&X&&b.dynamicChildren?(j(b.dynamicChildren,X,R,L,k,O,B),(w.key!=null||L&&w===L.subTree)&&traverseStaticChildren(b,w,!0)):ne(b,w,R,Y,L,k,O,B,A)},ee=(b,w,R,H,L,k,O,B,A)=>{w.slotScopeIds=B,b==null?w.shapeFlag&512?L.ctx.activate(w,R,H,O,A):ae(w,R,H,L,k,O,A):se(b,w,A)},ae=(b,w,R,H,L,k,O)=>{const B=b.component=createComponentInstance(b,H,L);if(isKeepAlive(b)&&(B.ctx.renderer=ce),setupComponent(B),B.asyncDep){if(L&&L.registerDep(B,Z),!b.el){const A=B.subTree=createVNode(Comment);y(null,A,w,R)}return}Z(B,b,w,R,L,k,O)},se=(b,w,R)=>{const H=w.component=b.component;if(shouldUpdateComponent(b,w,R))if(H.asyncDep&&!H.asyncResolved){q(H,w,R);return}else H.next=w,invalidateJob(H.update),H.update();else w.el=b.el,H.vnode=w},Z=(b,w,R,H,L,k,O)=>{const B=()=>{if(b.isMounted){let{next:Y,bu:J,u:X,parent:ie,vnode:fe}=b,pe=Y,me;toggleRecurse(b,!1),Y?(Y.el=fe.el,q(b,Y,O)):Y=fe,J&&invokeArrayFns(J),(me=Y.props&&Y.props.onVnodeBeforeUpdate)&&invokeVNodeHook(me,ie,Y,fe),toggleRecurse(b,!0);const ye=renderComponentRoot(b),ke=b.subTree;b.subTree=ye,x(ke,ye,f(ke.el),le(ke),b,L,k),Y.el=ye.el,pe===null&&updateHOCHostEl(b,ye.el),X&&queuePostRenderEffect(X,L),(me=Y.props&&Y.props.onVnodeUpdated)&&queuePostRenderEffect(()=>invokeVNodeHook(me,ie,Y,fe),L)}else{let Y;const{el:J,props:X}=w,{bm:ie,m:fe,parent:pe}=b,me=isAsyncWrapper(w);if(toggleRecurse(b,!1),ie&&invokeArrayFns(ie),!me&&(Y=X&&X.onVnodeBeforeMount)&&invokeVNodeHook(Y,pe,w),toggleRecurse(b,!0),J&&De){const ye=()=>{b.subTree=renderComponentRoot(b),De(J,b.subTree,b,L,null)};me?w.type.__asyncLoader().then(()=>!b.isUnmounted&&ye()):ye()}else{const ye=b.subTree=renderComponentRoot(b);x(null,ye,R,H,b,L,k),w.el=ye.el}if(fe&&queuePostRenderEffect(fe,L),!me&&(Y=X&&X.onVnodeMounted)){const ye=w;queuePostRenderEffect(()=>invokeVNodeHook(Y,pe,ye),L)}(w.shapeFlag&256||pe&&isAsyncWrapper(pe.vnode)&&pe.vnode.shapeFlag&256)&&b.a&&queuePostRenderEffect(b.a,L),b.isMounted=!0,w=R=H=null}},A=b.effect=new ReactiveEffect(B,()=>queueJob(M),b.scope),M=b.update=()=>A.run();M.id=b.uid,toggleRecurse(b,!0),M()},q=(b,w,R)=>{w.component=b;const H=b.vnode.props;b.vnode=w,b.next=null,updateProps(b,w.props,H,R),updateSlots(b,w.children,R),pauseTracking(),flushPreFlushCbs(b),resetTracking()},ne=(b,w,R,H,L,k,O,B,A=!1)=>{const M=b&&b.children,Y=b?b.shapeFlag:0,J=w.children,{patchFlag:X,shapeFlag:ie}=w;if(X>0){if(X&128){ge(M,J,R,H,L,k,O,B,A);return}else if(X&256){he(M,J,R,H,L,k,O,B,A);return}}ie&8?(Y&16&&U(M,L,k),J!==M&&u(R,J)):Y&16?ie&16?ge(M,J,R,H,L,k,O,B,A):U(M,L,k,!0):(Y&8&&u(R,""),ie&16&&$(J,R,H,L,k,O,B,A))},he=(b,w,R,H,L,k,O,B,A)=>{b=b||EMPTY_ARR,w=w||EMPTY_ARR;const M=b.length,Y=w.length,J=Math.min(M,Y);let X;for(X=0;X<J;X++){const ie=w[X]=A?cloneIfMounted(w[X]):normalizeVNode(w[X]);x(b[X],ie,R,null,L,k,O,B,A)}M>Y?U(b,L,k,!0,!1,J):$(w,R,H,L,k,O,B,A,J)},ge=(b,w,R,H,L,k,O,B,A)=>{let M=0;const Y=w.length;let J=b.length-1,X=Y-1;for(;M<=J&&M<=X;){const ie=b[M],fe=w[M]=A?cloneIfMounted(w[M]):normalizeVNode(w[M]);if(isSameVNodeType(ie,fe))x(ie,fe,R,null,L,k,O,B,A);else break;M++}for(;M<=J&&M<=X;){const ie=b[J],fe=w[X]=A?cloneIfMounted(w[X]):normalizeVNode(w[X]);if(isSameVNodeType(ie,fe))x(ie,fe,R,null,L,k,O,B,A);else break;J--,X--}if(M>J){if(M<=X){const ie=X+1,fe=ie<Y?w[ie].el:H;for(;M<=X;)x(null,w[M]=A?cloneIfMounted(w[M]):normalizeVNode(w[M]),R,fe,L,k,O,B,A),M++}}else if(M>X)for(;M<=J;)te(b[M],L,k,!0),M++;else{const ie=M,fe=M,pe=new Map;for(M=fe;M<=X;M++){const Pe=w[M]=A?cloneIfMounted(w[M]):normalizeVNode(w[M]);Pe.key!=null&&pe.set(Pe.key,M)}let me,ye=0;const ke=X-fe+1;let Re=!1,Ne=0;const Be=new Array(ke);for(M=0;M<ke;M++)Be[M]=0;for(M=ie;M<=J;M++){const Pe=b[M];if(ye>=ke){te(Pe,L,k,!0);continue}let V;if(Pe.key!=null)V=pe.get(Pe.key);else for(me=fe;me<=X;me++)if(Be[me-fe]===0&&isSameVNodeType(Pe,w[me])){V=me;break}V===void 0?te(Pe,L,k,!0):(Be[V-fe]=M+1,V>=Ne?Ne=V:Re=!0,x(Pe,w[V],R,null,L,k,O,B,A),ye++)}const _e=Re?getSequence(Be):EMPTY_ARR;for(me=_e.length-1,M=ke-1;M>=0;M--){const Pe=fe+M,V=w[Pe],re=Pe+1<Y?w[Pe+1].el:H;Be[M]===0?x(null,V,R,re,L,k,O,B,A):Re&&(me<0||M!==_e[me]?ve(V,R,re,2):me--)}}},ve=(b,w,R,H,L=null)=>{const{el:k,type:O,transition:B,children:A,shapeFlag:M}=b;if(M&6){ve(b.component.subTree,w,R,H);return}if(M&128){b.suspense.move(w,R,H);return}if(M&64){O.move(b,w,R,ce);return}if(O===Fragment){r(k,w,R);for(let J=0;J<A.length;J++)ve(A[J],w,R,H);r(b.anchor,w,R);return}if(O===Static){N(b,w,R);return}if(H!==2&&M&1&&B)if(H===0)B.beforeEnter(k),r(k,w,R),queuePostRenderEffect(()=>B.enter(k),L);else{const{leave:J,delayLeave:X,afterLeave:ie}=B,fe=()=>r(k,w,R),pe=()=>{J(k,()=>{fe(),ie&&ie()})};X?X(k,fe,pe):pe()}else r(k,w,R)},te=(b,w,R,H=!1,L=!1)=>{const{type:k,props:O,ref:B,children:A,dynamicChildren:M,shapeFlag:Y,patchFlag:J,dirs:X}=b;if(B!=null&&setRef(B,null,R,b,!0),Y&256){w.ctx.deactivate(b);return}const ie=Y&1&&X,fe=!isAsyncWrapper(b);let pe;if(fe&&(pe=O&&O.onVnodeBeforeUnmount)&&invokeVNodeHook(pe,w,b),Y&6)oe(b.component,R,H);else{if(Y&128){b.suspense.unmount(R,H);return}ie&&invokeDirectiveHook(b,null,w,"beforeUnmount"),Y&64?b.type.remove(b,w,R,L,ce,H):M&&(k!==Fragment||J>0&&J&64)?U(M,w,R,!1,!0):(k===Fragment&&J&384||!L&&Y&16)&&U(A,w,R),H&&Q(b)}(fe&&(pe=O&&O.onVnodeUnmounted)||ie)&&queuePostRenderEffect(()=>{pe&&invokeVNodeHook(pe,w,b),ie&&invokeDirectiveHook(b,null,w,"unmounted")},R)},Q=b=>{const{type:w,el:R,anchor:H,transition:L}=b;if(w===Fragment){G(R,H);return}if(w===Static){E(b);return}const k=()=>{n(R),L&&!L.persisted&&L.afterLeave&&L.afterLeave()};if(b.shapeFlag&1&&L&&!L.persisted){const{leave:O,delayLeave:B}=L,A=()=>O(R,k);B?B(b.el,k,A):A()}else k()},G=(b,w)=>{let R;for(;b!==w;)R=m(b),n(b),b=R;n(w)},oe=(b,w,R)=>{const{bum:H,scope:L,update:k,subTree:O,um:B}=b;H&&invokeArrayFns(H),L.stop(),k&&(k.active=!1,te(O,b,w,R)),B&&queuePostRenderEffect(B,w),queuePostRenderEffect(()=>{b.isUnmounted=!0},w),w&&w.pendingBranch&&!w.isUnmounted&&b.asyncDep&&!b.asyncResolved&&b.suspenseId===w.pendingId&&(w.deps--,w.deps===0&&w.resolve())},U=(b,w,R,H=!1,L=!1,k=0)=>{for(let O=k;O<b.length;O++)te(b[O],w,R,H,L)},le=b=>b.shapeFlag&6?le(b.component.subTree):b.shapeFlag&128?b.suspense.next():m(b.anchor||b.el),$e=(b,w,R)=>{b==null?w._vnode&&te(w._vnode,null,null,!0):x(w._vnode||null,b,w,null,null,null,R),flushPreFlushCbs(),flushPostFlushCbs(),w._vnode=b},ce={p:x,um:te,m:ve,r:Q,mt:ae,mc:$,pc:ne,pbc:j,n:le,o:e};let Ie,De;return t&&([Ie,De]=t(ce)),{render:$e,hydrate:Ie,createApp:createAppAPI($e,Ie)}}function toggleRecurse({effect:e,update:t},o){e.allowRecurse=t.allowRecurse=o}function needTransition(e,t){return(!e||e&&!e.pendingBranch)&&t&&!t.persisted}function traverseStaticChildren(e,t,o=!1){const r=e.children,n=t.children;if(isArray$2(r)&&isArray$2(n))for(let i=0;i<r.length;i++){const a=r[i];let l=n[i];l.shapeFlag&1&&!l.dynamicChildren&&((l.patchFlag<=0||l.patchFlag===32)&&(l=n[i]=cloneIfMounted(n[i]),l.el=a.el),o||traverseStaticChildren(a,l)),l.type===Text&&(l.el=a.el)}}function getSequence(e){const t=e.slice(),o=[0];let r,n,i,a,l;const s=e.length;for(r=0;r<s;r++){const d=e[r];if(d!==0){if(n=o[o.length-1],e[n]<d){t[r]=n,o.push(r);continue}for(i=0,a=o.length-1;i<a;)l=i+a>>1,e[o[l]]<d?i=l+1:a=l;d<e[o[i]]&&(i>0&&(t[r]=o[i-1]),o[i]=r)}}for(i=o.length,a=o[i-1];i-- >0;)o[i]=a,a=t[a];return o}const isTeleport=e=>e.__isTeleport,isTeleportDisabled=e=>e&&(e.disabled||e.disabled===""),isTargetSVG=e=>typeof SVGElement<"u"&&e instanceof SVGElement,resolveTarget=(e,t)=>{const o=e&&e.to;return isString(o)?t?t(o):null:o},TeleportImpl={name:"Teleport",__isTeleport:!0,process(e,t,o,r,n,i,a,l,s,d){const{mc:u,pc:f,pbc:m,o:{insert:v,querySelector:g,createText:x,createComment:C}}=d,y=isTeleportDisabled(t.props);let{shapeFlag:I,children:N,dynamicChildren:E}=t;if(e==null){const P=t.el=x(""),F=t.anchor=x("");v(P,o,r),v(F,o,r);const S=t.target=resolveTarget(t.props,g),$=t.targetAnchor=x("");S&&(v($,S),a=a||isTargetSVG(S));const D=(j,K)=>{I&16&&u(N,j,K,n,i,a,l,s)};y?D(o,F):S&&D(S,$)}else{t.el=e.el;const P=t.anchor=e.anchor,F=t.target=e.target,S=t.targetAnchor=e.targetAnchor,$=isTeleportDisabled(e.props),D=$?o:F,j=$?P:S;if(a=a||isTargetSVG(F),E?(m(e.dynamicChildren,E,D,n,i,a,l),traverseStaticChildren(e,t,!0)):s||f(e,t,D,j,n,i,a,l,!1),y)$?t.props&&e.props&&t.props.to!==e.props.to&&(t.props.to=e.props.to):moveTeleport(t,o,P,d,1);else if((t.props&&t.props.to)!==(e.props&&e.props.to)){const K=t.target=resolveTarget(t.props,g);K&&moveTeleport(t,K,null,d,0)}else $&&moveTeleport(t,F,S,d,1)}updateCssVars(t)},remove(e,t,o,r,{um:n,o:{remove:i}},a){const{shapeFlag:l,children:s,anchor:d,targetAnchor:u,target:f,props:m}=e;if(f&&i(u),a&&i(d),l&16){const v=a||!isTeleportDisabled(m);for(let g=0;g<s.length;g++){const x=s[g];n(x,t,o,v,!!x.dynamicChildren)}}},move:moveTeleport,hydrate:hydrateTeleport};function moveTeleport(e,t,o,{o:{insert:r},m:n},i=2){i===0&&r(e.targetAnchor,t,o);const{el:a,anchor:l,shapeFlag:s,children:d,props:u}=e,f=i===2;if(f&&r(a,t,o),(!f||isTeleportDisabled(u))&&s&16)for(let m=0;m<d.length;m++)n(d[m],t,o,2);f&&r(l,t,o)}function hydrateTeleport(e,t,o,r,n,i,{o:{nextSibling:a,parentNode:l,querySelector:s}},d){const u=t.target=resolveTarget(t.props,s);if(u){const f=u._lpa||u.firstChild;if(t.shapeFlag&16)if(isTeleportDisabled(t.props))t.anchor=d(a(e),t,l(e),o,r,n,i),t.targetAnchor=f;else{t.anchor=a(e);let m=f;for(;m;)if(m=a(m),m&&m.nodeType===8&&m.data==="teleport anchor"){t.targetAnchor=m,u._lpa=t.targetAnchor&&a(t.targetAnchor);break}d(f,t,u,o,r,n,i)}updateCssVars(t)}return t.anchor&&a(t.anchor)}const Teleport=TeleportImpl;function updateCssVars(e){const t=e.ctx;if(t&&t.ut){let o=e.children[0].el;for(;o&&o!==e.targetAnchor;)o.nodeType===1&&o.setAttribute("data-v-owner",t.uid),o=o.nextSibling;t.ut()}}const Fragment=Symbol.for("v-fgt"),Text=Symbol.for("v-txt"),Comment=Symbol.for("v-cmt"),Static=Symbol.for("v-stc"),blockStack=[];let currentBlock=null;function openBlock(e=!1){blockStack.push(currentBlock=e?null:[])}function closeBlock(){blockStack.pop(),currentBlock=blockStack[blockStack.length-1]||null}let isBlockTreeEnabled=1;function setBlockTracking(e){isBlockTreeEnabled+=e}function setupBlock(e){return e.dynamicChildren=isBlockTreeEnabled>0?currentBlock||EMPTY_ARR:null,closeBlock(),isBlockTreeEnabled>0&&currentBlock&&currentBlock.push(e),e}function createElementBlock(e,t,o,r,n,i){return setupBlock(createBaseVNode(e,t,o,r,n,i,!0))}function createBlock(e,t,o,r,n){return setupBlock(createVNode(e,t,o,r,n,!0))}function isVNode(e){return e?e.__v_isVNode===!0:!1}function isSameVNodeType(e,t){return e.type===t.type&&e.key===t.key}const InternalObjectKey="__vInternal",normalizeKey=({key:e})=>e??null,normalizeRef=({ref:e,ref_key:t,ref_for:o})=>(typeof e=="number"&&(e=""+e),e!=null?isString(e)||isRef(e)||isFunction$1(e)?{i:currentRenderingInstance,r:e,k:t,f:!!o}:e:null);function createBaseVNode(e,t=null,o=null,r=0,n=null,i=e===Fragment?0:1,a=!1,l=!1){const s={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&normalizeKey(t),ref:t&&normalizeRef(t),scopeId:currentScopeId,slotScopeIds:null,children:o,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetAnchor:null,staticCount:0,shapeFlag:i,patchFlag:r,dynamicProps:n,dynamicChildren:null,appContext:null,ctx:currentRenderingInstance};return l?(normalizeChildren(s,o),i&128&&e.normalize(s)):o&&(s.shapeFlag|=isString(o)?8:16),isBlockTreeEnabled>0&&!a&&currentBlock&&(s.patchFlag>0||i&6)&&s.patchFlag!==32&&currentBlock.push(s),s}const createVNode=_createVNode;function _createVNode(e,t=null,o=null,r=0,n=null,i=!1){if((!e||e===NULL_DYNAMIC_COMPONENT)&&(e=Comment),isVNode(e)){const l=cloneVNode(e,t,!0);return o&&normalizeChildren(l,o),isBlockTreeEnabled>0&&!i&&currentBlock&&(l.shapeFlag&6?currentBlock[currentBlock.indexOf(e)]=l:currentBlock.push(l)),l.patchFlag|=-2,l}if(isClassComponent(e)&&(e=e.__vccOpts),t){t=guardReactiveProps(t);let{class:l,style:s}=t;l&&!isString(l)&&(t.class=normalizeClass(l)),isObject$1(s)&&(isProxy(s)&&!isArray$2(s)&&(s=extend({},s)),t.style=normalizeStyle(s))}const a=isString(e)?1:isSuspense(e)?128:isTeleport(e)?64:isObject$1(e)?4:isFunction$1(e)?2:0;return createBaseVNode(e,t,o,r,n,a,i,!0)}function guardReactiveProps(e){return e?isProxy(e)||InternalObjectKey in e?extend({},e):e:null}function cloneVNode(e,t,o=!1){const{props:r,ref:n,patchFlag:i,children:a}=e,l=t?mergeProps(r||{},t):r;return{__v_isVNode:!0,__v_skip:!0,type:e.type,props:l,key:l&&normalizeKey(l),ref:t&&t.ref?o&&n?isArray$2(n)?n.concat(normalizeRef(t)):[n,normalizeRef(t)]:normalizeRef(t):n,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:a,target:e.target,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==Fragment?i===-1?16:i|16:i,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:e.transition,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&cloneVNode(e.ssContent),ssFallback:e.ssFallback&&cloneVNode(e.ssFallback),el:e.el,anchor:e.anchor,ctx:e.ctx,ce:e.ce}}function createTextVNode(e=" ",t=0){return createVNode(Text,null,e,t)}function normalizeVNode(e){return e==null||typeof e=="boolean"?createVNode(Comment):isArray$2(e)?createVNode(Fragment,null,e.slice()):typeof e=="object"?cloneIfMounted(e):createVNode(Text,null,String(e))}function cloneIfMounted(e){return e.el===null&&e.patchFlag!==-1||e.memo?e:cloneVNode(e)}function normalizeChildren(e,t){let o=0;const{shapeFlag:r}=e;if(t==null)t=null;else if(isArray$2(t))o=16;else if(typeof t=="object")if(r&65){const n=t.default;n&&(n._c&&(n._d=!1),normalizeChildren(e,n()),n._c&&(n._d=!0));return}else{o=32;const n=t._;!n&&!(InternalObjectKey in t)?t._ctx=currentRenderingInstance:n===3&&currentRenderingInstance&&(currentRenderingInstance.slots._===1?t._=1:(t._=2,e.patchFlag|=1024))}else isFunction$1(t)?(t={default:t,_ctx:currentRenderingInstance},o=32):(t=String(t),r&64?(o=16,t=[createTextVNode(t)]):o=8);e.children=t,e.shapeFlag|=o}function mergeProps(...e){const t={};for(let o=0;o<e.length;o++){const r=e[o];for(const n in r)if(n==="class")t.class!==r.class&&(t.class=normalizeClass([t.class,r.class]));else if(n==="style")t.style=normalizeStyle([t.style,r.style]);else if(isOn(n)){const i=t[n],a=r[n];a&&i!==a&&!(isArray$2(i)&&i.includes(a))&&(t[n]=i?[].concat(i,a):a)}else n!==""&&(t[n]=r[n])}return t}function invokeVNodeHook(e,t,o,r=null){callWithAsyncErrorHandling(e,t,7,[o,r])}const emptyAppContext=createAppContext();let uid=0;function createComponentInstance(e,t,o){const r=e.type,n=(t?t.appContext:e.appContext)||emptyAppContext,i={uid:uid++,vnode:e,type:r,parent:t,appContext:n,root:null,next:null,subTree:null,effect:null,update:null,scope:new EffectScope(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:t?t.provides:Object.create(n.provides),accessCache:null,renderCache:[],components:null,directives:null,propsOptions:normalizePropsOptions(r,n),emitsOptions:normalizeEmitsOptions(r,n),emit:null,emitted:null,propsDefaults:EMPTY_OBJ,inheritAttrs:r.inheritAttrs,ctx:EMPTY_OBJ,data:EMPTY_OBJ,props:EMPTY_OBJ,attrs:EMPTY_OBJ,slots:EMPTY_OBJ,refs:EMPTY_OBJ,setupState:EMPTY_OBJ,setupContext:null,attrsProxy:null,slotsProxy:null,suspense:o,suspenseId:o?o.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,da:null,a:null,rtg:null,rtc:null,ec:null,sp:null};return i.ctx={_:i},i.root=t?t.root:i,i.emit=emit.bind(null,i),e.ce&&e.ce(i),i}let currentInstance=null;const getCurrentInstance=()=>currentInstance||currentRenderingInstance;let internalSetCurrentInstance,globalCurrentInstanceSetters,settersKey="__VUE_INSTANCE_SETTERS__";(globalCurrentInstanceSetters=getGlobalThis()[settersKey])||(globalCurrentInstanceSetters=getGlobalThis()[settersKey]=[]),globalCurrentInstanceSetters.push(e=>currentInstance=e),internalSetCurrentInstance=e=>{globalCurrentInstanceSetters.length>1?globalCurrentInstanceSetters.forEach(t=>t(e)):globalCurrentInstanceSetters[0](e)};const setCurrentInstance=e=>{internalSetCurrentInstance(e),e.scope.on()},unsetCurrentInstance=()=>{currentInstance&&currentInstance.scope.off(),internalSetCurrentInstance(null)};function isStatefulComponent(e){return e.vnode.shapeFlag&4}let isInSSRComponentSetup=!1;function setupComponent(e,t=!1){isInSSRComponentSetup=t;const{props:o,children:r}=e.vnode,n=isStatefulComponent(e);initProps(e,o,n,t),initSlots(e,r);const i=n?setupStatefulComponent(e,t):void 0;return isInSSRComponentSetup=!1,i}function setupStatefulComponent(e,t){const o=e.type;e.accessCache=Object.create(null),e.proxy=markRaw(new Proxy(e.ctx,PublicInstanceProxyHandlers));const{setup:r}=o;if(r){const n=e.setupContext=r.length>1?createSetupContext(e):null;setCurrentInstance(e),pauseTracking();const i=callWithErrorHandling(r,e,0,[e.props,n]);if(resetTracking(),unsetCurrentInstance(),isPromise(i)){if(i.then(unsetCurrentInstance,unsetCurrentInstance),t)return i.then(a=>{handleSetupResult(e,a,t)}).catch(a=>{handleError(a,e,0)});e.asyncDep=i}else handleSetupResult(e,i,t)}else finishComponentSetup(e,t)}function handleSetupResult(e,t,o){isFunction$1(t)?e.type.__ssrInlineRender?e.ssrRender=t:e.render=t:isObject$1(t)&&(e.setupState=proxyRefs(t)),finishComponentSetup(e,o)}let compile;function finishComponentSetup(e,t,o){const r=e.type;if(!e.render){if(!t&&compile&&!r.render){const n=r.template||resolveMergedOptions(e).template;if(n){const{isCustomElement:i,compilerOptions:a}=e.appContext.config,{delimiters:l,compilerOptions:s}=r,d=extend(extend({isCustomElement:i,delimiters:l},a),s);r.render=compile(n,d)}}e.render=r.render||NOOP}{setCurrentInstance(e),pauseTracking();try{applyOptions(e)}finally{resetTracking(),unsetCurrentInstance()}}}function getAttrsProxy(e){return e.attrsProxy||(e.attrsProxy=new Proxy(e.attrs,{get(t,o){return track(e,"get","$attrs"),t[o]}}))}function createSetupContext(e){const t=o=>{e.exposed=o||{}};return{get attrs(){return getAttrsProxy(e)},slots:e.slots,emit:e.emit,expose:t}}function getExposeProxy(e){if(e.exposed)return e.exposeProxy||(e.exposeProxy=new Proxy(proxyRefs(markRaw(e.exposed)),{get(t,o){if(o in t)return t[o];if(o in publicPropertiesMap)return publicPropertiesMap[o](e)},has(t,o){return o in t||o in publicPropertiesMap}}))}function isClassComponent(e){return isFunction$1(e)&&"__vccOpts"in e}const computed=(e,t)=>computed$1(e,t,isInSSRComponentSetup);function h(e,t,o){const r=arguments.length;return r===2?isObject$1(t)&&!isArray$2(t)?isVNode(t)?createVNode(e,null,[t]):createVNode(e,t):createVNode(e,null,t):(r>3?o=Array.prototype.slice.call(arguments,2):r===3&&isVNode(o)&&(o=[o]),createVNode(e,t,o))}const ssrContextKey$1=Symbol.for("v-scx"),useSSRContext=()=>inject(ssrContextKey$1),version="3.3.13",svgNS="http://www.w3.org/2000/svg",doc=typeof document<"u"?document:null,templateContainer=doc&&doc.createElement("template"),nodeOps={insert:(e,t,o)=>{t.insertBefore(e,o||null)},remove:e=>{const t=e.parentNode;t&&t.removeChild(e)},createElement:(e,t,o,r)=>{const n=t?doc.createElementNS(svgNS,e):doc.createElement(e,o?{is:o}:void 0);return e==="select"&&r&&r.multiple!=null&&n.setAttribute("multiple",r.multiple),n},createText:e=>doc.createTextNode(e),createComment:e=>doc.createComment(e),setText:(e,t)=>{e.nodeValue=t},setElementText:(e,t)=>{e.textContent=t},parentNode:e=>e.parentNode,nextSibling:e=>e.nextSibling,querySelector:e=>doc.querySelector(e),setScopeId(e,t){e.setAttribute(t,"")},insertStaticContent(e,t,o,r,n,i){const a=o?o.previousSibling:t.lastChild;if(n&&(n===i||n.nextSibling))for(;t.insertBefore(n.cloneNode(!0),o),!(n===i||!(n=n.nextSibling)););else{templateContainer.innerHTML=r?`<svg>${e}</svg>`:e;const l=templateContainer.content;if(r){const s=l.firstChild;for(;s.firstChild;)l.appendChild(s.firstChild);l.removeChild(s)}t.insertBefore(l,o)}return[a?a.nextSibling:t.firstChild,o?o.previousSibling:t.lastChild]}},TRANSITION="transition",ANIMATION="animation",vtcKey=Symbol("_vtc"),Transition=(e,{slots:t})=>h(BaseTransition,resolveTransitionProps(e),t);Transition.displayName="Transition";const DOMTransitionPropsValidators={name:String,type:String,css:{type:Boolean,default:!0},duration:[String,Number,Object],enterFromClass:String,enterActiveClass:String,enterToClass:String,appearFromClass:String,appearActiveClass:String,appearToClass:String,leaveFromClass:String,leaveActiveClass:String,leaveToClass:String},TransitionPropsValidators=Transition.props=extend({},BaseTransitionPropsValidators,DOMTransitionPropsValidators),callHook=(e,t=[])=>{isArray$2(e)?e.forEach(o=>o(...t)):e&&e(...t)},hasExplicitCallback=e=>e?isArray$2(e)?e.some(t=>t.length>1):e.length>1:!1;function resolveTransitionProps(e){const t={};for(const z in e)z in DOMTransitionPropsValidators||(t[z]=e[z]);if(e.css===!1)return t;const{name:o="v",type:r,duration:n,enterFromClass:i=`${o}-enter-from`,enterActiveClass:a=`${o}-enter-active`,enterToClass:l=`${o}-enter-to`,appearFromClass:s=i,appearActiveClass:d=a,appearToClass:u=l,leaveFromClass:f=`${o}-leave-from`,leaveActiveClass:m=`${o}-leave-active`,leaveToClass:v=`${o}-leave-to`}=e,g=normalizeDuration(n),x=g&&g[0],C=g&&g[1],{onBeforeEnter:y,onEnter:I,onEnterCancelled:N,onLeave:E,onLeaveCancelled:P,onBeforeAppear:F=y,onAppear:S=I,onAppearCancelled:$=N}=t,D=(z,ee,ae)=>{removeTransitionClass(z,ee?u:l),removeTransitionClass(z,ee?d:a),ae&&ae()},j=(z,ee)=>{z._isLeaving=!1,removeTransitionClass(z,f),removeTransitionClass(z,v),removeTransitionClass(z,m),ee&&ee()},K=z=>(ee,ae)=>{const se=z?S:I,Z=()=>D(ee,z,ae);callHook(se,[ee,Z]),nextFrame(()=>{removeTransitionClass(ee,z?s:i),addTransitionClass(ee,z?u:l),hasExplicitCallback(se)||whenTransitionEnds(ee,r,x,Z)})};return extend(t,{onBeforeEnter(z){callHook(y,[z]),addTransitionClass(z,i),addTransitionClass(z,a)},onBeforeAppear(z){callHook(F,[z]),addTransitionClass(z,s),addTransitionClass(z,d)},onEnter:K(!1),onAppear:K(!0),onLeave(z,ee){z._isLeaving=!0;const ae=()=>j(z,ee);addTransitionClass(z,f),forceReflow(),addTransitionClass(z,m),nextFrame(()=>{z._isLeaving&&(removeTransitionClass(z,f),addTransitionClass(z,v),hasExplicitCallback(E)||whenTransitionEnds(z,r,C,ae))}),callHook(E,[z,ae])},onEnterCancelled(z){D(z,!1),callHook(N,[z])},onAppearCancelled(z){D(z,!0),callHook($,[z])},onLeaveCancelled(z){j(z),callHook(P,[z])}})}function normalizeDuration(e){if(e==null)return null;if(isObject$1(e))return[NumberOf(e.enter),NumberOf(e.leave)];{const t=NumberOf(e);return[t,t]}}function NumberOf(e){return toNumber(e)}function addTransitionClass(e,t){t.split(/\s+/).forEach(o=>o&&e.classList.add(o)),(e[vtcKey]||(e[vtcKey]=new Set)).add(t)}function removeTransitionClass(e,t){t.split(/\s+/).forEach(r=>r&&e.classList.remove(r));const o=e[vtcKey];o&&(o.delete(t),o.size||(e[vtcKey]=void 0))}function nextFrame(e){requestAnimationFrame(()=>{requestAnimationFrame(e)})}let endId=0;function whenTransitionEnds(e,t,o,r){const n=e._endId=++endId,i=()=>{n===e._endId&&r()};if(o)return setTimeout(i,o);const{type:a,timeout:l,propCount:s}=getTransitionInfo(e,t);if(!a)return r();const d=a+"end";let u=0;const f=()=>{e.removeEventListener(d,m),i()},m=v=>{v.target===e&&++u>=s&&f()};setTimeout(()=>{u<s&&f()},l+1),e.addEventListener(d,m)}function getTransitionInfo(e,t){const o=window.getComputedStyle(e),r=g=>(o[g]||"").split(", "),n=r(`${TRANSITION}Delay`),i=r(`${TRANSITION}Duration`),a=getTimeout(n,i),l=r(`${ANIMATION}Delay`),s=r(`${ANIMATION}Duration`),d=getTimeout(l,s);let u=null,f=0,m=0;t===TRANSITION?a>0&&(u=TRANSITION,f=a,m=i.length):t===ANIMATION?d>0&&(u=ANIMATION,f=d,m=s.length):(f=Math.max(a,d),u=f>0?a>d?TRANSITION:ANIMATION:null,m=u?u===TRANSITION?i.length:s.length:0);const v=u===TRANSITION&&/\b(transform|all)(,|$)/.test(r(`${TRANSITION}Property`).toString());return{type:u,timeout:f,propCount:m,hasTransform:v}}function getTimeout(e,t){for(;e.length<t.length;)e=e.concat(e);return Math.max(...t.map((o,r)=>toMs(o)+toMs(e[r])))}function toMs(e){return e==="auto"?0:Number(e.slice(0,-1).replace(",","."))*1e3}function forceReflow(){return document.body.offsetHeight}function patchClass(e,t,o){const r=e[vtcKey];r&&(t=(t?[t,...r]:[...r]).join(" ")),t==null?e.removeAttribute("class"):o?e.setAttribute("class",t):e.className=t}const vShowOldKey=Symbol("_vod"),vShow={beforeMount(e,{value:t},{transition:o}){e[vShowOldKey]=e.style.display==="none"?"":e.style.display,o&&t?o.beforeEnter(e):setDisplay(e,t)},mounted(e,{value:t},{transition:o}){o&&t&&o.enter(e)},updated(e,{value:t,oldValue:o},{transition:r}){!t!=!o&&(r?t?(r.beforeEnter(e),setDisplay(e,!0),r.enter(e)):r.leave(e,()=>{setDisplay(e,!1)}):setDisplay(e,t))},beforeUnmount(e,{value:t}){setDisplay(e,t)}};function setDisplay(e,t){e.style.display=t?e[vShowOldKey]:"none"}const CSS_VAR_TEXT=Symbol("");function patchStyle(e,t,o){const r=e.style,n=isString(o);if(o&&!n){if(t&&!isString(t))for(const i in t)o[i]==null&&setStyle(r,i,"");for(const i in o)setStyle(r,i,o[i])}else{const i=r.display;if(n){if(t!==o){const a=r[CSS_VAR_TEXT];a&&(o+=";"+a),r.cssText=o}}else t&&e.removeAttribute("style");vShowOldKey in e&&(r.display=i)}}const importantRE=/\s*!important$/;function setStyle(e,t,o){if(isArray$2(o))o.forEach(r=>setStyle(e,t,r));else if(o==null&&(o=""),t.startsWith("--"))e.setProperty(t,o);else{const r=autoPrefix(e,t);importantRE.test(o)?e.setProperty(hyphenate(r),o.replace(importantRE,""),"important"):e[r]=o}}const prefixes=["Webkit","Moz","ms"],prefixCache={};function autoPrefix(e,t){const o=prefixCache[t];if(o)return o;let r=camelize(t);if(r!=="filter"&&r in e)return prefixCache[t]=r;r=capitalize(r);for(let n=0;n<prefixes.length;n++){const i=prefixes[n]+r;if(i in e)return prefixCache[t]=i}return t}const xlinkNS="http://www.w3.org/1999/xlink";function patchAttr(e,t,o,r,n){if(r&&t.startsWith("xlink:"))o==null?e.removeAttributeNS(xlinkNS,t.slice(6,t.length)):e.setAttributeNS(xlinkNS,t,o);else{const i=isSpecialBooleanAttr(t);o==null||i&&!includeBooleanAttr(o)?e.removeAttribute(t):e.setAttribute(t,i?"":o)}}function patchDOMProp(e,t,o,r,n,i,a){if(t==="innerHTML"||t==="textContent"){r&&a(r,n,i),e[t]=o??"";return}const l=e.tagName;if(t==="value"&&l!=="PROGRESS"&&!l.includes("-")){e._value=o;const d=l==="OPTION"?e.getAttribute("value"):e.value,u=o??"";d!==u&&(e.value=u),o==null&&e.removeAttribute(t);return}let s=!1;if(o===""||o==null){const d=typeof e[t];d==="boolean"?o=includeBooleanAttr(o):o==null&&d==="string"?(o="",s=!0):d==="number"&&(o=0,s=!0)}try{e[t]=o}catch{}s&&e.removeAttribute(t)}function addEventListener(e,t,o,r){e.addEventListener(t,o,r)}function removeEventListener(e,t,o,r){e.removeEventListener(t,o,r)}const veiKey=Symbol("_vei");function patchEvent(e,t,o,r,n=null){const i=e[veiKey]||(e[veiKey]={}),a=i[t];if(r&&a)a.value=r;else{const[l,s]=parseName(t);if(r){const d=i[t]=createInvoker(r,n);addEventListener(e,l,d,s)}else a&&(removeEventListener(e,l,a,s),i[t]=void 0)}}const optionsModifierRE=/(?:Once|Passive|Capture)$/;function parseName(e){let t;if(optionsModifierRE.test(e)){t={};let r;for(;r=e.match(optionsModifierRE);)e=e.slice(0,e.length-r[0].length),t[r[0].toLowerCase()]=!0}return[e[2]===":"?e.slice(3):hyphenate(e.slice(2)),t]}let cachedNow=0;const p=Promise.resolve(),getNow=()=>cachedNow||(p.then(()=>cachedNow=0),cachedNow=Date.now());function createInvoker(e,t){const o=r=>{if(!r._vts)r._vts=Date.now();else if(r._vts<=o.attached)return;callWithAsyncErrorHandling(patchStopImmediatePropagation(r,o.value),t,5,[r])};return o.value=e,o.attached=getNow(),o}function patchStopImmediatePropagation(e,t){if(isArray$2(t)){const o=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{o.call(e),e._stopped=!0},t.map(r=>n=>!n._stopped&&r&&r(n))}else return t}const isNativeOn=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&e.charCodeAt(2)>96&&e.charCodeAt(2)<123,patchProp=(e,t,o,r,n=!1,i,a,l,s)=>{t==="class"?patchClass(e,r,n):t==="style"?patchStyle(e,o,r):isOn(t)?isModelListener(t)||patchEvent(e,t,o,r,a):(t[0]==="."?(t=t.slice(1),!0):t[0]==="^"?(t=t.slice(1),!1):shouldSetAsProp(e,t,r,n))?patchDOMProp(e,t,r,i,a,l,s):(t==="true-value"?e._trueValue=r:t==="false-value"&&(e._falseValue=r),patchAttr(e,t,r,n))};function shouldSetAsProp(e,t,o,r){if(r)return!!(t==="innerHTML"||t==="textContent"||t in e&&isNativeOn(t)&&isFunction$1(o));if(t==="spellcheck"||t==="draggable"||t==="translate"||t==="form"||t==="list"&&e.tagName==="INPUT"||t==="type"&&e.tagName==="TEXTAREA")return!1;if(t==="width"||t==="height"){const n=e.tagName;if(n==="IMG"||n==="VIDEO"||n==="CANVAS"||n==="SOURCE")return!1}return isNativeOn(t)&&isString(o)?!1:t in e}const positionMap=new WeakMap,newPositionMap=new WeakMap,moveCbKey=Symbol("_moveCb"),enterCbKey=Symbol("_enterCb"),TransitionGroupImpl={name:"TransitionGroup",props:extend({},TransitionPropsValidators,{tag:String,moveClass:String}),setup(e,{slots:t}){const o=getCurrentInstance(),r=useTransitionState();let n,i;return onUpdated(()=>{if(!n.length)return;const a=e.moveClass||`${e.name||"v"}-move`;if(!hasCSSTransform(n[0].el,o.vnode.el,a))return;n.forEach(callPendingCbs),n.forEach(recordPosition);const l=n.filter(applyTranslation);forceReflow(),l.forEach(s=>{const d=s.el,u=d.style;addTransitionClass(d,a),u.transform=u.webkitTransform=u.transitionDuration="";const f=d[moveCbKey]=m=>{m&&m.target!==d||(!m||/transform$/.test(m.propertyName))&&(d.removeEventListener("transitionend",f),d[moveCbKey]=null,removeTransitionClass(d,a))};d.addEventListener("transitionend",f)})}),()=>{const a=toRaw(e),l=resolveTransitionProps(a);let s=a.tag||Fragment;n=i,i=t.default?getTransitionRawChildren(t.default()):[];for(let d=0;d<i.length;d++){const u=i[d];u.key!=null&&setTransitionHooks(u,resolveTransitionHooks(u,l,r,o))}if(n)for(let d=0;d<n.length;d++){const u=n[d];setTransitionHooks(u,resolveTransitionHooks(u,l,r,o)),positionMap.set(u,u.el.getBoundingClientRect())}return createVNode(s,null,i)}}},removeMode=e=>delete e.mode;TransitionGroupImpl.props;const TransitionGroup=TransitionGroupImpl;function callPendingCbs(e){const t=e.el;t[moveCbKey]&&t[moveCbKey](),t[enterCbKey]&&t[enterCbKey]()}function recordPosition(e){newPositionMap.set(e,e.el.getBoundingClientRect())}function applyTranslation(e){const t=positionMap.get(e),o=newPositionMap.get(e),r=t.left-o.left,n=t.top-o.top;if(r||n){const i=e.el.style;return i.transform=i.webkitTransform=`translate(${r}px,${n}px)`,i.transitionDuration="0s",e}}function hasCSSTransform(e,t,o){const r=e.cloneNode(),n=e[vtcKey];n&&n.forEach(l=>{l.split(/\s+/).forEach(s=>s&&r.classList.remove(s))}),o.split(/\s+/).forEach(l=>l&&r.classList.add(l)),r.style.display="none";const i=t.nodeType===1?t:t.parentNode;i.appendChild(r);const{hasTransform:a}=getTransitionInfo(r);return i.removeChild(r),a}const rendererOptions=extend({patchProp},nodeOps);let renderer;function ensureRenderer(){return renderer||(renderer=createRenderer(rendererOptions))}const render$2=(...e)=>{ensureRenderer().render(...e)},createApp=(...e)=>{const t=ensureRenderer().createApp(...e),{mount:o}=t;return t.mount=r=>{const n=normalizeContainer(r);if(!n)return;const i=t._component;!isFunction$1(i)&&!i.render&&!i.template&&(i.template=n.innerHTML),n.innerHTML="";const a=o(n,!1,n instanceof SVGElement);return n instanceof Element&&(n.removeAttribute("v-cloak"),n.setAttribute("data-v-app","")),a},t};function normalizeContainer(e){return isString(e)?document.querySelector(e):e}var isVue2=!1;/*!
 * pinia v2.1.7
 * (c) 2023 Eduardo San Martin Morote
 * @license MIT
 */let activePinia;const setActivePinia=e=>activePinia=e,piniaSymbol=Symbol();function isPlainObject$1(e){return e&&typeof e=="object"&&Object.prototype.toString.call(e)==="[object Object]"&&typeof e.toJSON!="function"}var MutationType;(function(e){e.direct="direct",e.patchObject="patch object",e.patchFunction="patch function"})(MutationType||(MutationType={}));function createPinia(){const e=effectScope(!0),t=e.run(()=>ref({}));let o=[],r=[];const n=markRaw({install(i){setActivePinia(n),n._a=i,i.provide(piniaSymbol,n),i.config.globalProperties.$pinia=n,r.forEach(a=>o.push(a)),r=[]},use(i){return!this._a&&!isVue2?r.push(i):o.push(i),this},_p:o,_a:null,_e:e,_s:new Map,state:t});return n}const noop=()=>{};function addSubscription(e,t,o,r=noop){e.push(t);const n=()=>{const i=e.indexOf(t);i>-1&&(e.splice(i,1),r())};return!o&&getCurrentScope()&&onScopeDispose(n),n}function triggerSubscriptions(e,...t){e.slice().forEach(o=>{o(...t)})}const fallbackRunWithContext=e=>e();function mergeReactiveObjects(e,t){e instanceof Map&&t instanceof Map&&t.forEach((o,r)=>e.set(r,o)),e instanceof Set&&t instanceof Set&&t.forEach(e.add,e);for(const o in t){if(!t.hasOwnProperty(o))continue;const r=t[o],n=e[o];isPlainObject$1(n)&&isPlainObject$1(r)&&e.hasOwnProperty(o)&&!isRef(r)&&!isReactive(r)?e[o]=mergeReactiveObjects(n,r):e[o]=r}return e}const skipHydrateSymbol=Symbol();function shouldHydrate(e){return!isPlainObject$1(e)||!e.hasOwnProperty(skipHydrateSymbol)}const{assign}=Object;function isComputed(e){return!!(isRef(e)&&e.effect)}function createOptionsStore(e,t,o,r){const{state:n,actions:i,getters:a}=t,l=o.state.value[e];let s;function d(){l||(o.state.value[e]=n?n():{});const u=toRefs(o.state.value[e]);return assign(u,i,Object.keys(a||{}).reduce((f,m)=>(f[m]=markRaw(computed(()=>{setActivePinia(o);const v=o._s.get(e);return a[m].call(v,v)})),f),{}))}return s=createSetupStore(e,d,t,o,r,!0),s}function createSetupStore(e,t,o={},r,n,i){let a;const l=assign({actions:{}},o),s={deep:!0};let d,u,f=[],m=[],v;const g=r.state.value[e];!i&&!g&&(r.state.value[e]={}),ref({});let x;function C($){let D;d=u=!1,typeof $=="function"?($(r.state.value[e]),D={type:MutationType.patchFunction,storeId:e,events:v}):(mergeReactiveObjects(r.state.value[e],$),D={type:MutationType.patchObject,payload:$,storeId:e,events:v});const j=x=Symbol();nextTick().then(()=>{x===j&&(d=!0)}),u=!0,triggerSubscriptions(f,D,r.state.value[e])}const y=i?function(){const{state:D}=o,j=D?D():{};this.$patch(K=>{assign(K,j)})}:noop;function I(){a.stop(),f=[],m=[],r._s.delete(e)}function N($,D){return function(){setActivePinia(r);const j=Array.from(arguments),K=[],z=[];function ee(Z){K.push(Z)}function ae(Z){z.push(Z)}triggerSubscriptions(m,{args:j,name:$,store:P,after:ee,onError:ae});let se;try{se=D.apply(this&&this.$id===e?this:P,j)}catch(Z){throw triggerSubscriptions(z,Z),Z}return se instanceof Promise?se.then(Z=>(triggerSubscriptions(K,Z),Z)).catch(Z=>(triggerSubscriptions(z,Z),Promise.reject(Z))):(triggerSubscriptions(K,se),se)}}const E={_p:r,$id:e,$onAction:addSubscription.bind(null,m),$patch:C,$reset:y,$subscribe($,D={}){const j=addSubscription(f,$,D.detached,()=>K()),K=a.run(()=>watch(()=>r.state.value[e],z=>{(D.flush==="sync"?u:d)&&$({storeId:e,type:MutationType.direct,events:v},z)},assign({},s,D)));return j},$dispose:I},P=reactive(E);r._s.set(e,P);const S=(r._a&&r._a.runWithContext||fallbackRunWithContext)(()=>r._e.run(()=>(a=effectScope()).run(t)));for(const $ in S){const D=S[$];if(isRef(D)&&!isComputed(D)||isReactive(D))i||(g&&shouldHydrate(D)&&(isRef(D)?D.value=g[$]:mergeReactiveObjects(D,g[$])),r.state.value[e][$]=D);else if(typeof D=="function"){const j=N($,D);S[$]=j,l.actions[$]=D}}return assign(P,S),assign(toRaw(P),S),Object.defineProperty(P,"$state",{get:()=>r.state.value[e],set:$=>{C(D=>{assign(D,$)})}}),r._p.forEach($=>{assign(P,a.run(()=>$({store:P,app:r._a,pinia:r,options:l})))}),g&&i&&o.hydrate&&o.hydrate(P.$state,g),d=!0,u=!0,P}function defineStore(e,t,o){let r,n;const i=typeof t=="function";typeof e=="string"?(r=e,n=i?o:t):(n=e,r=e.id);function a(l,s){const d=hasInjectionContext();return l=l||(d?inject(piniaSymbol,null):null),l&&setActivePinia(l),l=activePinia,l._s.has(r)||(i?createSetupStore(r,t,n,l):createOptionsStore(r,n,l)),l._s.get(r)}return a.$id=r,a}function getPreciseEventTarget(e){return e.composedPath()[0]||null}function depx(e){return typeof e=="string"?e.endsWith("px")?Number(e.slice(0,e.length-2)):Number(e):e}function pxfy(e){if(e!=null)return typeof e=="number"?`${e}px`:e.endsWith("px")?e:`${e}px`}function getMargin(e,t){const o=e.trim().split(/\s+/g),r={top:o[0]};switch(o.length){case 1:r.right=o[0],r.bottom=o[0],r.left=o[0];break;case 2:r.right=o[1],r.left=o[1],r.bottom=o[0];break;case 3:r.right=o[1],r.bottom=o[2],r.left=o[1];break;case 4:r.right=o[1],r.bottom=o[2],r.left=o[3];break;default:throw new Error("[seemly/getMargin]:"+e+" is not a valid value.")}return t===void 0?r:r[t]}function getGap(e,t){const[o,r]=e.split(" ");return t?t==="row"?o:r:{row:o,col:r||o}}const colors={black:"#000",silver:"#C0C0C0",gray:"#808080",white:"#FFF",maroon:"#800000",red:"#F00",purple:"#800080",fuchsia:"#F0F",green:"#008000",lime:"#0F0",olive:"#808000",yellow:"#FF0",navy:"#000080",blue:"#00F",teal:"#008080",aqua:"#0FF",transparent:"#0000"},prefix$1="^\\s*",suffix="\\s*$",float="\\s*((\\.\\d+)|(\\d+(\\.\\d*)?))\\s*",hex="([0-9A-Fa-f])",dhex="([0-9A-Fa-f]{2})",rgbRegex=new RegExp(`${prefix$1}rgb\\s*\\(${float},${float},${float}\\)${suffix}`),rgbaRegex=new RegExp(`${prefix$1}rgba\\s*\\(${float},${float},${float},${float}\\)${suffix}`),sHexRegex=new RegExp(`${prefix$1}#${hex}${hex}${hex}${suffix}`),hexRegex=new RegExp(`${prefix$1}#${dhex}${dhex}${dhex}${suffix}`),sHexaRegex=new RegExp(`${prefix$1}#${hex}${hex}${hex}${hex}${suffix}`),hexaRegex=new RegExp(`${prefix$1}#${dhex}${dhex}${dhex}${dhex}${suffix}`);function parseHex(e){return parseInt(e,16)}function rgba(e){try{let t;if(t=hexRegex.exec(e))return[parseHex(t[1]),parseHex(t[2]),parseHex(t[3]),1];if(t=rgbRegex.exec(e))return[roundChannel(t[1]),roundChannel(t[5]),roundChannel(t[9]),1];if(t=rgbaRegex.exec(e))return[roundChannel(t[1]),roundChannel(t[5]),roundChannel(t[9]),roundAlpha(t[13])];if(t=sHexRegex.exec(e))return[parseHex(t[1]+t[1]),parseHex(t[2]+t[2]),parseHex(t[3]+t[3]),1];if(t=hexaRegex.exec(e))return[parseHex(t[1]),parseHex(t[2]),parseHex(t[3]),roundAlpha(parseHex(t[4])/255)];if(t=sHexaRegex.exec(e))return[parseHex(t[1]+t[1]),parseHex(t[2]+t[2]),parseHex(t[3]+t[3]),roundAlpha(parseHex(t[4]+t[4])/255)];if(e in colors)return rgba(colors[e]);throw new Error(`[seemly/rgba]: Invalid color value ${e}.`)}catch(t){throw t}}function normalizeAlpha(e){return e>1?1:e<0?0:e}function stringifyRgba(e,t,o,r){return`rgba(${roundChannel(e)}, ${roundChannel(t)}, ${roundChannel(o)}, ${normalizeAlpha(r)})`}function compositeChannel(e,t,o,r,n){return roundChannel((e*t*(1-r)+o*r)/n)}function composite(e,t){Array.isArray(e)||(e=rgba(e)),Array.isArray(t)||(t=rgba(t));const o=e[3],r=t[3],n=roundAlpha(o+r-o*r);return stringifyRgba(compositeChannel(e[0],o,t[0],r,n),compositeChannel(e[1],o,t[1],r,n),compositeChannel(e[2],o,t[2],r,n),n)}function changeColor(e,t){const[o,r,n,i=1]=Array.isArray(e)?e:rgba(e);return t.alpha?stringifyRgba(o,r,n,t.alpha):stringifyRgba(o,r,n,i)}function scaleColor(e,t){const[o,r,n,i=1]=Array.isArray(e)?e:rgba(e),{lightness:a=1,alpha:l=1}=t;return toRgbaString([o*a,r*a,n*a,i*l])}function roundAlpha(e){const t=Math.round(Number(e)*100)/100;return t>1?1:t<0?0:t}function roundChannel(e){const t=Math.round(Number(e));return t>255?255:t<0?0:t}function toRgbaString(e){const[t,o,r]=e;return 3 in e?`rgba(${roundChannel(t)}, ${roundChannel(o)}, ${roundChannel(r)}, ${roundAlpha(e[3])})`:`rgba(${roundChannel(t)}, ${roundChannel(o)}, ${roundChannel(r)}, 1)`}function createId(e=8){return Math.random().toString(16).slice(2,2+e)}function getSlot$1(e,t="default",o=[]){const n=e.$slots[t];return n===void 0?o:n()}function keep(e,t=[],o){const r={};return t.forEach(n=>{r[n]=e[n]}),Object.assign(r,o)}function omit(e,t=[],o){const r={};return Object.getOwnPropertyNames(e).forEach(i=>{t.includes(i)||(r[i]=e[i])}),Object.assign(r,o)}function flatten(e,t=!0,o=[]){return e.forEach(r=>{if(r!==null){if(typeof r!="object"){(typeof r=="string"||typeof r=="number")&&o.push(createTextVNode(String(r)));return}if(Array.isArray(r)){flatten(r,t,o);return}if(r.type===Fragment){if(r.children===null)return;Array.isArray(r.children)&&flatten(r.children,t,o)}else r.type!==Comment&&o.push(r)}}),o}function call(e,...t){if(Array.isArray(e))e.forEach(o=>call(o,...t));else return e(...t)}function keysOf(e){return Object.keys(e)}const render$1=(e,...t)=>typeof e=="function"?e(...t):typeof e=="string"?createTextVNode(e):typeof e=="number"?createTextVNode(String(e)):null;function warn$2(e,t){console.error(`[naive/${e}]: ${t}`)}function throwError(e,t){throw new Error(`[naive/${e}]: ${t}`)}function getFirstSlotVNode(e,t="default",o=void 0){const r=e[t];if(!r)return warn$2("getFirstSlotVNode",`slot[${t}] is empty`),null;const n=flatten(r(o));return n.length===1?n[0]:(warn$2("getFirstSlotVNode",`slot[${t}] should have exactly one child`),null)}function createInjectionKey(e){return e}function ensureValidVNode(e){return e.some(t=>isVNode(t)?!(t.type===Comment||t.type===Fragment&&!ensureValidVNode(t.children)):!0)?e:null}function resolveSlot(e,t){return e&&ensureValidVNode(e())||t()}function resolveSlotWithProps(e,t,o){return e&&ensureValidVNode(e(t))||o(t)}function resolveWrappedSlot(e,t){const o=e&&ensureValidVNode(e());return t(o||null)}function isSlotEmpty(e){return!(e&&ensureValidVNode(e()))}const Wrapper=defineComponent({render(){var e,t;return(t=(e=this.$slots).default)===null||t===void 0?void 0:t.call(e)}});function color2Class(e){return e.replace(/#|\(|\)|,|\s|\./g,"_")}function ampCount(e){let t=0;for(let o=0;o<e.length;++o)e[o]==="&"&&++t;return t}const separatorRegex=/\s*,(?![^(]*\))\s*/g,extraSpaceRegex=/\s+/g;function resolveSelectorWithAmp(e,t){const o=[];return t.split(separatorRegex).forEach(r=>{let n=ampCount(r);if(n){if(n===1){e.forEach(a=>{o.push(r.replace("&",a))});return}}else{e.forEach(a=>{o.push((a&&a+" ")+r)});return}let i=[r];for(;n--;){const a=[];i.forEach(l=>{e.forEach(s=>{a.push(l.replace("&",s))})}),i=a}i.forEach(a=>o.push(a))}),o}function resolveSelector(e,t){const o=[];return t.split(separatorRegex).forEach(r=>{e.forEach(n=>{o.push((n&&n+" ")+r)})}),o}function parseSelectorPath(e){let t=[""];return e.forEach(o=>{o=o&&o.trim(),o&&(o.includes("&")?t=resolveSelectorWithAmp(t,o):t=resolveSelector(t,o))}),t.join(", ").replace(extraSpaceRegex," ")}function removeElement(e){if(!e)return;const t=e.parentElement;t&&t.removeChild(e)}function queryElement(e){return document.querySelector(`style[cssr-id="${e}"]`)}function createElement(e){const t=document.createElement("style");return t.setAttribute("cssr-id",e),t}function isMediaOrSupports(e){return e?/^\s*@(s|m)/.test(e):!1}const kebabRegex=/[A-Z]/g;function kebabCase(e){return e.replace(kebabRegex,t=>"-"+t.toLowerCase())}function unwrapProperty(e,t="  "){return typeof e=="object"&&e!==null?` {
`+Object.entries(e).map(o=>t+`  ${kebabCase(o[0])}: ${o[1]};`).join(`
`)+`
`+t+"}":`: ${e};`}function unwrapProperties(e,t,o){return typeof e=="function"?e({context:t.context,props:o}):e}function createStyle(e,t,o,r){if(!t)return"";const n=unwrapProperties(t,o,r);if(!n)return"";if(typeof n=="string")return`${e} {
${n}
}`;const i=Object.keys(n);if(i.length===0)return o.config.keepEmptyBlock?e+` {
}`:"";const a=e?[e+" {"]:[];return i.forEach(l=>{const s=n[l];if(l==="raw"){a.push(`
`+s+`
`);return}l=kebabCase(l),s!=null&&a.push(`  ${l}${unwrapProperty(s)}`)}),e&&a.push("}"),a.join(`
`)}function loopCNodeListWithCallback(e,t,o){e&&e.forEach(r=>{if(Array.isArray(r))loopCNodeListWithCallback(r,t,o);else if(typeof r=="function"){const n=r(t);Array.isArray(n)?loopCNodeListWithCallback(n,t,o):n&&o(n)}else r&&o(r)})}function traverseCNode(e,t,o,r,n,i){const a=e.$;let l="";if(!a||typeof a=="string")isMediaOrSupports(a)?l=a:t.push(a);else if(typeof a=="function"){const u=a({context:r.context,props:n});isMediaOrSupports(u)?l=u:t.push(u)}else if(a.before&&a.before(r.context),!a.$||typeof a.$=="string")isMediaOrSupports(a.$)?l=a.$:t.push(a.$);else if(a.$){const u=a.$({context:r.context,props:n});isMediaOrSupports(u)?l=u:t.push(u)}const s=parseSelectorPath(t),d=createStyle(s,e.props,r,n);l?(o.push(`${l} {`),i&&d&&i.insertRule(`${l} {
${d}
}
`)):(i&&d&&i.insertRule(d),!i&&d.length&&o.push(d)),e.children&&loopCNodeListWithCallback(e.children,{context:r.context,props:n},u=>{if(typeof u=="string"){const f=createStyle(s,{raw:u},r,n);i?i.insertRule(f):o.push(f)}else traverseCNode(u,t,o,r,n,i)}),t.pop(),l&&o.push("}"),a&&a.after&&a.after(r.context)}function render(e,t,o,r=!1){const n=[];return traverseCNode(e,[],n,t,o,r?e.instance.__styleSheet:void 0),r?"":n.join(`

`)}function murmur2(e){for(var t=0,o,r=0,n=e.length;n>=4;++r,n-=4)o=e.charCodeAt(r)&255|(e.charCodeAt(++r)&255)<<8|(e.charCodeAt(++r)&255)<<16|(e.charCodeAt(++r)&255)<<24,o=(o&65535)*1540483477+((o>>>16)*59797<<16),o^=o>>>24,t=(o&65535)*1540483477+((o>>>16)*59797<<16)^(t&65535)*1540483477+((t>>>16)*59797<<16);switch(n){case 3:t^=(e.charCodeAt(r+2)&255)<<16;case 2:t^=(e.charCodeAt(r+1)&255)<<8;case 1:t^=e.charCodeAt(r)&255,t=(t&65535)*1540483477+((t>>>16)*59797<<16)}return t^=t>>>13,t=(t&65535)*1540483477+((t>>>16)*59797<<16),((t^t>>>15)>>>0).toString(36)}typeof window<"u"&&(window.__cssrContext={});function unmount(e,t,o){const{els:r}=t;if(o===void 0)r.forEach(removeElement),t.els=[];else{const n=queryElement(o);n&&r.includes(n)&&(removeElement(n),t.els=r.filter(i=>i!==n))}}function addElementToList(e,t){e.push(t)}function mount(e,t,o,r,n,i,a,l,s){if(i&&!s){if(o===void 0){console.error("[css-render/mount]: `id` is required in `silent` mode.");return}const m=window.__cssrContext;m[o]||(m[o]=!0,render(t,e,r,i));return}let d;if(o===void 0&&(d=t.render(r),o=murmur2(d)),s){s.adapter(o,d??t.render(r));return}const u=queryElement(o);if(u!==null&&!a)return u;const f=u??createElement(o);if(d===void 0&&(d=t.render(r)),f.textContent=d,u!==null)return u;if(l){const m=document.head.querySelector(`meta[name="${l}"]`);if(m)return document.head.insertBefore(f,m),addElementToList(t.els,f),f}return n?document.head.insertBefore(f,document.head.querySelector("style, link")):document.head.appendChild(f),addElementToList(t.els,f),f}function wrappedRender(e){return render(this,this.instance,e)}function wrappedMount(e={}){const{id:t,ssr:o,props:r,head:n=!1,silent:i=!1,force:a=!1,anchorMetaName:l}=e;return mount(this.instance,this,t,r,n,i,a,l,o)}function wrappedUnmount(e={}){const{id:t}=e;unmount(this.instance,this,t)}const createCNode=function(e,t,o,r){return{instance:e,$:t,props:o,children:r,els:[],render:wrappedRender,mount:wrappedMount,unmount:wrappedUnmount}},c$1=function(e,t,o,r){return Array.isArray(t)?createCNode(e,{$:null},null,t):Array.isArray(o)?createCNode(e,t,null,o):Array.isArray(r)?createCNode(e,t,o,r):createCNode(e,t,o,null)};function CssRender(e={}){let t=null;const o={c:(...r)=>c$1(o,...r),use:(r,...n)=>r.install(o,...n),find:queryElement,context:{},config:e,get __styleSheet(){if(!t){const r=document.createElement("style");return document.head.appendChild(r),t=document.styleSheets[document.styleSheets.length-1],t}return t}};return o}function exists(e,t){if(e===void 0)return!1;if(t){const{context:{ids:o}}=t;return o.has(e)}return queryElement(e)!==null}function plugin$1(e){let t=".",o="__",r="--",n;if(e){let g=e.blockPrefix;g&&(t=g),g=e.elementPrefix,g&&(o=g),g=e.modifierPrefix,g&&(r=g)}const i={install(g){n=g.c;const x=g.context;x.bem={},x.bem.b=null,x.bem.els=null}};function a(g){let x,C;return{before(y){x=y.bem.b,C=y.bem.els,y.bem.els=null},after(y){y.bem.b=x,y.bem.els=C},$({context:y,props:I}){return g=typeof g=="string"?g:g({context:y,props:I}),y.bem.b=g,`${(I==null?void 0:I.bPrefix)||t}${y.bem.b}`}}}function l(g){let x;return{before(C){x=C.bem.els},after(C){C.bem.els=x},$({context:C,props:y}){return g=typeof g=="string"?g:g({context:C,props:y}),C.bem.els=g.split(",").map(I=>I.trim()),C.bem.els.map(I=>`${(y==null?void 0:y.bPrefix)||t}${C.bem.b}${o}${I}`).join(", ")}}}function s(g){return{$({context:x,props:C}){g=typeof g=="string"?g:g({context:x,props:C});const y=g.split(",").map(E=>E.trim());function I(E){return y.map(P=>`&${(C==null?void 0:C.bPrefix)||t}${x.bem.b}${E!==void 0?`${o}${E}`:""}${r}${P}`).join(", ")}const N=x.bem.els;return N!==null?I(N[0]):I()}}}function d(g){return{$({context:x,props:C}){g=typeof g=="string"?g:g({context:x,props:C});const y=x.bem.els;return`&:not(${(C==null?void 0:C.bPrefix)||t}${x.bem.b}${y!==null&&y.length>0?`${o}${y[0]}`:""}${r}${g})`}}}return Object.assign(i,{cB:(...g)=>n(a(g[0]),g[1],g[2]),cE:(...g)=>n(l(g[0]),g[1],g[2]),cM:(...g)=>n(s(g[0]),g[1],g[2]),cNotM:(...g)=>n(d(g[0]),g[1],g[2])}),i}function createKey(e,t){return e+(t==="default"?"":t.replace(/^[a-z]/,o=>o.toUpperCase()))}createKey("abc","def");const namespace="n",prefix=`.${namespace}-`,elementPrefix="__",modifierPrefix="--",cssr=CssRender(),plugin=plugin$1({blockPrefix:prefix,elementPrefix,modifierPrefix});cssr.use(plugin);const{c,find}=cssr,{cB,cE,cM,cNotM}=plugin;function insideModal(e){return c(({props:{bPrefix:t}})=>`${t||prefix}modal, ${t||prefix}drawer`,[e])}function insidePopover(e){return c(({props:{bPrefix:t}})=>`${t||prefix}popover`,[e])}function asModal(e){return c(({props:{bPrefix:t}})=>`&${t||prefix}modal`,e)}const isBrowser$2=typeof document<"u"&&typeof window<"u",eventSet=new WeakSet;function eventEffectNotPerformed(e){return!eventSet.has(e)}function useFalseUntilTruthy(e){const t=ref(!!e.value);if(t.value)return readonly(t);const o=watch(e,r=>{r&&(t.value=!0,o())});return readonly(t)}function useMemo(e){const t=computed(e),o=ref(t.value);return watch(t,r=>{o.value=r}),typeof e=="function"?o:{__v_isRef:!0,get value(){return o.value},set value(r){e.set(r)}}}function hasInstance(){return getCurrentInstance()!==null}const isBrowser$1=typeof window<"u";function getEventTarget(e){return e.composedPath()[0]}const traps={mousemoveoutside:new WeakMap,clickoutside:new WeakMap};function createTrapHandler(e,t,o){if(e==="mousemoveoutside"){const r=n=>{t.contains(getEventTarget(n))||o(n)};return{mousemove:r,touchstart:r}}else if(e==="clickoutside"){let r=!1;const n=a=>{r=!t.contains(getEventTarget(a))},i=a=>{r&&(t.contains(getEventTarget(a))||o(a))};return{mousedown:n,mouseup:i,touchstart:n,touchend:i}}return console.error(`[evtd/create-trap-handler]: name \`${e}\` is invalid. This could be a bug of evtd.`),{}}function ensureTrapHandlers(e,t,o){const r=traps[e];let n=r.get(t);n===void 0&&r.set(t,n=new WeakMap);let i=n.get(o);return i===void 0&&n.set(o,i=createTrapHandler(e,t,o)),i}function trapOn(e,t,o,r){if(e==="mousemoveoutside"||e==="clickoutside"){const n=ensureTrapHandlers(e,t,o);return Object.keys(n).forEach(i=>{on(i,document,n[i],r)}),!0}return!1}function trapOff(e,t,o,r){if(e==="mousemoveoutside"||e==="clickoutside"){const n=ensureTrapHandlers(e,t,o);return Object.keys(n).forEach(i=>{off(i,document,n[i],r)}),!0}return!1}function createDelegate(){if(typeof window>"u")return{on:()=>{},off:()=>{}};const e=new WeakMap,t=new WeakMap;function o(){e.set(this,!0)}function r(){e.set(this,!0),t.set(this,!0)}function n(S,$,D){const j=S[$];return S[$]=function(){return D.apply(S,arguments),j.apply(S,arguments)},S}function i(S,$){S[$]=Event.prototype[$]}const a=new WeakMap,l=Object.getOwnPropertyDescriptor(Event.prototype,"currentTarget");function s(){var S;return(S=a.get(this))!==null&&S!==void 0?S:null}function d(S,$){l!==void 0&&Object.defineProperty(S,"currentTarget",{configurable:!0,enumerable:!0,get:$??l.get})}const u={bubble:{},capture:{}},f={};function m(){const S=function($){const{type:D,eventPhase:j,bubbles:K}=$,z=getEventTarget($);if(j===2)return;const ee=j===1?"capture":"bubble";let ae=z;const se=[];for(;ae===null&&(ae=window),se.push(ae),ae!==window;)ae=ae.parentNode||null;const Z=u.capture[D],q=u.bubble[D];if(n($,"stopPropagation",o),n($,"stopImmediatePropagation",r),d($,s),ee==="capture"){if(Z===void 0)return;for(let ne=se.length-1;ne>=0&&!e.has($);--ne){const he=se[ne],ge=Z.get(he);if(ge!==void 0){a.set($,he);for(const ve of ge){if(t.has($))break;ve($)}}if(ne===0&&!K&&q!==void 0){const ve=q.get(he);if(ve!==void 0)for(const te of ve){if(t.has($))break;te($)}}}}else if(ee==="bubble"){if(q===void 0)return;for(let ne=0;ne<se.length&&!e.has($);++ne){const he=se[ne],ge=q.get(he);if(ge!==void 0){a.set($,he);for(const ve of ge){if(t.has($))break;ve($)}}}}i($,"stopPropagation"),i($,"stopImmediatePropagation"),d($)};return S.displayName="evtdUnifiedHandler",S}function v(){const S=function($){const{type:D,eventPhase:j}=$;if(j!==2)return;const K=f[D];K!==void 0&&K.forEach(z=>z($))};return S.displayName="evtdUnifiedWindowEventHandler",S}const g=m(),x=v();function C(S,$){const D=u[S];return D[$]===void 0&&(D[$]=new Map,window.addEventListener($,g,S==="capture")),D[$]}function y(S){return f[S]===void 0&&(f[S]=new Set,window.addEventListener(S,x)),f[S]}function I(S,$){let D=S.get($);return D===void 0&&S.set($,D=new Set),D}function N(S,$,D,j){const K=u[$][D];if(K!==void 0){const z=K.get(S);if(z!==void 0&&z.has(j))return!0}return!1}function E(S,$){const D=f[S];return!!(D!==void 0&&D.has($))}function P(S,$,D,j){let K;if(typeof j=="object"&&j.once===!0?K=Z=>{F(S,$,K,j),D(Z)}:K=D,trapOn(S,$,K,j))return;const ee=j===!0||typeof j=="object"&&j.capture===!0?"capture":"bubble",ae=C(ee,S),se=I(ae,$);if(se.has(K)||se.add(K),$===window){const Z=y(S);Z.has(K)||Z.add(K)}}function F(S,$,D,j){if(trapOff(S,$,D,j))return;const z=j===!0||typeof j=="object"&&j.capture===!0,ee=z?"capture":"bubble",ae=C(ee,S),se=I(ae,$);if($===window&&!N($,z?"bubble":"capture",S,D)&&E(S,D)){const q=f[S];q.delete(D),q.size===0&&(window.removeEventListener(S,x),f[S]=void 0)}se.has(D)&&se.delete(D),se.size===0&&ae.delete($),ae.size===0&&(window.removeEventListener(S,g,ee==="capture"),u[ee][S]=void 0)}return{on:P,off:F}}const{on,off}=createDelegate(),mousePositionRef=ref(null);function clickHandler(e){if(e.clientX>0||e.clientY>0)mousePositionRef.value={x:e.clientX,y:e.clientY};else{const{target:t}=e;if(t instanceof Element){const{left:o,top:r,width:n,height:i}=t.getBoundingClientRect();o>0||r>0?mousePositionRef.value={x:o+n/2,y:r+i/2}:mousePositionRef.value={x:0,y:0}}else mousePositionRef.value=null}}let usedCount$2=0,managable$2=!0;function useClickPosition(){if(!isBrowser$1)return readonly(ref(null));usedCount$2===0&&on("click",document,clickHandler,!0);const e=()=>{usedCount$2+=1};return managable$2&&(managable$2=hasInstance())?(onBeforeMount(e),onBeforeUnmount(()=>{usedCount$2-=1,usedCount$2===0&&off("click",document,clickHandler,!0)})):e(),readonly(mousePositionRef)}const clickedTimeRef=ref(void 0);let usedCount$1=0;function handleClick(){clickedTimeRef.value=Date.now()}let managable$1=!0;function useClicked(e){if(!isBrowser$1)return readonly(ref(!1));const t=ref(!1);let o=null;function r(){o!==null&&window.clearTimeout(o)}function n(){r(),t.value=!0,o=window.setTimeout(()=>{t.value=!1},e)}usedCount$1===0&&on("click",window,handleClick,!0);const i=()=>{usedCount$1+=1,on("click",window,n,!0)};return managable$1&&(managable$1=hasInstance())?(onBeforeMount(i),onBeforeUnmount(()=>{usedCount$1-=1,usedCount$1===0&&off("click",window,handleClick,!0),off("click",window,n,!0),r()})):i(),readonly(t)}let usedCount=0;const supportMatchMedia=typeof window<"u"&&window.matchMedia!==void 0,osTheme=ref(null);let darkMql,lightMql;function handleDarkMqlChange(e){e.matches&&(osTheme.value="dark")}function handleLightMqlChange(e){e.matches&&(osTheme.value="light")}function init(){darkMql=window.matchMedia("(prefers-color-scheme: dark)"),lightMql=window.matchMedia("(prefers-color-scheme: light)"),darkMql.matches?osTheme.value="dark":lightMql.matches?osTheme.value="light":osTheme.value=null,darkMql.addEventListener?(darkMql.addEventListener("change",handleDarkMqlChange),lightMql.addEventListener("change",handleLightMqlChange)):darkMql.addListener&&(darkMql.addListener(handleDarkMqlChange),lightMql.addListener(handleLightMqlChange))}function clean(){"removeEventListener"in darkMql?(darkMql.removeEventListener("change",handleDarkMqlChange),lightMql.removeEventListener("change",handleLightMqlChange)):"removeListener"in darkMql&&(darkMql.removeListener(handleDarkMqlChange),lightMql.removeListener(handleLightMqlChange)),darkMql=void 0,lightMql=void 0}let managable=!0;function useOsTheme(){return supportMatchMedia&&(usedCount===0&&init(),managable&&(managable=hasInstance())&&(onBeforeMount(()=>{usedCount+=1}),onBeforeUnmount(()=>{usedCount-=1,usedCount===0&&clean()}))),readonly(osTheme)}function useMergedState(e,t){return watch(e,o=>{o!==void 0&&(t.value=o)}),computed(()=>e.value===void 0?t.value:e.value)}function isMounted(){const e=ref(!1);return onMounted(()=>{e.value=!0}),readonly(e)}function useCompitable(e,t){return computed(()=>{for(const o of t)if(e[o]!==void 0)return e[o];return e[t[t.length-1]]})}const isIos=(typeof window>"u"?!1:/iPad|iPhone|iPod/.test(navigator.platform)||navigator.platform==="MacIntel"&&navigator.maxTouchPoints>1)&&!window.MSStream;function useIsIos(){return isIos}const modalBodyInjectionKey="n-modal-body",modalInjectionKey="n-modal",drawerBodyInjectionKey="n-drawer-body",popoverBodyInjectionKey="n-popover-body";function getSlot(e,t,o="default"){const r=t[o];if(r===void 0)throw new Error(`[vueuc/${e}]: slot[${o}] is empty.`);return r()}const ctxKey="@@coContext",clickoutside={mounted(e,{value:t,modifiers:o}){e[ctxKey]={handler:void 0},typeof t=="function"&&(e[ctxKey].handler=t,on("clickoutside",e,t,{capture:o.capture}))},updated(e,{value:t,modifiers:o}){const r=e[ctxKey];typeof t=="function"?r.handler?r.handler!==t&&(off("clickoutside",e,r.handler,{capture:o.capture}),r.handler=t,on("clickoutside",e,t,{capture:o.capture})):(e[ctxKey].handler=t,on("clickoutside",e,t,{capture:o.capture})):r.handler&&(off("clickoutside",e,r.handler,{capture:o.capture}),r.handler=void 0)},unmounted(e,{modifiers:t}){const{handler:o}=e[ctxKey];o&&off("clickoutside",e,o,{capture:t.capture}),e[ctxKey].handler=void 0}},clickoutside$1=clickoutside;function warn$1(e,t){console.error(`[vdirs/${e}]: ${t}`)}class ZIndexManager{constructor(){this.elementZIndex=new Map,this.nextZIndex=2e3}get elementCount(){return this.elementZIndex.size}ensureZIndex(t,o){const{elementZIndex:r}=this;if(o!==void 0){t.style.zIndex=`${o}`,r.delete(t);return}const{nextZIndex:n}=this;r.has(t)&&r.get(t)+1===this.nextZIndex||(t.style.zIndex=`${n}`,r.set(t,n),this.nextZIndex=n+1,this.squashState())}unregister(t,o){const{elementZIndex:r}=this;r.has(t)?r.delete(t):o===void 0&&warn$1("z-index-manager/unregister-element","Element not found when unregistering."),this.squashState()}squashState(){const{elementCount:t}=this;t||(this.nextZIndex=2e3),this.nextZIndex-t>2500&&this.rearrange()}rearrange(){const t=Array.from(this.elementZIndex.entries());t.sort((o,r)=>o[1]-r[1]),this.nextZIndex=2e3,t.forEach(o=>{const r=o[0],n=this.nextZIndex++;`${n}`!==r.style.zIndex&&(r.style.zIndex=`${n}`)})}}const zIndexManager=new ZIndexManager,ctx="@@ziContext",zindexable={mounted(e,t){const{value:o={}}=t,{zIndex:r,enabled:n}=o;e[ctx]={enabled:!!n,initialized:!1},n&&(zIndexManager.ensureZIndex(e,r),e[ctx].initialized=!0)},updated(e,t){const{value:o={}}=t,{zIndex:r,enabled:n}=o,i=e[ctx].enabled;n&&!i&&(zIndexManager.ensureZIndex(e,r),e[ctx].initialized=!0),e[ctx].enabled=!!n},unmounted(e,t){if(!e[ctx].initialized)return;const{value:o={}}=t,{zIndex:r}=o;zIndexManager.unregister(e,r)}},zindexable$1=zindexable,ssrContextKey=Symbol("@css-render/vue3-ssr");function createStyleString(e,t){return`<style cssr-id="${e}">
${t}
</style>`}function ssrAdapter(e,t){const o=inject(ssrContextKey,null);if(o===null){console.error("[css-render/vue3-ssr]: no ssr context found.");return}const{styles:r,ids:n}=o;n.has(e)||r!==null&&(n.add(e),r.push(createStyleString(e,t)))}const isBrowser=typeof document<"u";function useSsrAdapter(){if(isBrowser)return;const e=inject(ssrContextKey,null);if(e!==null)return{adapter:ssrAdapter,context:e}}function warn(e,t){console.error(`[vueuc/${e}]: ${t}`)}function resolveTo(e){return typeof e=="string"?document.querySelector(e):e()}const LazyTeleport=defineComponent({name:"LazyTeleport",props:{to:{type:[String,Object],default:void 0},disabled:Boolean,show:{type:Boolean,required:!0}},setup(e){return{showTeleport:useFalseUntilTruthy(toRef(e,"show")),mergedTo:computed(()=>{const{to:t}=e;return t??"body"})}},render(){return this.showTeleport?this.disabled?getSlot("lazy-teleport",this.$slots):h(Teleport,{disabled:this.disabled,to:this.mergedTo},getSlot("lazy-teleport",this.$slots)):null}});var resizeObservers=[],hasActiveObservations=function(){return resizeObservers.some(function(e){return e.activeTargets.length>0})},hasSkippedObservations=function(){return resizeObservers.some(function(e){return e.skippedTargets.length>0})},msg="ResizeObserver loop completed with undelivered notifications.",deliverResizeLoopError=function(){var e;typeof ErrorEvent=="function"?e=new ErrorEvent("error",{message:msg}):(e=document.createEvent("Event"),e.initEvent("error",!1,!1),e.message=msg),window.dispatchEvent(e)},ResizeObserverBoxOptions;(function(e){e.BORDER_BOX="border-box",e.CONTENT_BOX="content-box",e.DEVICE_PIXEL_CONTENT_BOX="device-pixel-content-box"})(ResizeObserverBoxOptions||(ResizeObserverBoxOptions={}));var freeze=function(e){return Object.freeze(e)},ResizeObserverSize=function(){function e(t,o){this.inlineSize=t,this.blockSize=o,freeze(this)}return e}(),DOMRectReadOnly=function(){function e(t,o,r,n){return this.x=t,this.y=o,this.width=r,this.height=n,this.top=this.y,this.left=this.x,this.bottom=this.top+this.height,this.right=this.left+this.width,freeze(this)}return e.prototype.toJSON=function(){var t=this,o=t.x,r=t.y,n=t.top,i=t.right,a=t.bottom,l=t.left,s=t.width,d=t.height;return{x:o,y:r,top:n,right:i,bottom:a,left:l,width:s,height:d}},e.fromRect=function(t){return new e(t.x,t.y,t.width,t.height)},e}(),isSVG=function(e){return e instanceof SVGElement&&"getBBox"in e},isHidden=function(e){if(isSVG(e)){var t=e.getBBox(),o=t.width,r=t.height;return!o&&!r}var n=e,i=n.offsetWidth,a=n.offsetHeight;return!(i||a||e.getClientRects().length)},isElement=function(e){var t;if(e instanceof Element)return!0;var o=(t=e==null?void 0:e.ownerDocument)===null||t===void 0?void 0:t.defaultView;return!!(o&&e instanceof o.Element)},isReplacedElement=function(e){switch(e.tagName){case"INPUT":if(e.type!=="image")break;case"VIDEO":case"AUDIO":case"EMBED":case"OBJECT":case"CANVAS":case"IFRAME":case"IMG":return!0}return!1},global$1=typeof window<"u"?window:{},cache=new WeakMap,scrollRegexp=/auto|scroll/,verticalRegexp=/^tb|vertical/,IE=/msie|trident/i.test(global$1.navigator&&global$1.navigator.userAgent),parseDimension=function(e){return parseFloat(e||"0")},size=function(e,t,o){return e===void 0&&(e=0),t===void 0&&(t=0),o===void 0&&(o=!1),new ResizeObserverSize((o?t:e)||0,(o?e:t)||0)},zeroBoxes=freeze({devicePixelContentBoxSize:size(),borderBoxSize:size(),contentBoxSize:size(),contentRect:new DOMRectReadOnly(0,0,0,0)}),calculateBoxSizes=function(e,t){if(t===void 0&&(t=!1),cache.has(e)&&!t)return cache.get(e);if(isHidden(e))return cache.set(e,zeroBoxes),zeroBoxes;var o=getComputedStyle(e),r=isSVG(e)&&e.ownerSVGElement&&e.getBBox(),n=!IE&&o.boxSizing==="border-box",i=verticalRegexp.test(o.writingMode||""),a=!r&&scrollRegexp.test(o.overflowY||""),l=!r&&scrollRegexp.test(o.overflowX||""),s=r?0:parseDimension(o.paddingTop),d=r?0:parseDimension(o.paddingRight),u=r?0:parseDimension(o.paddingBottom),f=r?0:parseDimension(o.paddingLeft),m=r?0:parseDimension(o.borderTopWidth),v=r?0:parseDimension(o.borderRightWidth),g=r?0:parseDimension(o.borderBottomWidth),x=r?0:parseDimension(o.borderLeftWidth),C=f+d,y=s+u,I=x+v,N=m+g,E=l?e.offsetHeight-N-e.clientHeight:0,P=a?e.offsetWidth-I-e.clientWidth:0,F=n?C+I:0,S=n?y+N:0,$=r?r.width:parseDimension(o.width)-F-P,D=r?r.height:parseDimension(o.height)-S-E,j=$+C+P+I,K=D+y+E+N,z=freeze({devicePixelContentBoxSize:size(Math.round($*devicePixelRatio),Math.round(D*devicePixelRatio),i),borderBoxSize:size(j,K,i),contentBoxSize:size($,D,i),contentRect:new DOMRectReadOnly(f,s,$,D)});return cache.set(e,z),z},calculateBoxSize=function(e,t,o){var r=calculateBoxSizes(e,o),n=r.borderBoxSize,i=r.contentBoxSize,a=r.devicePixelContentBoxSize;switch(t){case ResizeObserverBoxOptions.DEVICE_PIXEL_CONTENT_BOX:return a;case ResizeObserverBoxOptions.BORDER_BOX:return n;default:return i}},ResizeObserverEntry=function(){function e(t){var o=calculateBoxSizes(t);this.target=t,this.contentRect=o.contentRect,this.borderBoxSize=freeze([o.borderBoxSize]),this.contentBoxSize=freeze([o.contentBoxSize]),this.devicePixelContentBoxSize=freeze([o.devicePixelContentBoxSize])}return e}(),calculateDepthForNode=function(e){if(isHidden(e))return 1/0;for(var t=0,o=e.parentNode;o;)t+=1,o=o.parentNode;return t},broadcastActiveObservations=function(){var e=1/0,t=[];resizeObservers.forEach(function(a){if(a.activeTargets.length!==0){var l=[];a.activeTargets.forEach(function(d){var u=new ResizeObserverEntry(d.target),f=calculateDepthForNode(d.target);l.push(u),d.lastReportedSize=calculateBoxSize(d.target,d.observedBox),f<e&&(e=f)}),t.push(function(){a.callback.call(a.observer,l,a.observer)}),a.activeTargets.splice(0,a.activeTargets.length)}});for(var o=0,r=t;o<r.length;o++){var n=r[o];n()}return e},gatherActiveObservationsAtDepth=function(e){resizeObservers.forEach(function(o){o.activeTargets.splice(0,o.activeTargets.length),o.skippedTargets.splice(0,o.skippedTargets.length),o.observationTargets.forEach(function(n){n.isActive()&&(calculateDepthForNode(n.target)>e?o.activeTargets.push(n):o.skippedTargets.push(n))})})},process=function(){var e=0;for(gatherActiveObservationsAtDepth(e);hasActiveObservations();)e=broadcastActiveObservations(),gatherActiveObservationsAtDepth(e);return hasSkippedObservations()&&deliverResizeLoopError(),e>0},trigger,callbacks=[],notify=function(){return callbacks.splice(0).forEach(function(e){return e()})},queueMicroTask=function(e){if(!trigger){var t=0,o=document.createTextNode(""),r={characterData:!0};new MutationObserver(function(){return notify()}).observe(o,r),trigger=function(){o.textContent="".concat(t?t--:t++)}}callbacks.push(e),trigger()},queueResizeObserver=function(e){queueMicroTask(function(){requestAnimationFrame(e)})},watching=0,isWatching=function(){return!!watching},CATCH_PERIOD=250,observerConfig={attributes:!0,characterData:!0,childList:!0,subtree:!0},events=["resize","load","transitionend","animationend","animationstart","animationiteration","keyup","keydown","mouseup","mousedown","mouseover","mouseout","blur","focus"],time=function(e){return e===void 0&&(e=0),Date.now()+e},scheduled=!1,Scheduler=function(){function e(){var t=this;this.stopped=!0,this.listener=function(){return t.schedule()}}return e.prototype.run=function(t){var o=this;if(t===void 0&&(t=CATCH_PERIOD),!scheduled){scheduled=!0;var r=time(t);queueResizeObserver(function(){var n=!1;try{n=process()}finally{if(scheduled=!1,t=r-time(),!isWatching())return;n?o.run(1e3):t>0?o.run(t):o.start()}})}},e.prototype.schedule=function(){this.stop(),this.run()},e.prototype.observe=function(){var t=this,o=function(){return t.observer&&t.observer.observe(document.body,observerConfig)};document.body?o():global$1.addEventListener("DOMContentLoaded",o)},e.prototype.start=function(){var t=this;this.stopped&&(this.stopped=!1,this.observer=new MutationObserver(this.listener),this.observe(),events.forEach(function(o){return global$1.addEventListener(o,t.listener,!0)}))},e.prototype.stop=function(){var t=this;this.stopped||(this.observer&&this.observer.disconnect(),events.forEach(function(o){return global$1.removeEventListener(o,t.listener,!0)}),this.stopped=!0)},e}(),scheduler=new Scheduler,updateCount=function(e){!watching&&e>0&&scheduler.start(),watching+=e,!watching&&scheduler.stop()},skipNotifyOnElement=function(e){return!isSVG(e)&&!isReplacedElement(e)&&getComputedStyle(e).display==="inline"},ResizeObservation=function(){function e(t,o){this.target=t,this.observedBox=o||ResizeObserverBoxOptions.CONTENT_BOX,this.lastReportedSize={inlineSize:0,blockSize:0}}return e.prototype.isActive=function(){var t=calculateBoxSize(this.target,this.observedBox,!0);return skipNotifyOnElement(this.target)&&(this.lastReportedSize=t),this.lastReportedSize.inlineSize!==t.inlineSize||this.lastReportedSize.blockSize!==t.blockSize},e}(),ResizeObserverDetail=function(){function e(t,o){this.activeTargets=[],this.skippedTargets=[],this.observationTargets=[],this.observer=t,this.callback=o}return e}(),observerMap=new WeakMap,getObservationIndex=function(e,t){for(var o=0;o<e.length;o+=1)if(e[o].target===t)return o;return-1},ResizeObserverController=function(){function e(){}return e.connect=function(t,o){var r=new ResizeObserverDetail(t,o);observerMap.set(t,r)},e.observe=function(t,o,r){var n=observerMap.get(t),i=n.observationTargets.length===0;getObservationIndex(n.observationTargets,o)<0&&(i&&resizeObservers.push(n),n.observationTargets.push(new ResizeObservation(o,r&&r.box)),updateCount(1),scheduler.schedule())},e.unobserve=function(t,o){var r=observerMap.get(t),n=getObservationIndex(r.observationTargets,o),i=r.observationTargets.length===1;n>=0&&(i&&resizeObservers.splice(resizeObservers.indexOf(r),1),r.observationTargets.splice(n,1),updateCount(-1))},e.disconnect=function(t){var o=this,r=observerMap.get(t);r.observationTargets.slice().forEach(function(n){return o.unobserve(t,n.target)}),r.activeTargets.splice(0,r.activeTargets.length)},e}(),ResizeObserver=function(){function e(t){if(arguments.length===0)throw new TypeError("Failed to construct 'ResizeObserver': 1 argument required, but only 0 present.");if(typeof t!="function")throw new TypeError("Failed to construct 'ResizeObserver': The callback provided as parameter 1 is not a function.");ResizeObserverController.connect(this,t)}return e.prototype.observe=function(t,o){if(arguments.length===0)throw new TypeError("Failed to execute 'observe' on 'ResizeObserver': 1 argument required, but only 0 present.");if(!isElement(t))throw new TypeError("Failed to execute 'observe' on 'ResizeObserver': parameter 1 is not of type 'Element");ResizeObserverController.observe(this,t,o)},e.prototype.unobserve=function(t){if(arguments.length===0)throw new TypeError("Failed to execute 'unobserve' on 'ResizeObserver': 1 argument required, but only 0 present.");if(!isElement(t))throw new TypeError("Failed to execute 'unobserve' on 'ResizeObserver': parameter 1 is not of type 'Element");ResizeObserverController.unobserve(this,t)},e.prototype.disconnect=function(){ResizeObserverController.disconnect(this)},e.toString=function(){return"function ResizeObserver () { [polyfill code] }"},e}();class ResizeObserverDelegate{constructor(){this.handleResize=this.handleResize.bind(this),this.observer=new(typeof window<"u"&&window.ResizeObserver||ResizeObserver)(this.handleResize),this.elHandlersMap=new Map}handleResize(t){for(const o of t){const r=this.elHandlersMap.get(o.target);r!==void 0&&r(o)}}registerHandler(t,o){this.elHandlersMap.set(t,o),this.observer.observe(t)}unregisterHandler(t){this.elHandlersMap.has(t)&&(this.elHandlersMap.delete(t),this.observer.unobserve(t))}}const resizeObserverManager=new ResizeObserverDelegate,VResizeObserver=defineComponent({name:"ResizeObserver",props:{onResize:Function},setup(e){let t=!1;const o=getCurrentInstance().proxy;function r(n){const{onResize:i}=e;i!==void 0&&i(n)}onMounted(()=>{const n=o.$el;if(n===void 0){warn("resize-observer","$el does not exist.");return}if(n.nextElementSibling!==n.nextSibling&&n.nodeType===3&&n.nodeValue!==""){warn("resize-observer","$el can not be observed (it may be a text node).");return}n.nextElementSibling!==null&&(resizeObserverManager.registerHandler(n.nextElementSibling,r),t=!0)}),onBeforeUnmount(()=>{t&&resizeObserverManager.unregisterHandler(o.$el.nextElementSibling)})},render(){return renderSlot(this.$slots,"default")}});function isHTMLElement(e){return e instanceof HTMLElement}function focusFirstDescendant(e){for(let t=0;t<e.childNodes.length;t++){const o=e.childNodes[t];if(isHTMLElement(o)&&(attemptFocus(o)||focusFirstDescendant(o)))return!0}return!1}function focusLastDescendant(e){for(let t=e.childNodes.length-1;t>=0;t--){const o=e.childNodes[t];if(isHTMLElement(o)&&(attemptFocus(o)||focusLastDescendant(o)))return!0}return!1}function attemptFocus(e){if(!isFocusable(e))return!1;try{e.focus({preventScroll:!0})}catch{}return document.activeElement===e}function isFocusable(e){if(e.tabIndex>0||e.tabIndex===0&&e.getAttribute("tabIndex")!==null)return!0;if(e.getAttribute("disabled"))return!1;switch(e.nodeName){case"A":return!!e.href&&e.rel!=="ignore";case"INPUT":return e.type!=="hidden"&&e.type!=="file";case"BUTTON":case"SELECT":case"TEXTAREA":return!0;default:return!1}}let stack=[];const FocusTrap=defineComponent({name:"FocusTrap",props:{disabled:Boolean,active:Boolean,autoFocus:{type:Boolean,default:!0},onEsc:Function,initialFocusTo:String,finalFocusTo:String,returnFocusOnDeactivated:{type:Boolean,default:!0}},setup(e){const t=createId(),o=ref(null),r=ref(null);let n=!1,i=!1;const a=typeof document>"u"?null:document.activeElement;function l(){return stack[stack.length-1]===t}function s(C){var y;C.code==="Escape"&&l()&&((y=e.onEsc)===null||y===void 0||y.call(e,C))}onMounted(()=>{watch(()=>e.active,C=>{C?(f(),on("keydown",document,s)):(off("keydown",document,s),n&&m())},{immediate:!0})}),onBeforeUnmount(()=>{off("keydown",document,s),n&&m()});function d(C){if(!i&&l()){const y=u();if(y===null||y.contains(getPreciseEventTarget(C)))return;v("first")}}function u(){const C=o.value;if(C===null)return null;let y=C;for(;y=y.nextSibling,!(y===null||y instanceof Element&&y.tagName==="DIV"););return y}function f(){var C;if(!e.disabled){if(stack.push(t),e.autoFocus){const{initialFocusTo:y}=e;y===void 0?v("first"):(C=resolveTo(y))===null||C===void 0||C.focus({preventScroll:!0})}n=!0,document.addEventListener("focus",d,!0)}}function m(){var C;if(e.disabled||(document.removeEventListener("focus",d,!0),stack=stack.filter(I=>I!==t),l()))return;const{finalFocusTo:y}=e;y!==void 0?(C=resolveTo(y))===null||C===void 0||C.focus({preventScroll:!0}):e.returnFocusOnDeactivated&&a instanceof HTMLElement&&(i=!0,a.focus({preventScroll:!0}),i=!1)}function v(C){if(l()&&e.active){const y=o.value,I=r.value;if(y!==null&&I!==null){const N=u();if(N==null||N===I){i=!0,y.focus({preventScroll:!0}),i=!1;return}i=!0;const E=C==="first"?focusFirstDescendant(N):focusLastDescendant(N);i=!1,E||(i=!0,y.focus({preventScroll:!0}),i=!1)}}}function g(C){if(i)return;const y=u();y!==null&&(C.relatedTarget!==null&&y.contains(C.relatedTarget)?v("last"):v("first"))}function x(C){i||(C.relatedTarget!==null&&C.relatedTarget===o.value?v("last"):v("first"))}return{focusableStartRef:o,focusableEndRef:r,focusableStyle:"position: absolute; height: 0; width: 0;",handleStartFocus:g,handleEndFocus:x}},render(){const{default:e}=this.$slots;if(e===void 0)return null;if(this.disabled)return e();const{active:t,focusableStyle:o}=this;return h(Fragment,null,[h("div",{"aria-hidden":"true",tabindex:t?"0":"-1",ref:"focusableStartRef",style:o,onFocus:this.handleStartFocus}),e(),h("div",{"aria-hidden":"true",style:o,ref:"focusableEndRef",tabindex:t?"0":"-1",onFocus:this.handleEndFocus})])}});let lockCount=0,originalMarginRight="",originalOverflow="",originalOverflowX="",originalOverflowY="";const lockHtmlScrollRightCompensationRef=ref("0px");function useLockHtmlScroll(e){if(typeof document>"u")return;const t=document.documentElement;let o,r=!1;const n=()=>{t.style.marginRight=originalMarginRight,t.style.overflow=originalOverflow,t.style.overflowX=originalOverflowX,t.style.overflowY=originalOverflowY,lockHtmlScrollRightCompensationRef.value="0px"};onMounted(()=>{o=watch(e,i=>{if(i){if(!lockCount){const a=window.innerWidth-t.offsetWidth;a>0&&(originalMarginRight=t.style.marginRight,t.style.marginRight=`${a}px`,lockHtmlScrollRightCompensationRef.value=`${a}px`),originalOverflow=t.style.overflow,originalOverflowX=t.style.overflowX,originalOverflowY=t.style.overflowY,t.style.overflow="hidden",t.style.overflowX="hidden",t.style.overflowY="hidden"}r=!0,lockCount++}else lockCount--,lockCount||n(),r=!1},{immediate:!0})}),onBeforeUnmount(()=>{o==null||o(),r&&(lockCount--,lockCount||n(),r=!1)})}const isComposingRef=ref(!1),compositionStartHandler=()=>{isComposingRef.value=!0},compositionEndHandler=()=>{isComposingRef.value=!1};let mountedCount=0;const useIsComposing=()=>(isBrowser$2&&(onBeforeMount(()=>{mountedCount||(window.addEventListener("compositionstart",compositionStartHandler),window.addEventListener("compositionend",compositionEndHandler)),mountedCount++}),onBeforeUnmount(()=>{mountedCount<=1?(window.removeEventListener("compositionstart",compositionStartHandler),window.removeEventListener("compositionend",compositionEndHandler),mountedCount=0):mountedCount--})),isComposingRef);function useReactivated(e){const t={isDeactivated:!1};let o=!1;return onActivated(()=>{if(t.isDeactivated=!1,!o){o=!0;return}e()}),onDeactivated(()=>{t.isDeactivated=!0,o||(o=!0)}),t}const formItemInjectionKey="n-form-item";function useFormItem(e,{defaultSize:t="medium",mergedSize:o,mergedDisabled:r}={}){const n=inject(formItemInjectionKey,null);provide(formItemInjectionKey,null);const i=computed(o?()=>o(n):()=>{const{size:s}=e;if(s)return s;if(n){const{mergedSize:d}=n;if(d.value!==void 0)return d.value}return t}),a=computed(r?()=>r(n):()=>{const{disabled:s}=e;return s!==void 0?s:n?n.disabled.value:!1}),l=computed(()=>{const{status:s}=e;return s||(n==null?void 0:n.mergedValidationStatus.value)});return onBeforeUnmount(()=>{n&&n.restoreValidation()}),{mergedSizeRef:i,mergedDisabledRef:a,mergedStatusRef:l,nTriggerFormBlur(){n&&n.handleContentBlur()},nTriggerFormChange(){n&&n.handleContentChange()},nTriggerFormFocus(){n&&n.handleContentFocus()},nTriggerFormInput(){n&&n.handleContentInput()}}}var freeGlobal=typeof global=="object"&&global&&global.Object===Object&&global;const freeGlobal$1=freeGlobal;var freeSelf=typeof self=="object"&&self&&self.Object===Object&&self,root=freeGlobal$1||freeSelf||Function("return this")();const root$1=root;var Symbol$1=root$1.Symbol;const Symbol$2=Symbol$1;var objectProto$a=Object.prototype,hasOwnProperty$8=objectProto$a.hasOwnProperty,nativeObjectToString$1=objectProto$a.toString,symToStringTag$1=Symbol$2?Symbol$2.toStringTag:void 0;function getRawTag(e){var t=hasOwnProperty$8.call(e,symToStringTag$1),o=e[symToStringTag$1];try{e[symToStringTag$1]=void 0;var r=!0}catch{}var n=nativeObjectToString$1.call(e);return r&&(t?e[symToStringTag$1]=o:delete e[symToStringTag$1]),n}var objectProto$9=Object.prototype,nativeObjectToString=objectProto$9.toString;function objectToString(e){return nativeObjectToString.call(e)}var nullTag="[object Null]",undefinedTag="[object Undefined]",symToStringTag=Symbol$2?Symbol$2.toStringTag:void 0;function baseGetTag(e){return e==null?e===void 0?undefinedTag:nullTag:symToStringTag&&symToStringTag in Object(e)?getRawTag(e):objectToString(e)}function isObjectLike(e){return e!=null&&typeof e=="object"}var symbolTag="[object Symbol]";function isSymbol(e){return typeof e=="symbol"||isObjectLike(e)&&baseGetTag(e)==symbolTag}function arrayMap(e,t){for(var o=-1,r=e==null?0:e.length,n=Array(r);++o<r;)n[o]=t(e[o],o,e);return n}var isArray=Array.isArray;const isArray$1=isArray;var INFINITY=1/0,symbolProto=Symbol$2?Symbol$2.prototype:void 0,symbolToString=symbolProto?symbolProto.toString:void 0;function baseToString(e){if(typeof e=="string")return e;if(isArray$1(e))return arrayMap(e,baseToString)+"";if(isSymbol(e))return symbolToString?symbolToString.call(e):"";var t=e+"";return t=="0"&&1/e==-INFINITY?"-0":t}function isObject(e){var t=typeof e;return e!=null&&(t=="object"||t=="function")}function identity(e){return e}var asyncTag="[object AsyncFunction]",funcTag$1="[object Function]",genTag="[object GeneratorFunction]",proxyTag="[object Proxy]";function isFunction(e){if(!isObject(e))return!1;var t=baseGetTag(e);return t==funcTag$1||t==genTag||t==asyncTag||t==proxyTag}var coreJsData=root$1["__core-js_shared__"];const coreJsData$1=coreJsData;var maskSrcKey=function(){var e=/[^.]+$/.exec(coreJsData$1&&coreJsData$1.keys&&coreJsData$1.keys.IE_PROTO||"");return e?"Symbol(src)_1."+e:""}();function isMasked(e){return!!maskSrcKey&&maskSrcKey in e}var funcProto$2=Function.prototype,funcToString$2=funcProto$2.toString;function toSource(e){if(e!=null){try{return funcToString$2.call(e)}catch{}try{return e+""}catch{}}return""}var reRegExpChar=/[\\^$.*+?()[\]{}|]/g,reIsHostCtor=/^\[object .+?Constructor\]$/,funcProto$1=Function.prototype,objectProto$8=Object.prototype,funcToString$1=funcProto$1.toString,hasOwnProperty$7=objectProto$8.hasOwnProperty,reIsNative=RegExp("^"+funcToString$1.call(hasOwnProperty$7).replace(reRegExpChar,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");function baseIsNative(e){if(!isObject(e)||isMasked(e))return!1;var t=isFunction(e)?reIsNative:reIsHostCtor;return t.test(toSource(e))}function getValue(e,t){return e==null?void 0:e[t]}function getNative(e,t){var o=getValue(e,t);return baseIsNative(o)?o:void 0}var objectCreate=Object.create,baseCreate=function(){function e(){}return function(t){if(!isObject(t))return{};if(objectCreate)return objectCreate(t);e.prototype=t;var o=new e;return e.prototype=void 0,o}}();const baseCreate$1=baseCreate;function apply(e,t,o){switch(o.length){case 0:return e.call(t);case 1:return e.call(t,o[0]);case 2:return e.call(t,o[0],o[1]);case 3:return e.call(t,o[0],o[1],o[2])}return e.apply(t,o)}function copyArray(e,t){var o=-1,r=e.length;for(t||(t=Array(r));++o<r;)t[o]=e[o];return t}var HOT_COUNT=800,HOT_SPAN=16,nativeNow=Date.now;function shortOut(e){var t=0,o=0;return function(){var r=nativeNow(),n=HOT_SPAN-(r-o);if(o=r,n>0){if(++t>=HOT_COUNT)return arguments[0]}else t=0;return e.apply(void 0,arguments)}}function constant(e){return function(){return e}}var defineProperty=function(){try{var e=getNative(Object,"defineProperty");return e({},"",{}),e}catch{}}();const defineProperty$1=defineProperty;var baseSetToString=defineProperty$1?function(e,t){return defineProperty$1(e,"toString",{configurable:!0,enumerable:!1,value:constant(t),writable:!0})}:identity;const baseSetToString$1=baseSetToString;var setToString=shortOut(baseSetToString$1);const setToString$1=setToString;var MAX_SAFE_INTEGER$1=9007199254740991,reIsUint=/^(?:0|[1-9]\d*)$/;function isIndex(e,t){var o=typeof e;return t=t??MAX_SAFE_INTEGER$1,!!t&&(o=="number"||o!="symbol"&&reIsUint.test(e))&&e>-1&&e%1==0&&e<t}function baseAssignValue(e,t,o){t=="__proto__"&&defineProperty$1?defineProperty$1(e,t,{configurable:!0,enumerable:!0,value:o,writable:!0}):e[t]=o}function eq(e,t){return e===t||e!==e&&t!==t}var objectProto$7=Object.prototype,hasOwnProperty$6=objectProto$7.hasOwnProperty;function assignValue(e,t,o){var r=e[t];(!(hasOwnProperty$6.call(e,t)&&eq(r,o))||o===void 0&&!(t in e))&&baseAssignValue(e,t,o)}function copyObject(e,t,o,r){var n=!o;o||(o={});for(var i=-1,a=t.length;++i<a;){var l=t[i],s=r?r(o[l],e[l],l,o,e):void 0;s===void 0&&(s=e[l]),n?baseAssignValue(o,l,s):assignValue(o,l,s)}return o}var nativeMax=Math.max;function overRest(e,t,o){return t=nativeMax(t===void 0?e.length-1:t,0),function(){for(var r=arguments,n=-1,i=nativeMax(r.length-t,0),a=Array(i);++n<i;)a[n]=r[t+n];n=-1;for(var l=Array(t+1);++n<t;)l[n]=r[n];return l[t]=o(a),apply(e,this,l)}}function baseRest(e,t){return setToString$1(overRest(e,t,identity),e+"")}var MAX_SAFE_INTEGER=9007199254740991;function isLength(e){return typeof e=="number"&&e>-1&&e%1==0&&e<=MAX_SAFE_INTEGER}function isArrayLike(e){return e!=null&&isLength(e.length)&&!isFunction(e)}function isIterateeCall(e,t,o){if(!isObject(o))return!1;var r=typeof t;return(r=="number"?isArrayLike(o)&&isIndex(t,o.length):r=="string"&&t in o)?eq(o[t],e):!1}function createAssigner(e){return baseRest(function(t,o){var r=-1,n=o.length,i=n>1?o[n-1]:void 0,a=n>2?o[2]:void 0;for(i=e.length>3&&typeof i=="function"?(n--,i):void 0,a&&isIterateeCall(o[0],o[1],a)&&(i=n<3?void 0:i,n=1),t=Object(t);++r<n;){var l=o[r];l&&e(t,l,r,i)}return t})}var objectProto$6=Object.prototype;function isPrototype(e){var t=e&&e.constructor,o=typeof t=="function"&&t.prototype||objectProto$6;return e===o}function baseTimes(e,t){for(var o=-1,r=Array(e);++o<e;)r[o]=t(o);return r}var argsTag$1="[object Arguments]";function baseIsArguments(e){return isObjectLike(e)&&baseGetTag(e)==argsTag$1}var objectProto$5=Object.prototype,hasOwnProperty$5=objectProto$5.hasOwnProperty,propertyIsEnumerable=objectProto$5.propertyIsEnumerable,isArguments=baseIsArguments(function(){return arguments}())?baseIsArguments:function(e){return isObjectLike(e)&&hasOwnProperty$5.call(e,"callee")&&!propertyIsEnumerable.call(e,"callee")};const isArguments$1=isArguments;function stubFalse(){return!1}var freeExports$2=typeof exports=="object"&&exports&&!exports.nodeType&&exports,freeModule$2=freeExports$2&&typeof module=="object"&&module&&!module.nodeType&&module,moduleExports$2=freeModule$2&&freeModule$2.exports===freeExports$2,Buffer$1=moduleExports$2?root$1.Buffer:void 0,nativeIsBuffer=Buffer$1?Buffer$1.isBuffer:void 0,isBuffer=nativeIsBuffer||stubFalse;const isBuffer$1=isBuffer;var argsTag="[object Arguments]",arrayTag="[object Array]",boolTag="[object Boolean]",dateTag="[object Date]",errorTag="[object Error]",funcTag="[object Function]",mapTag="[object Map]",numberTag="[object Number]",objectTag$1="[object Object]",regexpTag="[object RegExp]",setTag="[object Set]",stringTag="[object String]",weakMapTag="[object WeakMap]",arrayBufferTag="[object ArrayBuffer]",dataViewTag="[object DataView]",float32Tag="[object Float32Array]",float64Tag="[object Float64Array]",int8Tag="[object Int8Array]",int16Tag="[object Int16Array]",int32Tag="[object Int32Array]",uint8Tag="[object Uint8Array]",uint8ClampedTag="[object Uint8ClampedArray]",uint16Tag="[object Uint16Array]",uint32Tag="[object Uint32Array]",typedArrayTags={};typedArrayTags[float32Tag]=typedArrayTags[float64Tag]=typedArrayTags[int8Tag]=typedArrayTags[int16Tag]=typedArrayTags[int32Tag]=typedArrayTags[uint8Tag]=typedArrayTags[uint8ClampedTag]=typedArrayTags[uint16Tag]=typedArrayTags[uint32Tag]=!0;typedArrayTags[argsTag]=typedArrayTags[arrayTag]=typedArrayTags[arrayBufferTag]=typedArrayTags[boolTag]=typedArrayTags[dataViewTag]=typedArrayTags[dateTag]=typedArrayTags[errorTag]=typedArrayTags[funcTag]=typedArrayTags[mapTag]=typedArrayTags[numberTag]=typedArrayTags[objectTag$1]=typedArrayTags[regexpTag]=typedArrayTags[setTag]=typedArrayTags[stringTag]=typedArrayTags[weakMapTag]=!1;function baseIsTypedArray(e){return isObjectLike(e)&&isLength(e.length)&&!!typedArrayTags[baseGetTag(e)]}function baseUnary(e){return function(t){return e(t)}}var freeExports$1=typeof exports=="object"&&exports&&!exports.nodeType&&exports,freeModule$1=freeExports$1&&typeof module=="object"&&module&&!module.nodeType&&module,moduleExports$1=freeModule$1&&freeModule$1.exports===freeExports$1,freeProcess=moduleExports$1&&freeGlobal$1.process,nodeUtil=function(){try{var e=freeModule$1&&freeModule$1.require&&freeModule$1.require("util").types;return e||freeProcess&&freeProcess.binding&&freeProcess.binding("util")}catch{}}();const nodeUtil$1=nodeUtil;var nodeIsTypedArray=nodeUtil$1&&nodeUtil$1.isTypedArray,isTypedArray=nodeIsTypedArray?baseUnary(nodeIsTypedArray):baseIsTypedArray;const isTypedArray$1=isTypedArray;var objectProto$4=Object.prototype,hasOwnProperty$4=objectProto$4.hasOwnProperty;function arrayLikeKeys(e,t){var o=isArray$1(e),r=!o&&isArguments$1(e),n=!o&&!r&&isBuffer$1(e),i=!o&&!r&&!n&&isTypedArray$1(e),a=o||r||n||i,l=a?baseTimes(e.length,String):[],s=l.length;for(var d in e)(t||hasOwnProperty$4.call(e,d))&&!(a&&(d=="length"||n&&(d=="offset"||d=="parent")||i&&(d=="buffer"||d=="byteLength"||d=="byteOffset")||isIndex(d,s)))&&l.push(d);return l}function overArg(e,t){return function(o){return e(t(o))}}function nativeKeysIn(e){var t=[];if(e!=null)for(var o in Object(e))t.push(o);return t}var objectProto$3=Object.prototype,hasOwnProperty$3=objectProto$3.hasOwnProperty;function baseKeysIn(e){if(!isObject(e))return nativeKeysIn(e);var t=isPrototype(e),o=[];for(var r in e)r=="constructor"&&(t||!hasOwnProperty$3.call(e,r))||o.push(r);return o}function keysIn(e){return isArrayLike(e)?arrayLikeKeys(e,!0):baseKeysIn(e)}var nativeCreate=getNative(Object,"create");const nativeCreate$1=nativeCreate;function hashClear(){this.__data__=nativeCreate$1?nativeCreate$1(null):{},this.size=0}function hashDelete(e){var t=this.has(e)&&delete this.__data__[e];return this.size-=t?1:0,t}var HASH_UNDEFINED$1="__lodash_hash_undefined__",objectProto$2=Object.prototype,hasOwnProperty$2=objectProto$2.hasOwnProperty;function hashGet(e){var t=this.__data__;if(nativeCreate$1){var o=t[e];return o===HASH_UNDEFINED$1?void 0:o}return hasOwnProperty$2.call(t,e)?t[e]:void 0}var objectProto$1=Object.prototype,hasOwnProperty$1=objectProto$1.hasOwnProperty;function hashHas(e){var t=this.__data__;return nativeCreate$1?t[e]!==void 0:hasOwnProperty$1.call(t,e)}var HASH_UNDEFINED="__lodash_hash_undefined__";function hashSet(e,t){var o=this.__data__;return this.size+=this.has(e)?0:1,o[e]=nativeCreate$1&&t===void 0?HASH_UNDEFINED:t,this}function Hash(e){var t=-1,o=e==null?0:e.length;for(this.clear();++t<o;){var r=e[t];this.set(r[0],r[1])}}Hash.prototype.clear=hashClear;Hash.prototype.delete=hashDelete;Hash.prototype.get=hashGet;Hash.prototype.has=hashHas;Hash.prototype.set=hashSet;function listCacheClear(){this.__data__=[],this.size=0}function assocIndexOf(e,t){for(var o=e.length;o--;)if(eq(e[o][0],t))return o;return-1}var arrayProto=Array.prototype,splice=arrayProto.splice;function listCacheDelete(e){var t=this.__data__,o=assocIndexOf(t,e);if(o<0)return!1;var r=t.length-1;return o==r?t.pop():splice.call(t,o,1),--this.size,!0}function listCacheGet(e){var t=this.__data__,o=assocIndexOf(t,e);return o<0?void 0:t[o][1]}function listCacheHas(e){return assocIndexOf(this.__data__,e)>-1}function listCacheSet(e,t){var o=this.__data__,r=assocIndexOf(o,e);return r<0?(++this.size,o.push([e,t])):o[r][1]=t,this}function ListCache(e){var t=-1,o=e==null?0:e.length;for(this.clear();++t<o;){var r=e[t];this.set(r[0],r[1])}}ListCache.prototype.clear=listCacheClear;ListCache.prototype.delete=listCacheDelete;ListCache.prototype.get=listCacheGet;ListCache.prototype.has=listCacheHas;ListCache.prototype.set=listCacheSet;var Map$1=getNative(root$1,"Map");const Map$2=Map$1;function mapCacheClear(){this.size=0,this.__data__={hash:new Hash,map:new(Map$2||ListCache),string:new Hash}}function isKeyable(e){var t=typeof e;return t=="string"||t=="number"||t=="symbol"||t=="boolean"?e!=="__proto__":e===null}function getMapData(e,t){var o=e.__data__;return isKeyable(t)?o[typeof t=="string"?"string":"hash"]:o.map}function mapCacheDelete(e){var t=getMapData(this,e).delete(e);return this.size-=t?1:0,t}function mapCacheGet(e){return getMapData(this,e).get(e)}function mapCacheHas(e){return getMapData(this,e).has(e)}function mapCacheSet(e,t){var o=getMapData(this,e),r=o.size;return o.set(e,t),this.size+=o.size==r?0:1,this}function MapCache(e){var t=-1,o=e==null?0:e.length;for(this.clear();++t<o;){var r=e[t];this.set(r[0],r[1])}}MapCache.prototype.clear=mapCacheClear;MapCache.prototype.delete=mapCacheDelete;MapCache.prototype.get=mapCacheGet;MapCache.prototype.has=mapCacheHas;MapCache.prototype.set=mapCacheSet;function toString(e){return e==null?"":baseToString(e)}var getPrototype=overArg(Object.getPrototypeOf,Object);const getPrototype$1=getPrototype;var objectTag="[object Object]",funcProto=Function.prototype,objectProto=Object.prototype,funcToString=funcProto.toString,hasOwnProperty=objectProto.hasOwnProperty,objectCtorString=funcToString.call(Object);function isPlainObject(e){if(!isObjectLike(e)||baseGetTag(e)!=objectTag)return!1;var t=getPrototype$1(e);if(t===null)return!0;var o=hasOwnProperty.call(t,"constructor")&&t.constructor;return typeof o=="function"&&o instanceof o&&funcToString.call(o)==objectCtorString}function baseSlice(e,t,o){var r=-1,n=e.length;t<0&&(t=-t>n?0:n+t),o=o>n?n:o,o<0&&(o+=n),n=t>o?0:o-t>>>0,t>>>=0;for(var i=Array(n);++r<n;)i[r]=e[r+t];return i}function castSlice(e,t,o){var r=e.length;return o=o===void 0?r:o,!t&&o>=r?e:baseSlice(e,t,o)}var rsAstralRange$1="\\ud800-\\udfff",rsComboMarksRange$1="\\u0300-\\u036f",reComboHalfMarksRange$1="\\ufe20-\\ufe2f",rsComboSymbolsRange$1="\\u20d0-\\u20ff",rsComboRange$1=rsComboMarksRange$1+reComboHalfMarksRange$1+rsComboSymbolsRange$1,rsVarRange$1="\\ufe0e\\ufe0f",rsZWJ$1="\\u200d",reHasUnicode=RegExp("["+rsZWJ$1+rsAstralRange$1+rsComboRange$1+rsVarRange$1+"]");function hasUnicode(e){return reHasUnicode.test(e)}function asciiToArray(e){return e.split("")}var rsAstralRange="\\ud800-\\udfff",rsComboMarksRange="\\u0300-\\u036f",reComboHalfMarksRange="\\ufe20-\\ufe2f",rsComboSymbolsRange="\\u20d0-\\u20ff",rsComboRange=rsComboMarksRange+reComboHalfMarksRange+rsComboSymbolsRange,rsVarRange="\\ufe0e\\ufe0f",rsAstral="["+rsAstralRange+"]",rsCombo="["+rsComboRange+"]",rsFitz="\\ud83c[\\udffb-\\udfff]",rsModifier="(?:"+rsCombo+"|"+rsFitz+")",rsNonAstral="[^"+rsAstralRange+"]",rsRegional="(?:\\ud83c[\\udde6-\\uddff]){2}",rsSurrPair="[\\ud800-\\udbff][\\udc00-\\udfff]",rsZWJ="\\u200d",reOptMod=rsModifier+"?",rsOptVar="["+rsVarRange+"]?",rsOptJoin="(?:"+rsZWJ+"(?:"+[rsNonAstral,rsRegional,rsSurrPair].join("|")+")"+rsOptVar+reOptMod+")*",rsSeq=rsOptVar+reOptMod+rsOptJoin,rsSymbol="(?:"+[rsNonAstral+rsCombo+"?",rsCombo,rsRegional,rsSurrPair,rsAstral].join("|")+")",reUnicode=RegExp(rsFitz+"(?="+rsFitz+")|"+rsSymbol+rsSeq,"g");function unicodeToArray(e){return e.match(reUnicode)||[]}function stringToArray(e){return hasUnicode(e)?unicodeToArray(e):asciiToArray(e)}function createCaseFirst(e){return function(t){t=toString(t);var o=hasUnicode(t)?stringToArray(t):void 0,r=o?o[0]:t.charAt(0),n=o?castSlice(o,1).join(""):t.slice(1);return r[e]()+n}}var upperFirst=createCaseFirst("toUpperCase");const upperFirst$1=upperFirst;function stackClear(){this.__data__=new ListCache,this.size=0}function stackDelete(e){var t=this.__data__,o=t.delete(e);return this.size=t.size,o}function stackGet(e){return this.__data__.get(e)}function stackHas(e){return this.__data__.has(e)}var LARGE_ARRAY_SIZE=200;function stackSet(e,t){var o=this.__data__;if(o instanceof ListCache){var r=o.__data__;if(!Map$2||r.length<LARGE_ARRAY_SIZE-1)return r.push([e,t]),this.size=++o.size,this;o=this.__data__=new MapCache(r)}return o.set(e,t),this.size=o.size,this}function Stack(e){var t=this.__data__=new ListCache(e);this.size=t.size}Stack.prototype.clear=stackClear;Stack.prototype.delete=stackDelete;Stack.prototype.get=stackGet;Stack.prototype.has=stackHas;Stack.prototype.set=stackSet;var freeExports=typeof exports=="object"&&exports&&!exports.nodeType&&exports,freeModule=freeExports&&typeof module=="object"&&module&&!module.nodeType&&module,moduleExports=freeModule&&freeModule.exports===freeExports,Buffer=moduleExports?root$1.Buffer:void 0,allocUnsafe=Buffer?Buffer.allocUnsafe:void 0;function cloneBuffer(e,t){if(t)return e.slice();var o=e.length,r=allocUnsafe?allocUnsafe(o):new e.constructor(o);return e.copy(r),r}var Uint8Array$1=root$1.Uint8Array;const Uint8Array$2=Uint8Array$1;function cloneArrayBuffer(e){var t=new e.constructor(e.byteLength);return new Uint8Array$2(t).set(new Uint8Array$2(e)),t}function cloneTypedArray(e,t){var o=t?cloneArrayBuffer(e.buffer):e.buffer;return new e.constructor(o,e.byteOffset,e.length)}function initCloneObject(e){return typeof e.constructor=="function"&&!isPrototype(e)?baseCreate$1(getPrototype$1(e)):{}}function createBaseFor(e){return function(t,o,r){for(var n=-1,i=Object(t),a=r(t),l=a.length;l--;){var s=a[e?l:++n];if(o(i[s],s,i)===!1)break}return t}}var baseFor=createBaseFor();const baseFor$1=baseFor;function assignMergeValue(e,t,o){(o!==void 0&&!eq(e[t],o)||o===void 0&&!(t in e))&&baseAssignValue(e,t,o)}function isArrayLikeObject(e){return isObjectLike(e)&&isArrayLike(e)}function safeGet(e,t){if(!(t==="constructor"&&typeof e[t]=="function")&&t!="__proto__")return e[t]}function toPlainObject(e){return copyObject(e,keysIn(e))}function baseMergeDeep(e,t,o,r,n,i,a){var l=safeGet(e,o),s=safeGet(t,o),d=a.get(s);if(d){assignMergeValue(e,o,d);return}var u=i?i(l,s,o+"",e,t,a):void 0,f=u===void 0;if(f){var m=isArray$1(s),v=!m&&isBuffer$1(s),g=!m&&!v&&isTypedArray$1(s);u=s,m||v||g?isArray$1(l)?u=l:isArrayLikeObject(l)?u=copyArray(l):v?(f=!1,u=cloneBuffer(s,!0)):g?(f=!1,u=cloneTypedArray(s,!0)):u=[]:isPlainObject(s)||isArguments$1(s)?(u=l,isArguments$1(l)?u=toPlainObject(l):(!isObject(l)||isFunction(l))&&(u=initCloneObject(s))):f=!1}f&&(a.set(s,u),n(u,s,r,i,a),a.delete(s)),assignMergeValue(e,o,u)}function baseMerge(e,t,o,r,n){e!==t&&baseFor$1(t,function(i,a){if(n||(n=new Stack),isObject(i))baseMergeDeep(e,t,a,o,baseMerge,r,n);else{var l=r?r(safeGet(e,a),i,a+"",e,t,n):void 0;l===void 0&&(l=i),assignMergeValue(e,a,l)}},keysIn)}var merge=createAssigner(function(e,t,o){baseMerge(e,t,o)});const merge$1=merge,commonVariables$m={fontFamily:'v-sans, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol"',fontFamilyMono:"v-mono, SFMono-Regular, Menlo, Consolas, Courier, monospace",fontWeight:"400",fontWeightStrong:"500",cubicBezierEaseInOut:"cubic-bezier(.4, 0, .2, 1)",cubicBezierEaseOut:"cubic-bezier(0, 0, .2, 1)",cubicBezierEaseIn:"cubic-bezier(.4, 0, 1, 1)",borderRadius:"3px",borderRadiusSmall:"2px",fontSize:"14px",fontSizeMini:"12px",fontSizeTiny:"12px",fontSizeSmall:"14px",fontSizeMedium:"14px",fontSizeLarge:"15px",fontSizeHuge:"16px",lineHeight:"1.6",heightMini:"16px",heightTiny:"22px",heightSmall:"28px",heightMedium:"34px",heightLarge:"40px",heightHuge:"46px"},{fontSize,fontFamily,lineHeight}=commonVariables$m,globalStyle=c("body",`
 margin: 0;
 font-size: ${fontSize};
 font-family: ${fontFamily};
 line-height: ${lineHeight};
 -webkit-text-size-adjust: 100%;
 -webkit-tap-highlight-color: transparent;
`,[c("input",`
 font-family: inherit;
 font-size: inherit;
 `)]),configProviderInjectionKey="n-config-provider",cssrAnchorMetaName="naive-ui-style";function createTheme(e){return e}function useTheme(e,t,o,r,n,i){const a=useSsrAdapter(),l=inject(configProviderInjectionKey,null);if(o){const d=()=>{const u=i==null?void 0:i.value;o.mount({id:u===void 0?t:u+t,head:!0,props:{bPrefix:u?`.${u}-`:void 0},anchorMetaName:cssrAnchorMetaName,ssr:a}),l!=null&&l.preflightStyleDisabled||globalStyle.mount({id:"n-global",head:!0,anchorMetaName:cssrAnchorMetaName,ssr:a})};a?d():onBeforeMount(d)}return computed(()=>{var d;const{theme:{common:u,self:f,peers:m={}}={},themeOverrides:v={},builtinThemeOverrides:g={}}=n,{common:x,peers:C}=v,{common:y=void 0,[e]:{common:I=void 0,self:N=void 0,peers:E={}}={}}=(l==null?void 0:l.mergedThemeRef.value)||{},{common:P=void 0,[e]:F={}}=(l==null?void 0:l.mergedThemeOverridesRef.value)||{},{common:S,peers:$={}}=F,D=merge$1({},u||I||y||r.common,P,S,x),j=merge$1((d=f||N||r.self)===null||d===void 0?void 0:d(D),g,F,v);return{common:D,self:j,peers:merge$1({},r.peers,E,m),peerOverrides:merge$1({},g.peers,$,C)}})}useTheme.props={theme:Object,themeOverrides:Object,builtinThemeOverrides:Object};const defaultClsPrefix="n";function useConfig(e={},t={defaultBordered:!0}){const o=inject(configProviderInjectionKey,null);return{inlineThemeDisabled:o==null?void 0:o.inlineThemeDisabled,mergedRtlRef:o==null?void 0:o.mergedRtlRef,mergedComponentPropsRef:o==null?void 0:o.mergedComponentPropsRef,mergedBreakpointsRef:o==null?void 0:o.mergedBreakpointsRef,mergedBorderedRef:computed(()=>{var r,n;const{bordered:i}=e;return i!==void 0?i:(n=(r=o==null?void 0:o.mergedBorderedRef.value)!==null&&r!==void 0?r:t.defaultBordered)!==null&&n!==void 0?n:!0}),mergedClsPrefixRef:o?o.mergedClsPrefixRef:shallowRef(defaultClsPrefix),namespaceRef:computed(()=>o==null?void 0:o.mergedNamespaceRef.value)}}const enUS={name:"en-US",global:{undo:"Undo",redo:"Redo",confirm:"Confirm",clear:"Clear"},Popconfirm:{positiveText:"Confirm",negativeText:"Cancel"},Cascader:{placeholder:"Please Select",loading:"Loading",loadingRequiredMessage:e=>`Please load all ${e}'s descendants before checking it.`},Time:{dateFormat:"yyyy-MM-dd",dateTimeFormat:"yyyy-MM-dd HH:mm:ss"},DatePicker:{yearFormat:"yyyy",monthFormat:"MMM",dayFormat:"eeeeee",yearTypeFormat:"yyyy",monthTypeFormat:"yyyy-MM",dateFormat:"yyyy-MM-dd",dateTimeFormat:"yyyy-MM-dd HH:mm:ss",quarterFormat:"yyyy-qqq",clear:"Clear",now:"Now",confirm:"Confirm",selectTime:"Select Time",selectDate:"Select Date",datePlaceholder:"Select Date",datetimePlaceholder:"Select Date and Time",monthPlaceholder:"Select Month",yearPlaceholder:"Select Year",quarterPlaceholder:"Select Quarter",startDatePlaceholder:"Start Date",endDatePlaceholder:"End Date",startDatetimePlaceholder:"Start Date and Time",endDatetimePlaceholder:"End Date and Time",startMonthPlaceholder:"Start Month",endMonthPlaceholder:"End Month",monthBeforeYear:!0,firstDayOfWeek:6,today:"Today"},DataTable:{checkTableAll:"Select all in the table",uncheckTableAll:"Unselect all in the table",confirm:"Confirm",clear:"Clear"},LegacyTransfer:{sourceTitle:"Source",targetTitle:"Target"},Transfer:{selectAll:"Select all",unselectAll:"Unselect all",clearAll:"Clear",total:e=>`Total ${e} items`,selected:e=>`${e} items selected`},Empty:{description:"No Data"},Select:{placeholder:"Please Select"},TimePicker:{placeholder:"Select Time",positiveText:"OK",negativeText:"Cancel",now:"Now"},Pagination:{goto:"Goto",selectionSuffix:"page"},DynamicTags:{add:"Add"},Log:{loading:"Loading"},Input:{placeholder:"Please Input"},InputNumber:{placeholder:"Please Input"},DynamicInput:{create:"Create"},ThemeEditor:{title:"Theme Editor",clearAllVars:"Clear All Variables",clearSearch:"Clear Search",filterCompName:"Filter Component Name",filterVarName:"Filter Variable Name",import:"Import",export:"Export",restore:"Reset to Default"},Image:{tipPrevious:"Previous picture (←)",tipNext:"Next picture (→)",tipCounterclockwise:"Counterclockwise",tipClockwise:"Clockwise",tipZoomOut:"Zoom out",tipZoomIn:"Zoom in",tipDownload:"Download",tipClose:"Close (Esc)",tipOriginalSize:"Zoom to original size"}},enUS$1=enUS;function buildFormatLongFn(e){return function(){var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},o=t.width?String(t.width):e.defaultWidth,r=e.formats[o]||e.formats[e.defaultWidth];return r}}function buildLocalizeFn(e){return function(t,o){var r=o!=null&&o.context?String(o.context):"standalone",n;if(r==="formatting"&&e.formattingValues){var i=e.defaultFormattingWidth||e.defaultWidth,a=o!=null&&o.width?String(o.width):i;n=e.formattingValues[a]||e.formattingValues[i]}else{var l=e.defaultWidth,s=o!=null&&o.width?String(o.width):e.defaultWidth;n=e.values[s]||e.values[l]}var d=e.argumentCallback?e.argumentCallback(t):t;return n[d]}}function buildMatchFn(e){return function(t){var o=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},r=o.width,n=r&&e.matchPatterns[r]||e.matchPatterns[e.defaultMatchWidth],i=t.match(n);if(!i)return null;var a=i[0],l=r&&e.parsePatterns[r]||e.parsePatterns[e.defaultParseWidth],s=Array.isArray(l)?findIndex(l,function(f){return f.test(a)}):findKey(l,function(f){return f.test(a)}),d;d=e.valueCallback?e.valueCallback(s):s,d=o.valueCallback?o.valueCallback(d):d;var u=t.slice(a.length);return{value:d,rest:u}}}function findKey(e,t){for(var o in e)if(e.hasOwnProperty(o)&&t(e[o]))return o}function findIndex(e,t){for(var o=0;o<e.length;o++)if(t(e[o]))return o}function buildMatchPatternFn(e){return function(t){var o=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},r=t.match(e.matchPattern);if(!r)return null;var n=r[0],i=t.match(e.parsePattern);if(!i)return null;var a=e.valueCallback?e.valueCallback(i[0]):i[0];a=o.valueCallback?o.valueCallback(a):a;var l=t.slice(n.length);return{value:a,rest:l}}}var formatDistanceLocale={lessThanXSeconds:{one:"less than a second",other:"less than {{count}} seconds"},xSeconds:{one:"1 second",other:"{{count}} seconds"},halfAMinute:"half a minute",lessThanXMinutes:{one:"less than a minute",other:"less than {{count}} minutes"},xMinutes:{one:"1 minute",other:"{{count}} minutes"},aboutXHours:{one:"about 1 hour",other:"about {{count}} hours"},xHours:{one:"1 hour",other:"{{count}} hours"},xDays:{one:"1 day",other:"{{count}} days"},aboutXWeeks:{one:"about 1 week",other:"about {{count}} weeks"},xWeeks:{one:"1 week",other:"{{count}} weeks"},aboutXMonths:{one:"about 1 month",other:"about {{count}} months"},xMonths:{one:"1 month",other:"{{count}} months"},aboutXYears:{one:"about 1 year",other:"about {{count}} years"},xYears:{one:"1 year",other:"{{count}} years"},overXYears:{one:"over 1 year",other:"over {{count}} years"},almostXYears:{one:"almost 1 year",other:"almost {{count}} years"}},formatDistance=function(t,o,r){var n,i=formatDistanceLocale[t];return typeof i=="string"?n=i:o===1?n=i.one:n=i.other.replace("{{count}}",o.toString()),r!=null&&r.addSuffix?r.comparison&&r.comparison>0?"in "+n:n+" ago":n};const formatDistance$1=formatDistance;var dateFormats={full:"EEEE, MMMM do, y",long:"MMMM do, y",medium:"MMM d, y",short:"MM/dd/yyyy"},timeFormats={full:"h:mm:ss a zzzz",long:"h:mm:ss a z",medium:"h:mm:ss a",short:"h:mm a"},dateTimeFormats={full:"{{date}} 'at' {{time}}",long:"{{date}} 'at' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},formatLong={date:buildFormatLongFn({formats:dateFormats,defaultWidth:"full"}),time:buildFormatLongFn({formats:timeFormats,defaultWidth:"full"}),dateTime:buildFormatLongFn({formats:dateTimeFormats,defaultWidth:"full"})};const formatLong$1=formatLong;var formatRelativeLocale={lastWeek:"'last' eeee 'at' p",yesterday:"'yesterday at' p",today:"'today at' p",tomorrow:"'tomorrow at' p",nextWeek:"eeee 'at' p",other:"P"},formatRelative=function(t,o,r,n){return formatRelativeLocale[t]};const formatRelative$1=formatRelative;var eraValues={narrow:["B","A"],abbreviated:["BC","AD"],wide:["Before Christ","Anno Domini"]},quarterValues={narrow:["1","2","3","4"],abbreviated:["Q1","Q2","Q3","Q4"],wide:["1st quarter","2nd quarter","3rd quarter","4th quarter"]},monthValues={narrow:["J","F","M","A","M","J","J","A","S","O","N","D"],abbreviated:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],wide:["January","February","March","April","May","June","July","August","September","October","November","December"]},dayValues={narrow:["S","M","T","W","T","F","S"],short:["Su","Mo","Tu","We","Th","Fr","Sa"],abbreviated:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],wide:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"]},dayPeriodValues={narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"}},formattingDayPeriodValues={narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"}},ordinalNumber=function(t,o){var r=Number(t),n=r%100;if(n>20||n<10)switch(n%10){case 1:return r+"st";case 2:return r+"nd";case 3:return r+"rd"}return r+"th"},localize={ordinalNumber,era:buildLocalizeFn({values:eraValues,defaultWidth:"wide"}),quarter:buildLocalizeFn({values:quarterValues,defaultWidth:"wide",argumentCallback:function(t){return t-1}}),month:buildLocalizeFn({values:monthValues,defaultWidth:"wide"}),day:buildLocalizeFn({values:dayValues,defaultWidth:"wide"}),dayPeriod:buildLocalizeFn({values:dayPeriodValues,defaultWidth:"wide",formattingValues:formattingDayPeriodValues,defaultFormattingWidth:"wide"})};const localize$1=localize;var matchOrdinalNumberPattern=/^(\d+)(th|st|nd|rd)?/i,parseOrdinalNumberPattern=/\d+/i,matchEraPatterns={narrow:/^(b|a)/i,abbreviated:/^(b\.?\s?c\.?|b\.?\s?c\.?\s?e\.?|a\.?\s?d\.?|c\.?\s?e\.?)/i,wide:/^(before christ|before common era|anno domini|common era)/i},parseEraPatterns={any:[/^b/i,/^(a|c)/i]},matchQuarterPatterns={narrow:/^[1234]/i,abbreviated:/^q[1234]/i,wide:/^[1234](th|st|nd|rd)? quarter/i},parseQuarterPatterns={any:[/1/i,/2/i,/3/i,/4/i]},matchMonthPatterns={narrow:/^[jfmasond]/i,abbreviated:/^(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)/i,wide:/^(january|february|march|april|may|june|july|august|september|october|november|december)/i},parseMonthPatterns={narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^f/i,/^mar/i,/^ap/i,/^may/i,/^jun/i,/^jul/i,/^au/i,/^s/i,/^o/i,/^n/i,/^d/i]},matchDayPatterns={narrow:/^[smtwf]/i,short:/^(su|mo|tu|we|th|fr|sa)/i,abbreviated:/^(sun|mon|tue|wed|thu|fri|sat)/i,wide:/^(sunday|monday|tuesday|wednesday|thursday|friday|saturday)/i},parseDayPatterns={narrow:[/^s/i,/^m/i,/^t/i,/^w/i,/^t/i,/^f/i,/^s/i],any:[/^su/i,/^m/i,/^tu/i,/^w/i,/^th/i,/^f/i,/^sa/i]},matchDayPeriodPatterns={narrow:/^(a|p|mi|n|(in the|at) (morning|afternoon|evening|night))/i,any:/^([ap]\.?\s?m\.?|midnight|noon|(in the|at) (morning|afternoon|evening|night))/i},parseDayPeriodPatterns={any:{am:/^a/i,pm:/^p/i,midnight:/^mi/i,noon:/^no/i,morning:/morning/i,afternoon:/afternoon/i,evening:/evening/i,night:/night/i}},match={ordinalNumber:buildMatchPatternFn({matchPattern:matchOrdinalNumberPattern,parsePattern:parseOrdinalNumberPattern,valueCallback:function(t){return parseInt(t,10)}}),era:buildMatchFn({matchPatterns:matchEraPatterns,defaultMatchWidth:"wide",parsePatterns:parseEraPatterns,defaultParseWidth:"any"}),quarter:buildMatchFn({matchPatterns:matchQuarterPatterns,defaultMatchWidth:"wide",parsePatterns:parseQuarterPatterns,defaultParseWidth:"any",valueCallback:function(t){return t+1}}),month:buildMatchFn({matchPatterns:matchMonthPatterns,defaultMatchWidth:"wide",parsePatterns:parseMonthPatterns,defaultParseWidth:"any"}),day:buildMatchFn({matchPatterns:matchDayPatterns,defaultMatchWidth:"wide",parsePatterns:parseDayPatterns,defaultParseWidth:"any"}),dayPeriod:buildMatchFn({matchPatterns:matchDayPeriodPatterns,defaultMatchWidth:"any",parsePatterns:parseDayPeriodPatterns,defaultParseWidth:"any"})};const match$1=match;var locale={code:"en-US",formatDistance:formatDistance$1,formatLong:formatLong$1,formatRelative:formatRelative$1,localize:localize$1,match:match$1,options:{weekStartsOn:0,firstWeekContainsDate:1}};const defaultLocale=locale,dateEnUs={name:"en-US",locale:defaultLocale},dateEnUS=dateEnUs;function useLocale(e){const{mergedLocaleRef:t,mergedDateLocaleRef:o}=inject(configProviderInjectionKey,null)||{},r=computed(()=>{var i,a;return(a=(i=t==null?void 0:t.value)===null||i===void 0?void 0:i[e])!==null&&a!==void 0?a:enUS$1[e]});return{dateLocaleRef:computed(()=>{var i;return(i=o==null?void 0:o.value)!==null&&i!==void 0?i:dateEnUS}),localeRef:r}}function useStyle(e,t,o){if(!t)return;const r=useSsrAdapter(),n=inject(configProviderInjectionKey,null),i=()=>{const a=o.value;t.mount({id:a===void 0?e:a+e,head:!0,anchorMetaName:cssrAnchorMetaName,props:{bPrefix:a?`.${a}-`:void 0},ssr:r}),n!=null&&n.preflightStyleDisabled||globalStyle.mount({id:"n-global",head:!0,anchorMetaName:cssrAnchorMetaName,ssr:r})};r?i():onBeforeMount(i)}function useThemeClass(e,t,o,r){var n;o||throwError("useThemeClass","cssVarsRef is not passed");const i=(n=inject(configProviderInjectionKey,null))===null||n===void 0?void 0:n.mergedThemeHashRef,a=ref(""),l=useSsrAdapter();let s;const d=`__${e}`,u=()=>{let f=d;const m=t?t.value:void 0,v=i==null?void 0:i.value;v&&(f+="-"+v),m&&(f+="-"+m);const{themeOverrides:g,builtinThemeOverrides:x}=r;g&&(f+="-"+murmur2(JSON.stringify(g))),x&&(f+="-"+murmur2(JSON.stringify(x))),a.value=f,s=()=>{const C=o.value;let y="";for(const I in C)y+=`${I}: ${C[I]};`;c(`.${f}`,y).mount({id:f,ssr:l}),s=void 0}};return watchEffect(()=>{u()}),{themeClass:a,onRender:()=>{s==null||s()}}}function useRtl(e,t,o){if(!t)return;const r=useSsrAdapter(),n=computed(()=>{const{value:a}=t;if(!a)return;const l=a[e];if(l)return l}),i=()=>{watchEffect(()=>{const{value:a}=o,l=`${a}${e}Rtl`;if(exists(l,r))return;const{value:s}=n;s&&s.style.mount({id:l,head:!0,anchorMetaName:cssrAnchorMetaName,props:{bPrefix:a?`.${a}-`:void 0},ssr:r})})};return r?i():onBeforeMount(i),n}const AddIcon=defineComponent({name:"Add",render(){return h("svg",{width:"512",height:"512",viewBox:"0 0 512 512",fill:"none",xmlns:"http://www.w3.org/2000/svg"},h("path",{d:"M256 112V400M400 256H112",stroke:"currentColor","stroke-width":"32","stroke-linecap":"round","stroke-linejoin":"round"}))}});function replaceable(e,t){return defineComponent({name:upperFirst$1(e),setup(){var o;const r=(o=inject(configProviderInjectionKey,null))===null||o===void 0?void 0:o.mergedIconsRef;return()=>{var n;const i=(n=r==null?void 0:r.value)===null||n===void 0?void 0:n[e];return i?i():t}}})}const ErrorIcon$1=replaceable("close",h("svg",{viewBox:"0 0 12 12",version:"1.1",xmlns:"http://www.w3.org/2000/svg","aria-hidden":!0},h("g",{stroke:"none","stroke-width":"1",fill:"none","fill-rule":"evenodd"},h("g",{fill:"currentColor","fill-rule":"nonzero"},h("path",{d:"M2.08859116,2.2156945 L2.14644661,2.14644661 C2.32001296,1.97288026 2.58943736,1.95359511 2.7843055,2.08859116 L2.85355339,2.14644661 L6,5.293 L9.14644661,2.14644661 C9.34170876,1.95118446 9.65829124,1.95118446 9.85355339,2.14644661 C10.0488155,2.34170876 10.0488155,2.65829124 9.85355339,2.85355339 L6.707,6 L9.85355339,9.14644661 C10.0271197,9.32001296 10.0464049,9.58943736 9.91140884,9.7843055 L9.85355339,9.85355339 C9.67998704,10.0271197 9.41056264,10.0464049 9.2156945,9.91140884 L9.14644661,9.85355339 L6,6.707 L2.85355339,9.85355339 C2.65829124,10.0488155 2.34170876,10.0488155 2.14644661,9.85355339 C1.95118446,9.65829124 1.95118446,9.34170876 2.14644661,9.14644661 L5.293,6 L2.14644661,2.85355339 C1.97288026,2.67998704 1.95359511,2.41056264 2.08859116,2.2156945 L2.14644661,2.14644661 L2.08859116,2.2156945 Z"}))))),EyeIcon=defineComponent({name:"Eye",render(){return h("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 512 512"},h("path",{d:"M255.66 112c-77.94 0-157.89 45.11-220.83 135.33a16 16 0 0 0-.27 17.77C82.92 340.8 161.8 400 255.66 400c92.84 0 173.34-59.38 221.79-135.25a16.14 16.14 0 0 0 0-17.47C428.89 172.28 347.8 112 255.66 112z",fill:"none",stroke:"currentColor","stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"32"}),h("circle",{cx:"256",cy:"256",r:"80",fill:"none",stroke:"currentColor","stroke-miterlimit":"10","stroke-width":"32"}))}}),EyeOffIcon=defineComponent({name:"EyeOff",render(){return h("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 512 512"},h("path",{d:"M432 448a15.92 15.92 0 0 1-11.31-4.69l-352-352a16 16 0 0 1 22.62-22.62l352 352A16 16 0 0 1 432 448z",fill:"currentColor"}),h("path",{d:"M255.66 384c-41.49 0-81.5-12.28-118.92-36.5c-34.07-22-64.74-53.51-88.7-91v-.08c19.94-28.57 41.78-52.73 65.24-72.21a2 2 0 0 0 .14-2.94L93.5 161.38a2 2 0 0 0-2.71-.12c-24.92 21-48.05 46.76-69.08 76.92a31.92 31.92 0 0 0-.64 35.54c26.41 41.33 60.4 76.14 98.28 100.65C162 402 207.9 416 255.66 416a239.13 239.13 0 0 0 75.8-12.58a2 2 0 0 0 .77-3.31l-21.58-21.58a4 4 0 0 0-3.83-1a204.8 204.8 0 0 1-51.16 6.47z",fill:"currentColor"}),h("path",{d:"M490.84 238.6c-26.46-40.92-60.79-75.68-99.27-100.53C349 110.55 302 96 255.66 96a227.34 227.34 0 0 0-74.89 12.83a2 2 0 0 0-.75 3.31l21.55 21.55a4 4 0 0 0 3.88 1a192.82 192.82 0 0 1 50.21-6.69c40.69 0 80.58 12.43 118.55 37c34.71 22.4 65.74 53.88 89.76 91a.13.13 0 0 1 0 .16a310.72 310.72 0 0 1-64.12 72.73a2 2 0 0 0-.15 2.95l19.9 19.89a2 2 0 0 0 2.7.13a343.49 343.49 0 0 0 68.64-78.48a32.2 32.2 0 0 0-.1-34.78z",fill:"currentColor"}),h("path",{d:"M256 160a95.88 95.88 0 0 0-21.37 2.4a2 2 0 0 0-1 3.38l112.59 112.56a2 2 0 0 0 3.38-1A96 96 0 0 0 256 160z",fill:"currentColor"}),h("path",{d:"M165.78 233.66a2 2 0 0 0-3.38 1a96 96 0 0 0 115 115a2 2 0 0 0 1-3.38z",fill:"currentColor"}))}}),ErrorIcon=replaceable("error",h("svg",{viewBox:"0 0 48 48",version:"1.1",xmlns:"http://www.w3.org/2000/svg"},h("g",{stroke:"none","stroke-width":"1","fill-rule":"evenodd"},h("g",{"fill-rule":"nonzero"},h("path",{d:"M24,4 C35.045695,4 44,12.954305 44,24 C44,35.045695 35.045695,44 24,44 C12.954305,44 4,35.045695 4,24 C4,12.954305 12.954305,4 24,4 Z M17.8838835,16.1161165 L17.7823881,16.0249942 C17.3266086,15.6583353 16.6733914,15.6583353 16.2176119,16.0249942 L16.1161165,16.1161165 L16.0249942,16.2176119 C15.6583353,16.6733914 15.6583353,17.3266086 16.0249942,17.7823881 L16.1161165,17.8838835 L22.233,24 L16.1161165,30.1161165 L16.0249942,30.2176119 C15.6583353,30.6733914 15.6583353,31.3266086 16.0249942,31.7823881 L16.1161165,31.8838835 L16.2176119,31.9750058 C16.6733914,32.3416647 17.3266086,32.3416647 17.7823881,31.9750058 L17.8838835,31.8838835 L24,25.767 L30.1161165,31.8838835 L30.2176119,31.9750058 C30.6733914,32.3416647 31.3266086,32.3416647 31.7823881,31.9750058 L31.8838835,31.8838835 L31.9750058,31.7823881 C32.3416647,31.3266086 32.3416647,30.6733914 31.9750058,30.2176119 L31.8838835,30.1161165 L25.767,24 L31.8838835,17.8838835 L31.9750058,17.7823881 C32.3416647,17.3266086 32.3416647,16.6733914 31.9750058,16.2176119 L31.8838835,16.1161165 L31.7823881,16.0249942 C31.3266086,15.6583353 30.6733914,15.6583353 30.2176119,16.0249942 L30.1161165,16.1161165 L24,22.233 L17.8838835,16.1161165 L17.7823881,16.0249942 L17.8838835,16.1161165 Z"}))))),InfoIcon=replaceable("info",h("svg",{viewBox:"0 0 28 28",version:"1.1",xmlns:"http://www.w3.org/2000/svg"},h("g",{stroke:"none","stroke-width":"1","fill-rule":"evenodd"},h("g",{"fill-rule":"nonzero"},h("path",{d:"M14,2 C20.6274,2 26,7.37258 26,14 C26,20.6274 20.6274,26 14,26 C7.37258,26 2,20.6274 2,14 C2,7.37258 7.37258,2 14,2 Z M14,11 C13.4477,11 13,11.4477 13,12 L13,12 L13,20 C13,20.5523 13.4477,21 14,21 C14.5523,21 15,20.5523 15,20 L15,20 L15,12 C15,11.4477 14.5523,11 14,11 Z M14,6.75 C13.3096,6.75 12.75,7.30964 12.75,8 C12.75,8.69036 13.3096,9.25 14,9.25 C14.6904,9.25 15.25,8.69036 15.25,8 C15.25,7.30964 14.6904,6.75 14,6.75 Z"}))))),RemoveIcon=defineComponent({name:"Remove",render(){return h("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 512 512"},h("line",{x1:"400",y1:"256",x2:"112",y2:"256",style:`
        fill: none;
        stroke: currentColor;
        stroke-linecap: round;
        stroke-linejoin: round;
        stroke-width: 32px;
      `}))}}),SuccessIcon=replaceable("success",h("svg",{viewBox:"0 0 48 48",version:"1.1",xmlns:"http://www.w3.org/2000/svg"},h("g",{stroke:"none","stroke-width":"1","fill-rule":"evenodd"},h("g",{"fill-rule":"nonzero"},h("path",{d:"M24,4 C35.045695,4 44,12.954305 44,24 C44,35.045695 35.045695,44 24,44 C12.954305,44 4,35.045695 4,24 C4,12.954305 12.954305,4 24,4 Z M32.6338835,17.6161165 C32.1782718,17.1605048 31.4584514,17.1301307 30.9676119,17.5249942 L30.8661165,17.6161165 L20.75,27.732233 L17.1338835,24.1161165 C16.6457281,23.6279612 15.8542719,23.6279612 15.3661165,24.1161165 C14.9105048,24.5717282 14.8801307,25.2915486 15.2749942,25.7823881 L15.3661165,25.8838835 L19.8661165,30.3838835 C20.3217282,30.8394952 21.0415486,30.8698693 21.5323881,30.4750058 L21.6338835,30.3838835 L32.6338835,19.3838835 C33.1220388,18.8957281 33.1220388,18.1042719 32.6338835,17.6161165 Z"}))))),WarningIcon=replaceable("warning",h("svg",{viewBox:"0 0 24 24",version:"1.1",xmlns:"http://www.w3.org/2000/svg"},h("g",{stroke:"none","stroke-width":"1","fill-rule":"evenodd"},h("g",{"fill-rule":"nonzero"},h("path",{d:"M12,2 C17.523,2 22,6.478 22,12 C22,17.522 17.523,22 12,22 C6.477,22 2,17.522 2,12 C2,6.478 6.477,2 12,2 Z M12.0018002,15.0037242 C11.450254,15.0037242 11.0031376,15.4508407 11.0031376,16.0023869 C11.0031376,16.553933 11.450254,17.0010495 12.0018002,17.0010495 C12.5533463,17.0010495 13.0004628,16.553933 13.0004628,16.0023869 C13.0004628,15.4508407 12.5533463,15.0037242 12.0018002,15.0037242 Z M11.99964,7 C11.4868042,7.00018474 11.0642719,7.38637706 11.0066858,7.8837365 L11,8.00036004 L11.0018003,13.0012393 L11.00857,13.117858 C11.0665141,13.6151758 11.4893244,14.0010638 12.0021602,14.0008793 C12.514996,14.0006946 12.9375283,13.6145023 12.9951144,13.1171428 L13.0018002,13.0005193 L13,7.99964009 L12.9932303,7.8830214 C12.9352861,7.38570354 12.5124758,6.99981552 11.99964,7 Z"}))))),ChevronDownIcon=defineComponent({name:"ChevronDown",render(){return h("svg",{viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg"},h("path",{d:"M3.14645 5.64645C3.34171 5.45118 3.65829 5.45118 3.85355 5.64645L8 9.79289L12.1464 5.64645C12.3417 5.45118 12.6583 5.45118 12.8536 5.64645C13.0488 5.84171 13.0488 6.15829 12.8536 6.35355L8.35355 10.8536C8.15829 11.0488 7.84171 11.0488 7.64645 10.8536L3.14645 6.35355C2.95118 6.15829 2.95118 5.84171 3.14645 5.64645Z",fill:"currentColor"}))}}),ClearIcon=replaceable("clear",h("svg",{viewBox:"0 0 16 16",version:"1.1",xmlns:"http://www.w3.org/2000/svg"},h("g",{stroke:"none","stroke-width":"1",fill:"none","fill-rule":"evenodd"},h("g",{fill:"currentColor","fill-rule":"nonzero"},h("path",{d:"M8,2 C11.3137085,2 14,4.6862915 14,8 C14,11.3137085 11.3137085,14 8,14 C4.6862915,14 2,11.3137085 2,8 C2,4.6862915 4.6862915,2 8,2 Z M6.5343055,5.83859116 C6.33943736,5.70359511 6.07001296,5.72288026 5.89644661,5.89644661 L5.89644661,5.89644661 L5.83859116,5.9656945 C5.70359511,6.16056264 5.72288026,6.42998704 5.89644661,6.60355339 L5.89644661,6.60355339 L7.293,8 L5.89644661,9.39644661 L5.83859116,9.4656945 C5.70359511,9.66056264 5.72288026,9.92998704 5.89644661,10.1035534 L5.89644661,10.1035534 L5.9656945,10.1614088 C6.16056264,10.2964049 6.42998704,10.2771197 6.60355339,10.1035534 L6.60355339,10.1035534 L8,8.707 L9.39644661,10.1035534 L9.4656945,10.1614088 C9.66056264,10.2964049 9.92998704,10.2771197 10.1035534,10.1035534 L10.1035534,10.1035534 L10.1614088,10.0343055 C10.2964049,9.83943736 10.2771197,9.57001296 10.1035534,9.39644661 L10.1035534,9.39644661 L8.707,8 L10.1035534,6.60355339 L10.1614088,6.5343055 C10.2964049,6.33943736 10.2771197,6.07001296 10.1035534,5.89644661 L10.1035534,5.89644661 L10.0343055,5.83859116 C9.83943736,5.70359511 9.57001296,5.72288026 9.39644661,5.89644661 L9.39644661,5.89644661 L8,7.293 L6.60355339,5.89644661 Z"}))))),NIconSwitchTransition=defineComponent({name:"BaseIconSwitchTransition",setup(e,{slots:t}){const o=isMounted();return()=>h(Transition,{name:"icon-switch-transition",appear:o.value},t)}}),NFadeInExpandTransition=defineComponent({name:"FadeInExpandTransition",props:{appear:Boolean,group:Boolean,mode:String,onLeave:Function,onAfterLeave:Function,onAfterEnter:Function,width:Boolean,reverse:Boolean},setup(e,{slots:t}){function o(l){e.width?l.style.maxWidth=`${l.offsetWidth}px`:l.style.maxHeight=`${l.offsetHeight}px`,l.offsetWidth}function r(l){e.width?l.style.maxWidth="0":l.style.maxHeight="0",l.offsetWidth;const{onLeave:s}=e;s&&s()}function n(l){e.width?l.style.maxWidth="":l.style.maxHeight="";const{onAfterLeave:s}=e;s&&s()}function i(l){if(l.style.transition="none",e.width){const s=l.offsetWidth;l.style.maxWidth="0",l.offsetWidth,l.style.transition="",l.style.maxWidth=`${s}px`}else if(e.reverse)l.style.maxHeight=`${l.offsetHeight}px`,l.offsetHeight,l.style.transition="",l.style.maxHeight="0";else{const s=l.offsetHeight;l.style.maxHeight="0",l.offsetWidth,l.style.transition="",l.style.maxHeight=`${s}px`}l.offsetWidth}function a(l){var s;e.width?l.style.maxWidth="":e.reverse||(l.style.maxHeight=""),(s=e.onAfterEnter)===null||s===void 0||s.call(e)}return()=>{const{group:l,width:s,appear:d,mode:u}=e,f=l?TransitionGroup:Transition,m={name:s?"fade-in-width-expand-transition":"fade-in-height-expand-transition",appear:d,onEnter:i,onAfterEnter:a,onBeforeLeave:o,onLeave:r,onAfterLeave:n};return l||(m.mode=u),h(f,m,t)}}}),style$e=cB("base-icon",`
 height: 1em;
 width: 1em;
 line-height: 1em;
 text-align: center;
 display: inline-block;
 position: relative;
 fill: currentColor;
 transform: translateZ(0);
`,[c("svg",`
 height: 1em;
 width: 1em;
 `)]),NBaseIcon=defineComponent({name:"BaseIcon",props:{role:String,ariaLabel:String,ariaDisabled:{type:Boolean,default:void 0},ariaHidden:{type:Boolean,default:void 0},clsPrefix:{type:String,required:!0},onClick:Function,onMousedown:Function,onMouseup:Function},setup(e){useStyle("-base-icon",style$e,toRef(e,"clsPrefix"))},render(){return h("i",{class:`${this.clsPrefix}-base-icon`,onClick:this.onClick,onMousedown:this.onMousedown,onMouseup:this.onMouseup,role:this.role,"aria-label":this.ariaLabel,"aria-hidden":this.ariaHidden,"aria-disabled":this.ariaDisabled},this.$slots)}}),style$d=cB("base-close",`
 display: flex;
 align-items: center;
 justify-content: center;
 cursor: pointer;
 background-color: transparent;
 color: var(--n-close-icon-color);
 border-radius: var(--n-close-border-radius);
 height: var(--n-close-size);
 width: var(--n-close-size);
 font-size: var(--n-close-icon-size);
 outline: none;
 border: none;
 position: relative;
 padding: 0;
`,[cM("absolute",`
 height: var(--n-close-icon-size);
 width: var(--n-close-icon-size);
 `),c("&::before",`
 content: "";
 position: absolute;
 width: var(--n-close-size);
 height: var(--n-close-size);
 left: 50%;
 top: 50%;
 transform: translateY(-50%) translateX(-50%);
 transition: inherit;
 border-radius: inherit;
 `),cNotM("disabled",[c("&:hover",`
 color: var(--n-close-icon-color-hover);
 `),c("&:hover::before",`
 background-color: var(--n-close-color-hover);
 `),c("&:focus::before",`
 background-color: var(--n-close-color-hover);
 `),c("&:active",`
 color: var(--n-close-icon-color-pressed);
 `),c("&:active::before",`
 background-color: var(--n-close-color-pressed);
 `)]),cM("disabled",`
 cursor: not-allowed;
 color: var(--n-close-icon-color-disabled);
 background-color: transparent;
 `),cM("round",[c("&::before",`
 border-radius: 50%;
 `)])]),NBaseClose=defineComponent({name:"BaseClose",props:{isButtonTag:{type:Boolean,default:!0},clsPrefix:{type:String,required:!0},disabled:{type:Boolean,default:void 0},focusable:{type:Boolean,default:!0},round:Boolean,onClick:Function,absolute:Boolean},setup(e){return useStyle("-base-close",style$d,toRef(e,"clsPrefix")),()=>{const{clsPrefix:t,disabled:o,absolute:r,round:n,isButtonTag:i}=e;return h(i?"button":"div",{type:i?"button":void 0,tabindex:o||!e.focusable?-1:0,"aria-disabled":o,"aria-label":"close",role:i?void 0:"button",disabled:o,class:[`${t}-base-close`,r&&`${t}-base-close--absolute`,o&&`${t}-base-close--disabled`,n&&`${t}-base-close--round`],onMousedown:l=>{e.focusable||l.preventDefault()},onClick:e.onClick},h(NBaseIcon,{clsPrefix:t},{default:()=>h(ErrorIcon$1,null)}))}}}),{cubicBezierEaseInOut:cubicBezierEaseInOut$3}=commonVariables$m;function iconSwitchTransition({originalTransform:e="",left:t=0,top:o=0,transition:r=`all .3s ${cubicBezierEaseInOut$3} !important`}={}){return[c("&.icon-switch-transition-enter-from, &.icon-switch-transition-leave-to",{transform:e+" scale(0.75)",left:t,top:o,opacity:0}),c("&.icon-switch-transition-enter-to, &.icon-switch-transition-leave-from",{transform:`scale(1) ${e}`,left:t,top:o,opacity:1}),c("&.icon-switch-transition-enter-active, &.icon-switch-transition-leave-active",{transformOrigin:"center",position:"absolute",left:t,top:o,transition:r})]}const style$c=c([c("@keyframes loading-container-rotate",`
 to {
 -webkit-transform: rotate(360deg);
 transform: rotate(360deg);
 }
 `),c("@keyframes loading-layer-rotate",`
 12.5% {
 -webkit-transform: rotate(135deg);
 transform: rotate(135deg);
 }
 25% {
 -webkit-transform: rotate(270deg);
 transform: rotate(270deg);
 }
 37.5% {
 -webkit-transform: rotate(405deg);
 transform: rotate(405deg);
 }
 50% {
 -webkit-transform: rotate(540deg);
 transform: rotate(540deg);
 }
 62.5% {
 -webkit-transform: rotate(675deg);
 transform: rotate(675deg);
 }
 75% {
 -webkit-transform: rotate(810deg);
 transform: rotate(810deg);
 }
 87.5% {
 -webkit-transform: rotate(945deg);
 transform: rotate(945deg);
 }
 100% {
 -webkit-transform: rotate(1080deg);
 transform: rotate(1080deg);
 } 
 `),c("@keyframes loading-left-spin",`
 from {
 -webkit-transform: rotate(265deg);
 transform: rotate(265deg);
 }
 50% {
 -webkit-transform: rotate(130deg);
 transform: rotate(130deg);
 }
 to {
 -webkit-transform: rotate(265deg);
 transform: rotate(265deg);
 }
 `),c("@keyframes loading-right-spin",`
 from {
 -webkit-transform: rotate(-265deg);
 transform: rotate(-265deg);
 }
 50% {
 -webkit-transform: rotate(-130deg);
 transform: rotate(-130deg);
 }
 to {
 -webkit-transform: rotate(-265deg);
 transform: rotate(-265deg);
 }
 `),cB("base-loading",`
 position: relative;
 line-height: 0;
 width: 1em;
 height: 1em;
 `,[cE("transition-wrapper",`
 position: absolute;
 width: 100%;
 height: 100%;
 `,[iconSwitchTransition()]),cE("container",`
 display: inline-flex;
 position: relative;
 direction: ltr;
 line-height: 0;
 animation: loading-container-rotate 1568.2352941176ms linear infinite;
 font-size: 0;
 letter-spacing: 0;
 white-space: nowrap;
 opacity: 1;
 width: 100%;
 height: 100%;
 `,[cE("svg",`
 stroke: var(--n-text-color);
 fill: transparent;
 position: absolute;
 height: 100%;
 overflow: hidden;
 `),cE("container-layer",`
 position: absolute;
 width: 100%;
 height: 100%;
 animation: loading-layer-rotate 5332ms cubic-bezier(0.4, 0, 0.2, 1) infinite both;
 `,[cE("container-layer-left",`
 display: inline-flex;
 position: relative;
 width: 50%;
 height: 100%;
 overflow: hidden;
 `,[cE("svg",`
 animation: loading-left-spin 1333ms cubic-bezier(0.4, 0, 0.2, 1) infinite both;
 width: 200%;
 `)]),cE("container-layer-patch",`
 position: absolute;
 top: 0;
 left: 47.5%;
 box-sizing: border-box;
 width: 5%;
 height: 100%;
 overflow: hidden;
 `,[cE("svg",`
 left: -900%;
 width: 2000%;
 transform: rotate(180deg);
 `)]),cE("container-layer-right",`
 display: inline-flex;
 position: relative;
 width: 50%;
 height: 100%;
 overflow: hidden;
 `,[cE("svg",`
 animation: loading-right-spin 1333ms cubic-bezier(0.4, 0, 0.2, 1) infinite both;
 left: -100%;
 width: 200%;
 `)])])]),cE("placeholder",`
 position: absolute;
 left: 50%;
 top: 50%;
 transform: translateX(-50%) translateY(-50%);
 `,[iconSwitchTransition({left:"50%",top:"50%",originalTransform:"translateX(-50%) translateY(-50%)"})])])]),exposedLoadingProps={strokeWidth:{type:Number,default:28},stroke:{type:String,default:void 0}},NBaseLoading=defineComponent({name:"BaseLoading",props:Object.assign({clsPrefix:{type:String,required:!0},show:{type:Boolean,default:!0},scale:{type:Number,default:1},radius:{type:Number,default:100}},exposedLoadingProps),setup(e){useStyle("-base-loading",style$c,toRef(e,"clsPrefix"))},render(){const{clsPrefix:e,radius:t,strokeWidth:o,stroke:r,scale:n}=this,i=t/n;return h("div",{class:`${e}-base-loading`,role:"img","aria-label":"loading"},h(NIconSwitchTransition,null,{default:()=>this.show?h("div",{key:"icon",class:`${e}-base-loading__transition-wrapper`},h("div",{class:`${e}-base-loading__container`},h("div",{class:`${e}-base-loading__container-layer`},h("div",{class:`${e}-base-loading__container-layer-left`},h("svg",{class:`${e}-base-loading__svg`,viewBox:`0 0 ${2*i} ${2*i}`,xmlns:"http://www.w3.org/2000/svg",style:{color:r}},h("circle",{fill:"none",stroke:"currentColor","stroke-width":o,"stroke-linecap":"round",cx:i,cy:i,r:t-o/2,"stroke-dasharray":4.91*t,"stroke-dashoffset":2.46*t}))),h("div",{class:`${e}-base-loading__container-layer-patch`},h("svg",{class:`${e}-base-loading__svg`,viewBox:`0 0 ${2*i} ${2*i}`,xmlns:"http://www.w3.org/2000/svg",style:{color:r}},h("circle",{fill:"none",stroke:"currentColor","stroke-width":o,"stroke-linecap":"round",cx:i,cy:i,r:t-o/2,"stroke-dasharray":4.91*t,"stroke-dashoffset":2.46*t}))),h("div",{class:`${e}-base-loading__container-layer-right`},h("svg",{class:`${e}-base-loading__svg`,viewBox:`0 0 ${2*i} ${2*i}`,xmlns:"http://www.w3.org/2000/svg",style:{color:r}},h("circle",{fill:"none",stroke:"currentColor","stroke-width":o,"stroke-linecap":"round",cx:i,cy:i,r:t-o/2,"stroke-dasharray":4.91*t,"stroke-dashoffset":2.46*t})))))):h("div",{key:"placeholder",class:`${e}-base-loading__placeholder`},this.$slots)}))}}),base$1={neutralBase:"#000",neutralInvertBase:"#fff",neutralTextBase:"#fff",neutralPopover:"rgb(72, 72, 78)",neutralCard:"rgb(24, 24, 28)",neutralModal:"rgb(44, 44, 50)",neutralBody:"rgb(16, 16, 20)",alpha1:"0.9",alpha2:"0.82",alpha3:"0.52",alpha4:"0.38",alpha5:"0.28",alphaClose:"0.52",alphaDisabled:"0.38",alphaDisabledInput:"0.06",alphaPending:"0.09",alphaTablePending:"0.06",alphaTableStriped:"0.05",alphaPressed:"0.05",alphaAvatar:"0.18",alphaRail:"0.2",alphaProgressRail:"0.12",alphaBorder:"0.24",alphaDivider:"0.09",alphaInput:"0.1",alphaAction:"0.06",alphaTab:"0.04",alphaScrollbar:"0.2",alphaScrollbarHover:"0.3",alphaCode:"0.12",alphaTag:"0.2",primaryHover:"#7fe7c4",primaryDefault:"#63e2b7",primaryActive:"#5acea7",primarySuppl:"rgb(42, 148, 125)",infoHover:"#8acbec",infoDefault:"#70c0e8",infoActive:"#66afd3",infoSuppl:"rgb(56, 137, 197)",errorHover:"#e98b8b",errorDefault:"#e88080",errorActive:"#e57272",errorSuppl:"rgb(208, 58, 82)",warningHover:"#f5d599",warningDefault:"#f2c97d",warningActive:"#e6c260",warningSuppl:"rgb(240, 138, 0)",successHover:"#7fe7c4",successDefault:"#63e2b7",successActive:"#5acea7",successSuppl:"rgb(42, 148, 125)"},baseBackgroundRgb$1=rgba(base$1.neutralBase),baseInvertBackgroundRgb$1=rgba(base$1.neutralInvertBase),overlayPrefix$1="rgba("+baseInvertBackgroundRgb$1.slice(0,3).join(", ")+", ";function overlay$1(e){return overlayPrefix$1+String(e)+")"}function neutral$1(e){const t=Array.from(baseInvertBackgroundRgb$1);return t[3]=Number(e),composite(baseBackgroundRgb$1,t)}const derived$1=Object.assign(Object.assign({name:"common"},commonVariables$m),{baseColor:base$1.neutralBase,primaryColor:base$1.primaryDefault,primaryColorHover:base$1.primaryHover,primaryColorPressed:base$1.primaryActive,primaryColorSuppl:base$1.primarySuppl,infoColor:base$1.infoDefault,infoColorHover:base$1.infoHover,infoColorPressed:base$1.infoActive,infoColorSuppl:base$1.infoSuppl,successColor:base$1.successDefault,successColorHover:base$1.successHover,successColorPressed:base$1.successActive,successColorSuppl:base$1.successSuppl,warningColor:base$1.warningDefault,warningColorHover:base$1.warningHover,warningColorPressed:base$1.warningActive,warningColorSuppl:base$1.warningSuppl,errorColor:base$1.errorDefault,errorColorHover:base$1.errorHover,errorColorPressed:base$1.errorActive,errorColorSuppl:base$1.errorSuppl,textColorBase:base$1.neutralTextBase,textColor1:overlay$1(base$1.alpha1),textColor2:overlay$1(base$1.alpha2),textColor3:overlay$1(base$1.alpha3),textColorDisabled:overlay$1(base$1.alpha4),placeholderColor:overlay$1(base$1.alpha4),placeholderColorDisabled:overlay$1(base$1.alpha5),iconColor:overlay$1(base$1.alpha4),iconColorDisabled:overlay$1(base$1.alpha5),iconColorHover:overlay$1(Number(base$1.alpha4)*1.25),iconColorPressed:overlay$1(Number(base$1.alpha4)*.8),opacity1:base$1.alpha1,opacity2:base$1.alpha2,opacity3:base$1.alpha3,opacity4:base$1.alpha4,opacity5:base$1.alpha5,dividerColor:overlay$1(base$1.alphaDivider),borderColor:overlay$1(base$1.alphaBorder),closeIconColorHover:overlay$1(Number(base$1.alphaClose)),closeIconColor:overlay$1(Number(base$1.alphaClose)),closeIconColorPressed:overlay$1(Number(base$1.alphaClose)),closeColorHover:"rgba(255, 255, 255, .12)",closeColorPressed:"rgba(255, 255, 255, .08)",clearColor:overlay$1(base$1.alpha4),clearColorHover:scaleColor(overlay$1(base$1.alpha4),{alpha:1.25}),clearColorPressed:scaleColor(overlay$1(base$1.alpha4),{alpha:.8}),scrollbarColor:overlay$1(base$1.alphaScrollbar),scrollbarColorHover:overlay$1(base$1.alphaScrollbarHover),scrollbarWidth:"5px",scrollbarHeight:"5px",scrollbarBorderRadius:"5px",progressRailColor:overlay$1(base$1.alphaProgressRail),railColor:overlay$1(base$1.alphaRail),popoverColor:base$1.neutralPopover,tableColor:base$1.neutralCard,cardColor:base$1.neutralCard,modalColor:base$1.neutralModal,bodyColor:base$1.neutralBody,tagColor:neutral$1(base$1.alphaTag),avatarColor:overlay$1(base$1.alphaAvatar),invertedColor:base$1.neutralBase,inputColor:overlay$1(base$1.alphaInput),codeColor:overlay$1(base$1.alphaCode),tabColor:overlay$1(base$1.alphaTab),actionColor:overlay$1(base$1.alphaAction),tableHeaderColor:overlay$1(base$1.alphaAction),hoverColor:overlay$1(base$1.alphaPending),tableColorHover:overlay$1(base$1.alphaTablePending),tableColorStriped:overlay$1(base$1.alphaTableStriped),pressedColor:overlay$1(base$1.alphaPressed),opacityDisabled:base$1.alphaDisabled,inputColorDisabled:overlay$1(base$1.alphaDisabledInput),buttonColor2:"rgba(255, 255, 255, .08)",buttonColor2Hover:"rgba(255, 255, 255, .12)",buttonColor2Pressed:"rgba(255, 255, 255, .08)",boxShadow1:"0 1px 2px -2px rgba(0, 0, 0, .24), 0 3px 6px 0 rgba(0, 0, 0, .18), 0 5px 12px 4px rgba(0, 0, 0, .12)",boxShadow2:"0 3px 6px -4px rgba(0, 0, 0, .24), 0 6px 12px 0 rgba(0, 0, 0, .16), 0 9px 18px 8px rgba(0, 0, 0, .10)",boxShadow3:"0 6px 16px -9px rgba(0, 0, 0, .08), 0 9px 28px 0 rgba(0, 0, 0, .05), 0 12px 48px 16px rgba(0, 0, 0, .03)"}),commonDark=derived$1,base={neutralBase:"#FFF",neutralInvertBase:"#000",neutralTextBase:"#000",neutralPopover:"#fff",neutralCard:"#fff",neutralModal:"#fff",neutralBody:"#fff",alpha1:"0.82",alpha2:"0.72",alpha3:"0.38",alpha4:"0.24",alpha5:"0.18",alphaClose:"0.6",alphaDisabled:"0.5",alphaDisabledInput:"0.02",alphaPending:"0.05",alphaTablePending:"0.02",alphaPressed:"0.07",alphaAvatar:"0.2",alphaRail:"0.14",alphaProgressRail:".08",alphaBorder:"0.12",alphaDivider:"0.06",alphaInput:"0",alphaAction:"0.02",alphaTab:"0.04",alphaScrollbar:"0.25",alphaScrollbarHover:"0.4",alphaCode:"0.05",alphaTag:"0.02",primaryHover:"#36ad6a",primaryDefault:"#18a058",primaryActive:"#0c7a43",primarySuppl:"#36ad6a",infoHover:"#4098fc",infoDefault:"#2080f0",infoActive:"#1060c9",infoSuppl:"#4098fc",errorHover:"#de576d",errorDefault:"#d03050",errorActive:"#ab1f3f",errorSuppl:"#de576d",warningHover:"#fcb040",warningDefault:"#f0a020",warningActive:"#c97c10",warningSuppl:"#fcb040",successHover:"#36ad6a",successDefault:"#18a058",successActive:"#0c7a43",successSuppl:"#36ad6a"},baseBackgroundRgb=rgba(base.neutralBase),baseInvertBackgroundRgb=rgba(base.neutralInvertBase),overlayPrefix="rgba("+baseInvertBackgroundRgb.slice(0,3).join(", ")+", ";function overlay(e){return overlayPrefix+String(e)+")"}function neutral(e){const t=Array.from(baseInvertBackgroundRgb);return t[3]=Number(e),composite(baseBackgroundRgb,t)}const derived=Object.assign(Object.assign({name:"common"},commonVariables$m),{baseColor:base.neutralBase,primaryColor:base.primaryDefault,primaryColorHover:base.primaryHover,primaryColorPressed:base.primaryActive,primaryColorSuppl:base.primarySuppl,infoColor:base.infoDefault,infoColorHover:base.infoHover,infoColorPressed:base.infoActive,infoColorSuppl:base.infoSuppl,successColor:base.successDefault,successColorHover:base.successHover,successColorPressed:base.successActive,successColorSuppl:base.successSuppl,warningColor:base.warningDefault,warningColorHover:base.warningHover,warningColorPressed:base.warningActive,warningColorSuppl:base.warningSuppl,errorColor:base.errorDefault,errorColorHover:base.errorHover,errorColorPressed:base.errorActive,errorColorSuppl:base.errorSuppl,textColorBase:base.neutralTextBase,textColor1:"rgb(31, 34, 37)",textColor2:"rgb(51, 54, 57)",textColor3:"rgb(118, 124, 130)",textColorDisabled:neutral(base.alpha4),placeholderColor:neutral(base.alpha4),placeholderColorDisabled:neutral(base.alpha5),iconColor:neutral(base.alpha4),iconColorHover:scaleColor(neutral(base.alpha4),{lightness:.75}),iconColorPressed:scaleColor(neutral(base.alpha4),{lightness:.9}),iconColorDisabled:neutral(base.alpha5),opacity1:base.alpha1,opacity2:base.alpha2,opacity3:base.alpha3,opacity4:base.alpha4,opacity5:base.alpha5,dividerColor:"rgb(239, 239, 245)",borderColor:"rgb(224, 224, 230)",closeIconColor:neutral(Number(base.alphaClose)),closeIconColorHover:neutral(Number(base.alphaClose)),closeIconColorPressed:neutral(Number(base.alphaClose)),closeColorHover:"rgba(0, 0, 0, .09)",closeColorPressed:"rgba(0, 0, 0, .13)",clearColor:neutral(base.alpha4),clearColorHover:scaleColor(neutral(base.alpha4),{lightness:.75}),clearColorPressed:scaleColor(neutral(base.alpha4),{lightness:.9}),scrollbarColor:overlay(base.alphaScrollbar),scrollbarColorHover:overlay(base.alphaScrollbarHover),scrollbarWidth:"5px",scrollbarHeight:"5px",scrollbarBorderRadius:"5px",progressRailColor:neutral(base.alphaProgressRail),railColor:"rgb(219, 219, 223)",popoverColor:base.neutralPopover,tableColor:base.neutralCard,cardColor:base.neutralCard,modalColor:base.neutralModal,bodyColor:base.neutralBody,tagColor:"#eee",avatarColor:neutral(base.alphaAvatar),invertedColor:"rgb(0, 20, 40)",inputColor:neutral(base.alphaInput),codeColor:"rgb(244, 244, 248)",tabColor:"rgb(247, 247, 250)",actionColor:"rgb(250, 250, 252)",tableHeaderColor:"rgb(250, 250, 252)",hoverColor:"rgb(243, 243, 245)",tableColorHover:"rgba(0, 0, 100, 0.03)",tableColorStriped:"rgba(0, 0, 100, 0.02)",pressedColor:"rgb(237, 237, 239)",opacityDisabled:base.alphaDisabled,inputColorDisabled:"rgb(250, 250, 252)",buttonColor2:"rgba(46, 51, 56, .05)",buttonColor2Hover:"rgba(46, 51, 56, .09)",buttonColor2Pressed:"rgba(46, 51, 56, .13)",boxShadow1:"0 1px 2px -2px rgba(0, 0, 0, .08), 0 3px 6px 0 rgba(0, 0, 0, .06), 0 5px 12px 4px rgba(0, 0, 0, .04)",boxShadow2:"0 3px 6px -4px rgba(0, 0, 0, .12), 0 6px 16px 0 rgba(0, 0, 0, .08), 0 9px 28px 8px rgba(0, 0, 0, .05)",boxShadow3:"0 6px 16px -9px rgba(0, 0, 0, .08), 0 9px 28px 0 rgba(0, 0, 0, .05), 0 12px 48px 16px rgba(0, 0, 0, .03)"}),commonLight=derived,commonVars$c={iconSizeSmall:"34px",iconSizeMedium:"40px",iconSizeLarge:"46px",iconSizeHuge:"52px"},self$R=e=>{const{textColorDisabled:t,iconColor:o,textColor2:r,fontSizeSmall:n,fontSizeMedium:i,fontSizeLarge:a,fontSizeHuge:l}=e;return Object.assign(Object.assign({},commonVars$c),{fontSizeSmall:n,fontSizeMedium:i,fontSizeLarge:a,fontSizeHuge:l,textColor:t,iconColor:o,extraTextColor:r})},emptyLight={name:"Empty",common:commonLight,self:self$R},emptyLight$1=emptyLight,emptyDark={name:"Empty",common:commonDark,self:self$R},emptyDark$1=emptyDark,self$Q=e=>{const{scrollbarColor:t,scrollbarColorHover:o}=e;return{color:t,colorHover:o}},scrollbarLight={name:"Scrollbar",common:commonLight,self:self$Q},scrollbarDark={name:"Scrollbar",common:commonDark,self:self$Q},scrollbarDark$1=scrollbarDark,{cubicBezierEaseInOut:cubicBezierEaseInOut$2}=commonVariables$m;function fadeInTransition({name:e="fade-in",enterDuration:t="0.2s",leaveDuration:o="0.2s",enterCubicBezier:r=cubicBezierEaseInOut$2,leaveCubicBezier:n=cubicBezierEaseInOut$2}={}){return[c(`&.${e}-transition-enter-active`,{transition:`all ${t} ${r}!important`}),c(`&.${e}-transition-leave-active`,{transition:`all ${o} ${n}!important`}),c(`&.${e}-transition-enter-from, &.${e}-transition-leave-to`,{opacity:0}),c(`&.${e}-transition-leave-from, &.${e}-transition-enter-to`,{opacity:1})]}const style$b=cB("scrollbar",`
 overflow: hidden;
 position: relative;
 z-index: auto;
 height: 100%;
 width: 100%;
`,[c(">",[cB("scrollbar-container",`
 width: 100%;
 overflow: scroll;
 height: 100%;
 min-height: inherit;
 max-height: inherit;
 scrollbar-width: none;
 `,[c("&::-webkit-scrollbar, &::-webkit-scrollbar-track-piece, &::-webkit-scrollbar-thumb",`
 width: 0;
 height: 0;
 display: none;
 `),c(">",[cB("scrollbar-content",`
 box-sizing: border-box;
 min-width: 100%;
 `)])])]),c(">, +",[cB("scrollbar-rail",`
 position: absolute;
 pointer-events: none;
 user-select: none;
 -webkit-user-select: none;
 `,[cM("horizontal",`
 left: 2px;
 right: 2px;
 bottom: 4px;
 height: var(--n-scrollbar-height);
 `,[c(">",[cE("scrollbar",`
 height: var(--n-scrollbar-height);
 border-radius: var(--n-scrollbar-border-radius);
 right: 0;
 `)])]),cM("vertical",`
 right: 4px;
 top: 2px;
 bottom: 2px;
 width: var(--n-scrollbar-width);
 `,[c(">",[cE("scrollbar",`
 width: var(--n-scrollbar-width);
 border-radius: var(--n-scrollbar-border-radius);
 bottom: 0;
 `)])]),cM("disabled",[c(">",[cE("scrollbar",{pointerEvents:"none"})])]),c(">",[cE("scrollbar",`
 position: absolute;
 cursor: pointer;
 pointer-events: all;
 background-color: var(--n-scrollbar-color);
 transition: background-color .2s var(--n-scrollbar-bezier);
 `,[fadeInTransition(),c("&:hover",{backgroundColor:"var(--n-scrollbar-color-hover)"})])])])])]),scrollbarProps=Object.assign(Object.assign({},useTheme.props),{size:{type:Number,default:5},duration:{type:Number,default:0},scrollable:{type:Boolean,default:!0},xScrollable:Boolean,trigger:{type:String,default:"hover"},useUnifiedContainer:Boolean,triggerDisplayManually:Boolean,container:Function,content:Function,containerClass:String,containerStyle:[String,Object],contentClass:String,contentStyle:[String,Object],horizontalRailStyle:[String,Object],verticalRailStyle:[String,Object],onScroll:Function,onWheel:Function,onResize:Function,internalOnUpdateScrollLeft:Function,internalHoistYRail:Boolean}),Scrollbar=defineComponent({name:"Scrollbar",props:scrollbarProps,inheritAttrs:!1,setup(e){const{mergedClsPrefixRef:t,inlineThemeDisabled:o,mergedRtlRef:r}=useConfig(e),n=useRtl("Scrollbar",r,t),i=ref(null),a=ref(null),l=ref(null),s=ref(null),d=ref(null),u=ref(null),f=ref(null),m=ref(null),v=ref(null),g=ref(null),x=ref(null),C=ref(0),y=ref(0),I=ref(!1),N=ref(!1);let E=!1,P=!1,F,S,$=0,D=0,j=0,K=0;const z=useIsIos(),ee=computed(()=>{const{value:V}=m,{value:re}=u,{value:ue}=g;return V===null||re===null||ue===null?0:Math.min(V,ue*V/re+e.size*1.5)}),ae=computed(()=>`${ee.value}px`),se=computed(()=>{const{value:V}=v,{value:re}=f,{value:ue}=x;return V===null||re===null||ue===null?0:ue*V/re+e.size*1.5}),Z=computed(()=>`${se.value}px`),q=computed(()=>{const{value:V}=m,{value:re}=C,{value:ue}=u,{value:be}=g;if(V===null||ue===null||be===null)return 0;{const we=ue-V;return we?re/we*(be-ee.value):0}}),ne=computed(()=>`${q.value}px`),he=computed(()=>{const{value:V}=v,{value:re}=y,{value:ue}=f,{value:be}=x;if(V===null||ue===null||be===null)return 0;{const we=ue-V;return we?re/we*(be-se.value):0}}),ge=computed(()=>`${he.value}px`),ve=computed(()=>{const{value:V}=m,{value:re}=u;return V!==null&&re!==null&&re>V}),te=computed(()=>{const{value:V}=v,{value:re}=f;return V!==null&&re!==null&&re>V}),Q=computed(()=>{const{trigger:V}=e;return V==="none"||I.value}),G=computed(()=>{const{trigger:V}=e;return V==="none"||N.value}),oe=computed(()=>{const{container:V}=e;return V?V():a.value}),U=computed(()=>{const{content:V}=e;return V?V():l.value}),le=useReactivated(()=>{e.container||Ie({top:C.value,left:y.value})}),$e=()=>{le.isDeactivated||X()},ce=V=>{if(le.isDeactivated)return;const{onResize:re}=e;re&&re(V),X()},Ie=(V,re)=>{if(!e.scrollable)return;if(typeof V=="number"){b(re??0,V,0,!1,"auto");return}const{left:ue,top:be,index:we,elSize:Ee,position:Me,behavior:Ce,el:Oe,debounce:Ae=!0}=V;(ue!==void 0||be!==void 0)&&b(ue??0,be??0,0,!1,Ce),Oe!==void 0?b(0,Oe.offsetTop,Oe.offsetHeight,Ae,Ce):we!==void 0&&Ee!==void 0?b(0,we*Ee,Ee,Ae,Ce):Me==="bottom"?b(0,Number.MAX_SAFE_INTEGER,0,!1,Ce):Me==="top"&&b(0,0,0,!1,Ce)},De=(V,re)=>{if(!e.scrollable)return;const{value:ue}=oe;ue&&(typeof V=="object"?ue.scrollBy(V):ue.scrollBy(V,re||0))};function b(V,re,ue,be,we){const{value:Ee}=oe;if(Ee){if(be){const{scrollTop:Me,offsetHeight:Ce}=Ee;if(re>Me){re+ue<=Me+Ce||Ee.scrollTo({left:V,top:re+ue-Ce,behavior:we});return}}Ee.scrollTo({left:V,top:re,behavior:we})}}function w(){O(),B(),X()}function R(){H()}function H(){L(),k()}function L(){S!==void 0&&window.clearTimeout(S),S=window.setTimeout(()=>{N.value=!1},e.duration)}function k(){F!==void 0&&window.clearTimeout(F),F=window.setTimeout(()=>{I.value=!1},e.duration)}function O(){F!==void 0&&window.clearTimeout(F),I.value=!0}function B(){S!==void 0&&window.clearTimeout(S),N.value=!0}function A(V){const{onScroll:re}=e;re&&re(V),M()}function M(){const{value:V}=oe;V&&(C.value=V.scrollTop,y.value=V.scrollLeft*(n!=null&&n.value?-1:1))}function Y(){const{value:V}=U;V&&(u.value=V.offsetHeight,f.value=V.offsetWidth);const{value:re}=oe;re&&(m.value=re.offsetHeight,v.value=re.offsetWidth);const{value:ue}=d,{value:be}=s;ue&&(x.value=ue.offsetWidth),be&&(g.value=be.offsetHeight)}function J(){const{value:V}=oe;V&&(C.value=V.scrollTop,y.value=V.scrollLeft*(n!=null&&n.value?-1:1),m.value=V.offsetHeight,v.value=V.offsetWidth,u.value=V.scrollHeight,f.value=V.scrollWidth);const{value:re}=d,{value:ue}=s;re&&(x.value=re.offsetWidth),ue&&(g.value=ue.offsetHeight)}function X(){e.scrollable&&(e.useUnifiedContainer?J():(Y(),M()))}function ie(V){var re;return!(!((re=i.value)===null||re===void 0)&&re.contains(getPreciseEventTarget(V)))}function fe(V){V.preventDefault(),V.stopPropagation(),P=!0,on("mousemove",window,pe,!0),on("mouseup",window,me,!0),D=y.value,j=n!=null&&n.value?window.innerWidth-V.clientX:V.clientX}function pe(V){if(!P)return;F!==void 0&&window.clearTimeout(F),S!==void 0&&window.clearTimeout(S);const{value:re}=v,{value:ue}=f,{value:be}=se;if(re===null||ue===null)return;const Ee=(n!=null&&n.value?window.innerWidth-V.clientX-j:V.clientX-j)*(ue-re)/(re-be),Me=ue-re;let Ce=D+Ee;Ce=Math.min(Me,Ce),Ce=Math.max(Ce,0);const{value:Oe}=oe;if(Oe){Oe.scrollLeft=Ce*(n!=null&&n.value?-1:1);const{internalOnUpdateScrollLeft:Ae}=e;Ae&&Ae(Ce)}}function me(V){V.preventDefault(),V.stopPropagation(),off("mousemove",window,pe,!0),off("mouseup",window,me,!0),P=!1,X(),ie(V)&&H()}function ye(V){V.preventDefault(),V.stopPropagation(),E=!0,on("mousemove",window,ke,!0),on("mouseup",window,Re,!0),$=C.value,K=V.clientY}function ke(V){if(!E)return;F!==void 0&&window.clearTimeout(F),S!==void 0&&window.clearTimeout(S);const{value:re}=m,{value:ue}=u,{value:be}=ee;if(re===null||ue===null)return;const Ee=(V.clientY-K)*(ue-re)/(re-be),Me=ue-re;let Ce=$+Ee;Ce=Math.min(Me,Ce),Ce=Math.max(Ce,0);const{value:Oe}=oe;Oe&&(Oe.scrollTop=Ce)}function Re(V){V.preventDefault(),V.stopPropagation(),off("mousemove",window,ke,!0),off("mouseup",window,Re,!0),E=!1,X(),ie(V)&&H()}watchEffect(()=>{const{value:V}=te,{value:re}=ve,{value:ue}=t,{value:be}=d,{value:we}=s;be&&(V?be.classList.remove(`${ue}-scrollbar-rail--disabled`):be.classList.add(`${ue}-scrollbar-rail--disabled`)),we&&(re?we.classList.remove(`${ue}-scrollbar-rail--disabled`):we.classList.add(`${ue}-scrollbar-rail--disabled`))}),onMounted(()=>{e.container||X()}),onBeforeUnmount(()=>{F!==void 0&&window.clearTimeout(F),S!==void 0&&window.clearTimeout(S),off("mousemove",window,ke,!0),off("mouseup",window,Re,!0)});const Ne=useTheme("Scrollbar","-scrollbar",style$b,scrollbarLight,e,t),Be=computed(()=>{const{common:{cubicBezierEaseInOut:V,scrollbarBorderRadius:re,scrollbarHeight:ue,scrollbarWidth:be},self:{color:we,colorHover:Ee}}=Ne.value;return{"--n-scrollbar-bezier":V,"--n-scrollbar-color":we,"--n-scrollbar-color-hover":Ee,"--n-scrollbar-border-radius":re,"--n-scrollbar-width":be,"--n-scrollbar-height":ue}}),_e=o?useThemeClass("scrollbar",void 0,Be,e):void 0;return Object.assign(Object.assign({},{scrollTo:Ie,scrollBy:De,sync:X,syncUnifiedContainer:J,handleMouseEnterWrapper:w,handleMouseLeaveWrapper:R}),{mergedClsPrefix:t,rtlEnabled:n,containerScrollTop:C,wrapperRef:i,containerRef:a,contentRef:l,yRailRef:s,xRailRef:d,needYBar:ve,needXBar:te,yBarSizePx:ae,xBarSizePx:Z,yBarTopPx:ne,xBarLeftPx:ge,isShowXBar:Q,isShowYBar:G,isIos:z,handleScroll:A,handleContentResize:$e,handleContainerResize:ce,handleYScrollMouseDown:ye,handleXScrollMouseDown:fe,cssVars:o?void 0:Be,themeClass:_e==null?void 0:_e.themeClass,onRender:_e==null?void 0:_e.onRender})},render(){var e;const{$slots:t,mergedClsPrefix:o,triggerDisplayManually:r,rtlEnabled:n,internalHoistYRail:i}=this;if(!this.scrollable)return(e=t.default)===null||e===void 0?void 0:e.call(t);const a=this.trigger==="none",l=(u,f)=>h("div",{ref:"yRailRef",class:[`${o}-scrollbar-rail`,`${o}-scrollbar-rail--vertical`,u],"data-scrollbar-rail":!0,style:[f||"",this.verticalRailStyle],"aria-hiddens":!0},h(a?Wrapper:Transition,a?null:{name:"fade-in-transition"},{default:()=>this.needYBar&&this.isShowYBar&&!this.isIos?h("div",{class:`${o}-scrollbar-rail__scrollbar`,style:{height:this.yBarSizePx,top:this.yBarTopPx},onMousedown:this.handleYScrollMouseDown}):null})),s=()=>{var u,f;return(u=this.onRender)===null||u===void 0||u.call(this),h("div",mergeProps(this.$attrs,{role:"none",ref:"wrapperRef",class:[`${o}-scrollbar`,this.themeClass,n&&`${o}-scrollbar--rtl`],style:this.cssVars,onMouseenter:r?void 0:this.handleMouseEnterWrapper,onMouseleave:r?void 0:this.handleMouseLeaveWrapper}),[this.container?(f=t.default)===null||f===void 0?void 0:f.call(t):h("div",{role:"none",ref:"containerRef",class:[`${o}-scrollbar-container`,this.containerClass],style:this.containerStyle,onScroll:this.handleScroll,onWheel:this.onWheel},h(VResizeObserver,{onResize:this.handleContentResize},{default:()=>h("div",{ref:"contentRef",role:"none",style:[{width:this.xScrollable?"fit-content":null},this.contentStyle],class:[`${o}-scrollbar-content`,this.contentClass]},t)})),i?null:l(void 0,void 0),this.xScrollable&&h("div",{ref:"xRailRef",class:[`${o}-scrollbar-rail`,`${o}-scrollbar-rail--horizontal`],style:this.horizontalRailStyle,"data-scrollbar-rail":!0,"aria-hidden":!0},h(a?Wrapper:Transition,a?null:{name:"fade-in-transition"},{default:()=>this.needXBar&&this.isShowXBar&&!this.isIos?h("div",{class:`${o}-scrollbar-rail__scrollbar`,style:{width:this.xBarSizePx,right:n?this.xBarLeftPx:void 0,left:n?void 0:this.xBarLeftPx},onMousedown:this.handleXScrollMouseDown}):null}))])},d=this.container?s():h(VResizeObserver,{onResize:this.handleContainerResize},{default:s});return i?h(Fragment,null,d,l(this.themeClass,this.cssVars)):d}}),NScrollbar=Scrollbar,commonVariables$l={height:"calc(var(--n-option-height) * 7.6)",paddingSmall:"4px 0",paddingMedium:"4px 0",paddingLarge:"4px 0",paddingHuge:"4px 0",optionPaddingSmall:"0 12px",optionPaddingMedium:"0 12px",optionPaddingLarge:"0 12px",optionPaddingHuge:"0 12px",loadingSize:"18px"},self$P=e=>{const{borderRadius:t,popoverColor:o,textColor3:r,dividerColor:n,textColor2:i,primaryColorPressed:a,textColorDisabled:l,primaryColor:s,opacityDisabled:d,hoverColor:u,fontSizeSmall:f,fontSizeMedium:m,fontSizeLarge:v,fontSizeHuge:g,heightSmall:x,heightMedium:C,heightLarge:y,heightHuge:I}=e;return Object.assign(Object.assign({},commonVariables$l),{optionFontSizeSmall:f,optionFontSizeMedium:m,optionFontSizeLarge:v,optionFontSizeHuge:g,optionHeightSmall:x,optionHeightMedium:C,optionHeightLarge:y,optionHeightHuge:I,borderRadius:t,color:o,groupHeaderTextColor:r,actionDividerColor:n,optionTextColor:i,optionTextColorPressed:a,optionTextColorDisabled:l,optionTextColorActive:s,optionOpacityDisabled:d,optionCheckColor:s,optionColorPending:u,optionColorActive:"rgba(0, 0, 0, 0)",optionColorActivePending:u,actionTextColor:i,loadingColor:s})},internalSelectMenuDark={name:"InternalSelectMenu",common:commonDark,peers:{Scrollbar:scrollbarDark$1,Empty:emptyDark$1},self:self$P},internalSelectMenuDark$1=internalSelectMenuDark,{cubicBezierEaseIn:cubicBezierEaseIn$1,cubicBezierEaseOut:cubicBezierEaseOut$1}=commonVariables$m;function fadeInScaleUpTransition({transformOrigin:e="inherit",duration:t=".2s",enterScale:o=".9",originalTransform:r="",originalTransition:n=""}={}){return[c("&.fade-in-scale-up-transition-leave-active",{transformOrigin:e,transition:`opacity ${t} ${cubicBezierEaseIn$1}, transform ${t} ${cubicBezierEaseIn$1} ${n&&","+n}`}),c("&.fade-in-scale-up-transition-enter-active",{transformOrigin:e,transition:`opacity ${t} ${cubicBezierEaseOut$1}, transform ${t} ${cubicBezierEaseOut$1} ${n&&","+n}`}),c("&.fade-in-scale-up-transition-enter-from, &.fade-in-scale-up-transition-leave-to",{opacity:0,transform:`${r} scale(${o})`}),c("&.fade-in-scale-up-transition-leave-from, &.fade-in-scale-up-transition-enter-to",{opacity:1,transform:`${r} scale(1)`})]}const style$a=cB("base-wave",`
 position: absolute;
 left: 0;
 right: 0;
 top: 0;
 bottom: 0;
 border-radius: inherit;
`),NBaseWave=defineComponent({name:"BaseWave",props:{clsPrefix:{type:String,required:!0}},setup(e){useStyle("-base-wave",style$a,toRef(e,"clsPrefix"));const t=ref(null),o=ref(!1);let r=null;return onBeforeUnmount(()=>{r!==null&&window.clearTimeout(r)}),{active:o,selfRef:t,play(){r!==null&&(window.clearTimeout(r),o.value=!1,r=null),nextTick(()=>{var n;(n=t.value)===null||n===void 0||n.offsetHeight,o.value=!0,r=window.setTimeout(()=>{o.value=!1,r=null},1e3)})}}},render(){const{clsPrefix:e}=this;return h("div",{ref:"selfRef","aria-hidden":!0,class:[`${e}-base-wave`,this.active&&`${e}-base-wave--active`]})}}),commonVariables$k={space:"6px",spaceArrow:"10px",arrowOffset:"10px",arrowOffsetVertical:"10px",arrowHeight:"6px",padding:"8px 14px"},self$O=e=>{const{boxShadow2:t,popoverColor:o,textColor2:r,borderRadius:n,fontSize:i,dividerColor:a}=e;return Object.assign(Object.assign({},commonVariables$k),{fontSize:i,borderRadius:n,color:o,dividerColor:a,textColor:r,boxShadow:t})},popoverDark={name:"Popover",common:commonDark,self:self$O},popoverDark$1=popoverDark,commonVariables$j={closeIconSizeTiny:"12px",closeIconSizeSmall:"12px",closeIconSizeMedium:"14px",closeIconSizeLarge:"14px",closeSizeTiny:"16px",closeSizeSmall:"16px",closeSizeMedium:"18px",closeSizeLarge:"18px",padding:"0 7px",closeMargin:"0 0 0 4px",closeMarginRtl:"0 4px 0 0"},tagDark={name:"Tag",common:commonDark,self(e){const{textColor2:t,primaryColorHover:o,primaryColorPressed:r,primaryColor:n,infoColor:i,successColor:a,warningColor:l,errorColor:s,baseColor:d,borderColor:u,tagColor:f,opacityDisabled:m,closeIconColor:v,closeIconColorHover:g,closeIconColorPressed:x,closeColorHover:C,closeColorPressed:y,borderRadiusSmall:I,fontSizeMini:N,fontSizeTiny:E,fontSizeSmall:P,fontSizeMedium:F,heightMini:S,heightTiny:$,heightSmall:D,heightMedium:j,buttonColor2Hover:K,buttonColor2Pressed:z,fontWeightStrong:ee}=e;return Object.assign(Object.assign({},commonVariables$j),{closeBorderRadius:I,heightTiny:S,heightSmall:$,heightMedium:D,heightLarge:j,borderRadius:I,opacityDisabled:m,fontSizeTiny:N,fontSizeSmall:E,fontSizeMedium:P,fontSizeLarge:F,fontWeightStrong:ee,textColorCheckable:t,textColorHoverCheckable:t,textColorPressedCheckable:t,textColorChecked:d,colorCheckable:"#0000",colorHoverCheckable:K,colorPressedCheckable:z,colorChecked:n,colorCheckedHover:o,colorCheckedPressed:r,border:`1px solid ${u}`,textColor:t,color:f,colorBordered:"#0000",closeIconColor:v,closeIconColorHover:g,closeIconColorPressed:x,closeColorHover:C,closeColorPressed:y,borderPrimary:`1px solid ${changeColor(n,{alpha:.3})}`,textColorPrimary:n,colorPrimary:changeColor(n,{alpha:.16}),colorBorderedPrimary:"#0000",closeIconColorPrimary:scaleColor(n,{lightness:.7}),closeIconColorHoverPrimary:scaleColor(n,{lightness:.7}),closeIconColorPressedPrimary:scaleColor(n,{lightness:.7}),closeColorHoverPrimary:changeColor(n,{alpha:.16}),closeColorPressedPrimary:changeColor(n,{alpha:.12}),borderInfo:`1px solid ${changeColor(i,{alpha:.3})}`,textColorInfo:i,colorInfo:changeColor(i,{alpha:.16}),colorBorderedInfo:"#0000",closeIconColorInfo:scaleColor(i,{alpha:.7}),closeIconColorHoverInfo:scaleColor(i,{alpha:.7}),closeIconColorPressedInfo:scaleColor(i,{alpha:.7}),closeColorHoverInfo:changeColor(i,{alpha:.16}),closeColorPressedInfo:changeColor(i,{alpha:.12}),borderSuccess:`1px solid ${changeColor(a,{alpha:.3})}`,textColorSuccess:a,colorSuccess:changeColor(a,{alpha:.16}),colorBorderedSuccess:"#0000",closeIconColorSuccess:scaleColor(a,{alpha:.7}),closeIconColorHoverSuccess:scaleColor(a,{alpha:.7}),closeIconColorPressedSuccess:scaleColor(a,{alpha:.7}),closeColorHoverSuccess:changeColor(a,{alpha:.16}),closeColorPressedSuccess:changeColor(a,{alpha:.12}),borderWarning:`1px solid ${changeColor(l,{alpha:.3})}`,textColorWarning:l,colorWarning:changeColor(l,{alpha:.16}),colorBorderedWarning:"#0000",closeIconColorWarning:scaleColor(l,{alpha:.7}),closeIconColorHoverWarning:scaleColor(l,{alpha:.7}),closeIconColorPressedWarning:scaleColor(l,{alpha:.7}),closeColorHoverWarning:changeColor(l,{alpha:.16}),closeColorPressedWarning:changeColor(l,{alpha:.11}),borderError:`1px solid ${changeColor(s,{alpha:.3})}`,textColorError:s,colorError:changeColor(s,{alpha:.16}),colorBorderedError:"#0000",closeIconColorError:scaleColor(s,{alpha:.7}),closeIconColorHoverError:scaleColor(s,{alpha:.7}),closeIconColorPressedError:scaleColor(s,{alpha:.7}),closeColorHoverError:changeColor(s,{alpha:.16}),closeColorPressedError:changeColor(s,{alpha:.12})})}},tagDark$1=tagDark,style$9=cB("base-clear",`
 flex-shrink: 0;
 height: 1em;
 width: 1em;
 position: relative;
`,[c(">",[cE("clear",`
 font-size: var(--n-clear-size);
 height: 1em;
 width: 1em;
 cursor: pointer;
 color: var(--n-clear-color);
 transition: color .3s var(--n-bezier);
 display: flex;
 `,[c("&:hover",`
 color: var(--n-clear-color-hover)!important;
 `),c("&:active",`
 color: var(--n-clear-color-pressed)!important;
 `)]),cE("placeholder",`
 display: flex;
 `),cE("clear, placeholder",`
 position: absolute;
 left: 50%;
 top: 50%;
 transform: translateX(-50%) translateY(-50%);
 `,[iconSwitchTransition({originalTransform:"translateX(-50%) translateY(-50%)",left:"50%",top:"50%"})])])]),NBaseClear=defineComponent({name:"BaseClear",props:{clsPrefix:{type:String,required:!0},show:Boolean,onClear:Function},setup(e){return useStyle("-base-clear",style$9,toRef(e,"clsPrefix")),{handleMouseDown(t){var o;t.preventDefault(),(o=e.onClear)===null||o===void 0||o.call(e,t)}}},render(){const{clsPrefix:e}=this;return h("div",{class:`${e}-base-clear`},h(NIconSwitchTransition,null,{default:()=>{var t,o;return this.show?h("div",{key:"dismiss",class:`${e}-base-clear__clear`,onClick:this.onClear,onMousedown:this.handleMouseDown,"data-clear":!0},resolveSlot(this.$slots.icon,()=>[h(NBaseIcon,{clsPrefix:e},{default:()=>h(ClearIcon,null)})])):h("div",{key:"icon",class:`${e}-base-clear__placeholder`},(o=(t=this.$slots).placeholder)===null||o===void 0?void 0:o.call(t))}}))}}),NBaseSuffix=defineComponent({name:"InternalSelectionSuffix",props:{clsPrefix:{type:String,required:!0},showArrow:{type:Boolean,default:void 0},showClear:{type:Boolean,default:void 0},loading:{type:Boolean,default:!1},onClear:Function},setup(e,{slots:t}){return()=>{const{clsPrefix:o}=e;return h(NBaseLoading,{clsPrefix:o,class:`${o}-base-suffix`,strokeWidth:24,scale:.85,show:e.loading},{default:()=>e.showArrow?h(NBaseClear,{clsPrefix:o,show:e.showClear,onClear:e.onClear},{placeholder:()=>h(NBaseIcon,{clsPrefix:o,class:`${o}-base-suffix__arrow`},{default:()=>resolveSlot(t.default,()=>[h(ChevronDownIcon,null)])})}):null})}}}),commonVars$b={paddingSingle:"0 26px 0 12px",paddingMultiple:"3px 26px 0 12px",clearSize:"16px",arrowSize:"16px"},internalSelectionDark={name:"InternalSelection",common:commonDark,peers:{Popover:popoverDark$1},self(e){const{borderRadius:t,textColor2:o,textColorDisabled:r,inputColor:n,inputColorDisabled:i,primaryColor:a,primaryColorHover:l,warningColor:s,warningColorHover:d,errorColor:u,errorColorHover:f,iconColor:m,iconColorDisabled:v,clearColor:g,clearColorHover:x,clearColorPressed:C,placeholderColor:y,placeholderColorDisabled:I,fontSizeTiny:N,fontSizeSmall:E,fontSizeMedium:P,fontSizeLarge:F,heightTiny:S,heightSmall:$,heightMedium:D,heightLarge:j}=e;return Object.assign(Object.assign({},commonVars$b),{fontSizeTiny:N,fontSizeSmall:E,fontSizeMedium:P,fontSizeLarge:F,heightTiny:S,heightSmall:$,heightMedium:D,heightLarge:j,borderRadius:t,textColor:o,textColorDisabled:r,placeholderColor:y,placeholderColorDisabled:I,color:n,colorDisabled:i,colorActive:changeColor(a,{alpha:.1}),border:"1px solid #0000",borderHover:`1px solid ${l}`,borderActive:`1px solid ${a}`,borderFocus:`1px solid ${l}`,boxShadowHover:"none",boxShadowActive:`0 0 8px 0 ${changeColor(a,{alpha:.4})}`,boxShadowFocus:`0 0 8px 0 ${changeColor(a,{alpha:.4})}`,caretColor:a,arrowColor:m,arrowColorDisabled:v,loadingColor:a,borderWarning:`1px solid ${s}`,borderHoverWarning:`1px solid ${d}`,borderActiveWarning:`1px solid ${s}`,borderFocusWarning:`1px solid ${d}`,boxShadowHoverWarning:"none",boxShadowActiveWarning:`0 0 8px 0 ${changeColor(s,{alpha:.4})}`,boxShadowFocusWarning:`0 0 8px 0 ${changeColor(s,{alpha:.4})}`,colorActiveWarning:changeColor(s,{alpha:.1}),caretColorWarning:s,borderError:`1px solid ${u}`,borderHoverError:`1px solid ${f}`,borderActiveError:`1px solid ${u}`,borderFocusError:`1px solid ${f}`,boxShadowHoverError:"none",boxShadowActiveError:`0 0 8px 0 ${changeColor(u,{alpha:.4})}`,boxShadowFocusError:`0 0 8px 0 ${changeColor(u,{alpha:.4})}`,colorActiveError:changeColor(u,{alpha:.1}),caretColorError:u,clearColor:g,clearColorHover:x,clearColorPressed:C})}},internalSelectionDark$1=internalSelectionDark,{cubicBezierEaseInOut:cubicBezierEaseInOut$1}=commonVariables$m;function fadeInWidthExpandTransition({duration:e=".2s",delay:t=".1s"}={}){return[c("&.fade-in-width-expand-transition-leave-from, &.fade-in-width-expand-transition-enter-to",{opacity:1}),c("&.fade-in-width-expand-transition-leave-to, &.fade-in-width-expand-transition-enter-from",`
 opacity: 0!important;
 margin-left: 0!important;
 margin-right: 0!important;
 `),c("&.fade-in-width-expand-transition-leave-active",`
 overflow: hidden;
 transition:
 opacity ${e} ${cubicBezierEaseInOut$1},
 max-width ${e} ${cubicBezierEaseInOut$1} ${t},
 margin-left ${e} ${cubicBezierEaseInOut$1} ${t},
 margin-right ${e} ${cubicBezierEaseInOut$1} ${t};
 `),c("&.fade-in-width-expand-transition-enter-active",`
 overflow: hidden;
 transition:
 opacity ${e} ${cubicBezierEaseInOut$1} ${t},
 max-width ${e} ${cubicBezierEaseInOut$1},
 margin-left ${e} ${cubicBezierEaseInOut$1},
 margin-right ${e} ${cubicBezierEaseInOut$1};
 `)]}const commonVars$a={iconMargin:"11px 8px 0 12px",iconMarginRtl:"11px 12px 0 8px",iconSize:"24px",closeIconSize:"16px",closeSize:"20px",closeMargin:"13px 14px 0 0",closeMarginRtl:"13px 0 0 14px",padding:"13px"},alertDark={name:"Alert",common:commonDark,self(e){const{lineHeight:t,borderRadius:o,fontWeightStrong:r,dividerColor:n,inputColor:i,textColor1:a,textColor2:l,closeColorHover:s,closeColorPressed:d,closeIconColor:u,closeIconColorHover:f,closeIconColorPressed:m,infoColorSuppl:v,successColorSuppl:g,warningColorSuppl:x,errorColorSuppl:C,fontSize:y}=e;return Object.assign(Object.assign({},commonVars$a),{fontSize:y,lineHeight:t,titleFontWeight:r,borderRadius:o,border:`1px solid ${n}`,color:i,titleTextColor:a,iconColor:l,contentTextColor:l,closeBorderRadius:o,closeColorHover:s,closeColorPressed:d,closeIconColor:u,closeIconColorHover:f,closeIconColorPressed:m,borderInfo:`1px solid ${changeColor(v,{alpha:.35})}`,colorInfo:changeColor(v,{alpha:.25}),titleTextColorInfo:a,iconColorInfo:v,contentTextColorInfo:l,closeColorHoverInfo:s,closeColorPressedInfo:d,closeIconColorInfo:u,closeIconColorHoverInfo:f,closeIconColorPressedInfo:m,borderSuccess:`1px solid ${changeColor(g,{alpha:.35})}`,colorSuccess:changeColor(g,{alpha:.25}),titleTextColorSuccess:a,iconColorSuccess:g,contentTextColorSuccess:l,closeColorHoverSuccess:s,closeColorPressedSuccess:d,closeIconColorSuccess:u,closeIconColorHoverSuccess:f,closeIconColorPressedSuccess:m,borderWarning:`1px solid ${changeColor(x,{alpha:.35})}`,colorWarning:changeColor(x,{alpha:.25}),titleTextColorWarning:a,iconColorWarning:x,contentTextColorWarning:l,closeColorHoverWarning:s,closeColorPressedWarning:d,closeIconColorWarning:u,closeIconColorHoverWarning:f,closeIconColorPressedWarning:m,borderError:`1px solid ${changeColor(C,{alpha:.35})}`,colorError:changeColor(C,{alpha:.25}),titleTextColorError:a,iconColorError:C,contentTextColorError:l,closeColorHoverError:s,closeColorPressedError:d,closeIconColorError:u,closeIconColorHoverError:f,closeIconColorPressedError:m})}},alertDark$1=alertDark,{cubicBezierEaseInOut,cubicBezierEaseOut,cubicBezierEaseIn}=commonVariables$m;function fadeInHeightExpandTransition({overflow:e="hidden",duration:t=".3s",originalTransition:o="",leavingDelay:r="0s",foldPadding:n=!1,enterToProps:i=void 0,leaveToProps:a=void 0,reverse:l=!1}={}){const s=l?"leave":"enter",d=l?"enter":"leave";return[c(`&.fade-in-height-expand-transition-${d}-from,
 &.fade-in-height-expand-transition-${s}-to`,Object.assign(Object.assign({},i),{opacity:1})),c(`&.fade-in-height-expand-transition-${d}-to,
 &.fade-in-height-expand-transition-${s}-from`,Object.assign(Object.assign({},a),{opacity:0,marginTop:"0 !important",marginBottom:"0 !important",paddingTop:n?"0 !important":void 0,paddingBottom:n?"0 !important":void 0})),c(`&.fade-in-height-expand-transition-${d}-active`,`
 overflow: ${e};
 transition:
 max-height ${t} ${cubicBezierEaseInOut} ${r},
 opacity ${t} ${cubicBezierEaseOut} ${r},
 margin-top ${t} ${cubicBezierEaseInOut} ${r},
 margin-bottom ${t} ${cubicBezierEaseInOut} ${r},
 padding-top ${t} ${cubicBezierEaseInOut} ${r},
 padding-bottom ${t} ${cubicBezierEaseInOut} ${r}
 ${o?","+o:""}
 `),c(`&.fade-in-height-expand-transition-${s}-active`,`
 overflow: ${e};
 transition:
 max-height ${t} ${cubicBezierEaseInOut},
 opacity ${t} ${cubicBezierEaseIn},
 margin-top ${t} ${cubicBezierEaseInOut},
 margin-bottom ${t} ${cubicBezierEaseInOut},
 padding-top ${t} ${cubicBezierEaseInOut},
 padding-bottom ${t} ${cubicBezierEaseInOut}
 ${o?","+o:""}
 `)]}const commonVars$9={linkFontSize:"13px",linkPadding:"0 0 0 16px",railWidth:"4px"},self$N=e=>{const{borderRadius:t,railColor:o,primaryColor:r,primaryColorHover:n,primaryColorPressed:i,textColor2:a}=e;return Object.assign(Object.assign({},commonVars$9),{borderRadius:t,railColor:o,railColorActive:r,linkColor:changeColor(r,{alpha:.15}),linkTextColor:a,linkTextColorHover:n,linkTextColorPressed:i,linkTextColorActive:r})},anchorDark={name:"Anchor",common:commonDark,self:self$N},anchorDark$1=anchorDark,isChrome=isBrowser$2&&"chrome"in window;isBrowser$2&&navigator.userAgent.includes("Firefox");const isSafari=isBrowser$2&&navigator.userAgent.includes("Safari")&&!isChrome,commonVariables$i={paddingTiny:"0 8px",paddingSmall:"0 10px",paddingMedium:"0 12px",paddingLarge:"0 14px",clearSize:"16px"},inputDark={name:"Input",common:commonDark,self(e){const{textColor2:t,textColor3:o,textColorDisabled:r,primaryColor:n,primaryColorHover:i,inputColor:a,inputColorDisabled:l,warningColor:s,warningColorHover:d,errorColor:u,errorColorHover:f,borderRadius:m,lineHeight:v,fontSizeTiny:g,fontSizeSmall:x,fontSizeMedium:C,fontSizeLarge:y,heightTiny:I,heightSmall:N,heightMedium:E,heightLarge:P,clearColor:F,clearColorHover:S,clearColorPressed:$,placeholderColor:D,placeholderColorDisabled:j,iconColor:K,iconColorDisabled:z,iconColorHover:ee,iconColorPressed:ae}=e;return Object.assign(Object.assign({},commonVariables$i),{countTextColorDisabled:r,countTextColor:o,heightTiny:I,heightSmall:N,heightMedium:E,heightLarge:P,fontSizeTiny:g,fontSizeSmall:x,fontSizeMedium:C,fontSizeLarge:y,lineHeight:v,lineHeightTextarea:v,borderRadius:m,iconSize:"16px",groupLabelColor:a,textColor:t,textColorDisabled:r,textDecorationColor:t,groupLabelTextColor:t,caretColor:n,placeholderColor:D,placeholderColorDisabled:j,color:a,colorDisabled:l,colorFocus:changeColor(n,{alpha:.1}),groupLabelBorder:"1px solid #0000",border:"1px solid #0000",borderHover:`1px solid ${i}`,borderDisabled:"1px solid #0000",borderFocus:`1px solid ${i}`,boxShadowFocus:`0 0 8px 0 ${changeColor(n,{alpha:.3})}`,loadingColor:n,loadingColorWarning:s,borderWarning:`1px solid ${s}`,borderHoverWarning:`1px solid ${d}`,colorFocusWarning:changeColor(s,{alpha:.1}),borderFocusWarning:`1px solid ${d}`,boxShadowFocusWarning:`0 0 8px 0 ${changeColor(s,{alpha:.3})}`,caretColorWarning:s,loadingColorError:u,borderError:`1px solid ${u}`,borderHoverError:`1px solid ${f}`,colorFocusError:changeColor(u,{alpha:.1}),borderFocusError:`1px solid ${f}`,boxShadowFocusError:`0 0 8px 0 ${changeColor(u,{alpha:.3})}`,caretColorError:u,clearColor:F,clearColorHover:S,clearColorPressed:$,iconColor:K,iconColorDisabled:z,iconColorHover:ee,iconColorPressed:ae,suffixTextColor:t})}},inputDark$1=inputDark,self$M=e=>{const{textColor2:t,textColor3:o,textColorDisabled:r,primaryColor:n,primaryColorHover:i,inputColor:a,inputColorDisabled:l,borderColor:s,warningColor:d,warningColorHover:u,errorColor:f,errorColorHover:m,borderRadius:v,lineHeight:g,fontSizeTiny:x,fontSizeSmall:C,fontSizeMedium:y,fontSizeLarge:I,heightTiny:N,heightSmall:E,heightMedium:P,heightLarge:F,actionColor:S,clearColor:$,clearColorHover:D,clearColorPressed:j,placeholderColor:K,placeholderColorDisabled:z,iconColor:ee,iconColorDisabled:ae,iconColorHover:se,iconColorPressed:Z}=e;return Object.assign(Object.assign({},commonVariables$i),{countTextColorDisabled:r,countTextColor:o,heightTiny:N,heightSmall:E,heightMedium:P,heightLarge:F,fontSizeTiny:x,fontSizeSmall:C,fontSizeMedium:y,fontSizeLarge:I,lineHeight:g,lineHeightTextarea:g,borderRadius:v,iconSize:"16px",groupLabelColor:S,groupLabelTextColor:t,textColor:t,textColorDisabled:r,textDecorationColor:t,caretColor:n,placeholderColor:K,placeholderColorDisabled:z,color:a,colorDisabled:l,colorFocus:a,groupLabelBorder:`1px solid ${s}`,border:`1px solid ${s}`,borderHover:`1px solid ${i}`,borderDisabled:`1px solid ${s}`,borderFocus:`1px solid ${i}`,boxShadowFocus:`0 0 0 2px ${changeColor(n,{alpha:.2})}`,loadingColor:n,loadingColorWarning:d,borderWarning:`1px solid ${d}`,borderHoverWarning:`1px solid ${u}`,colorFocusWarning:a,borderFocusWarning:`1px solid ${u}`,boxShadowFocusWarning:`0 0 0 2px ${changeColor(d,{alpha:.2})}`,caretColorWarning:d,loadingColorError:f,borderError:`1px solid ${f}`,borderHoverError:`1px solid ${m}`,colorFocusError:a,borderFocusError:`1px solid ${m}`,boxShadowFocusError:`0 0 0 2px ${changeColor(f,{alpha:.2})}`,caretColorError:f,clearColor:$,clearColorHover:D,clearColorPressed:j,iconColor:ee,iconColorDisabled:ae,iconColorHover:se,iconColorPressed:Z,suffixTextColor:t})},inputLight={name:"Input",common:commonLight,self:self$M},inputLight$1=inputLight,inputInjectionKey="n-input";function len(e){let t=0;for(const o of e)t++;return t}function isEmptyInputValue(e){return e===""||e==null}function useCursor(e){const t=ref(null);function o(){const{value:i}=e;if(!(i!=null&&i.focus)){n();return}const{selectionStart:a,selectionEnd:l,value:s}=i;if(a==null||l==null){n();return}t.value={start:a,end:l,beforeText:s.slice(0,a),afterText:s.slice(l)}}function r(){var i;const{value:a}=t,{value:l}=e;if(!a||!l)return;const{value:s}=l,{start:d,beforeText:u,afterText:f}=a;let m=s.length;if(s.endsWith(f))m=s.length-f.length;else if(s.startsWith(u))m=u.length;else{const v=u[d-1],g=s.indexOf(v,d-1);g!==-1&&(m=g+1)}(i=l.setSelectionRange)===null||i===void 0||i.call(l,m,m)}function n(){t.value=null}return watch(e,n),{recordCursor:o,restoreCursor:r}}const WordCount=defineComponent({name:"InputWordCount",setup(e,{slots:t}){const{mergedValueRef:o,maxlengthRef:r,mergedClsPrefixRef:n,countGraphemesRef:i}=inject(inputInjectionKey),a=computed(()=>{const{value:l}=o;return l===null||Array.isArray(l)?0:(i.value||len)(l)});return()=>{const{value:l}=r,{value:s}=o;return h("span",{class:`${n.value}-input-word-count`},resolveSlotWithProps(t.default,{value:s===null||Array.isArray(s)?"":s},()=>[l===void 0?a.value:`${a.value} / ${l}`]))}}}),style$8=cB("input",`
 max-width: 100%;
 cursor: text;
 line-height: 1.5;
 z-index: auto;
 outline: none;
 box-sizing: border-box;
 position: relative;
 display: inline-flex;
 border-radius: var(--n-border-radius);
 background-color: var(--n-color);
 transition: background-color .3s var(--n-bezier);
 font-size: var(--n-font-size);
 --n-padding-vertical: calc((var(--n-height) - 1.5 * var(--n-font-size)) / 2);
`,[cE("input, textarea",`
 overflow: hidden;
 flex-grow: 1;
 position: relative;
 `),cE("input-el, textarea-el, input-mirror, textarea-mirror, separator, placeholder",`
 box-sizing: border-box;
 font-size: inherit;
 line-height: 1.5;
 font-family: inherit;
 border: none;
 outline: none;
 background-color: #0000;
 text-align: inherit;
 transition:
 -webkit-text-fill-color .3s var(--n-bezier),
 caret-color .3s var(--n-bezier),
 color .3s var(--n-bezier),
 text-decoration-color .3s var(--n-bezier);
 `),cE("input-el, textarea-el",`
 -webkit-appearance: none;
 scrollbar-width: none;
 width: 100%;
 min-width: 0;
 text-decoration-color: var(--n-text-decoration-color);
 color: var(--n-text-color);
 caret-color: var(--n-caret-color);
 background-color: transparent;
 `,[c("&::-webkit-scrollbar, &::-webkit-scrollbar-track-piece, &::-webkit-scrollbar-thumb",`
 width: 0;
 height: 0;
 display: none;
 `),c("&::placeholder",`
 color: #0000;
 -webkit-text-fill-color: transparent !important;
 `),c("&:-webkit-autofill ~",[cE("placeholder","display: none;")])]),cM("round",[cNotM("textarea","border-radius: calc(var(--n-height) / 2);")]),cE("placeholder",`
 pointer-events: none;
 position: absolute;
 left: 0;
 right: 0;
 top: 0;
 bottom: 0;
 overflow: hidden;
 color: var(--n-placeholder-color);
 `,[c("span",`
 width: 100%;
 display: inline-block;
 `)]),cM("textarea",[cE("placeholder","overflow: visible;")]),cNotM("autosize","width: 100%;"),cM("autosize",[cE("textarea-el, input-el",`
 position: absolute;
 top: 0;
 left: 0;
 height: 100%;
 `)]),cB("input-wrapper",`
 overflow: hidden;
 display: inline-flex;
 flex-grow: 1;
 position: relative;
 padding-left: var(--n-padding-left);
 padding-right: var(--n-padding-right);
 `),cE("input-mirror",`
 padding: 0;
 height: var(--n-height);
 line-height: var(--n-height);
 overflow: hidden;
 visibility: hidden;
 position: static;
 white-space: pre;
 pointer-events: none;
 `),cE("input-el",`
 padding: 0;
 height: var(--n-height);
 line-height: var(--n-height);
 `,[c("&[type=password]::-ms-reveal","display: none;"),c("+",[cE("placeholder",`
 display: flex;
 align-items: center; 
 `)])]),cNotM("textarea",[cE("placeholder","white-space: nowrap;")]),cE("eye",`
 display: flex;
 align-items: center;
 justify-content: center;
 transition: color .3s var(--n-bezier);
 `),cM("textarea","width: 100%;",[cB("input-word-count",`
 position: absolute;
 right: var(--n-padding-right);
 bottom: var(--n-padding-vertical);
 `),cM("resizable",[cB("input-wrapper",`
 resize: vertical;
 min-height: var(--n-height);
 `)]),cE("textarea-el, textarea-mirror, placeholder",`
 height: 100%;
 padding-left: 0;
 padding-right: 0;
 padding-top: var(--n-padding-vertical);
 padding-bottom: var(--n-padding-vertical);
 word-break: break-word;
 display: inline-block;
 vertical-align: bottom;
 box-sizing: border-box;
 line-height: var(--n-line-height-textarea);
 margin: 0;
 resize: none;
 white-space: pre-wrap;
 scroll-padding-block-end: var(--n-padding-vertical);
 `),cE("textarea-mirror",`
 width: 100%;
 pointer-events: none;
 overflow: hidden;
 visibility: hidden;
 position: static;
 white-space: pre-wrap;
 overflow-wrap: break-word;
 `)]),cM("pair",[cE("input-el, placeholder","text-align: center;"),cE("separator",`
 display: flex;
 align-items: center;
 transition: color .3s var(--n-bezier);
 color: var(--n-text-color);
 white-space: nowrap;
 `,[cB("icon",`
 color: var(--n-icon-color);
 `),cB("base-icon",`
 color: var(--n-icon-color);
 `)])]),cM("disabled",`
 cursor: not-allowed;
 background-color: var(--n-color-disabled);
 `,[cE("border","border: var(--n-border-disabled);"),cE("input-el, textarea-el",`
 cursor: not-allowed;
 color: var(--n-text-color-disabled);
 text-decoration-color: var(--n-text-color-disabled);
 `),cE("placeholder","color: var(--n-placeholder-color-disabled);"),cE("separator","color: var(--n-text-color-disabled);",[cB("icon",`
 color: var(--n-icon-color-disabled);
 `),cB("base-icon",`
 color: var(--n-icon-color-disabled);
 `)]),cB("input-word-count",`
 color: var(--n-count-text-color-disabled);
 `),cE("suffix, prefix","color: var(--n-text-color-disabled);",[cB("icon",`
 color: var(--n-icon-color-disabled);
 `),cB("internal-icon",`
 color: var(--n-icon-color-disabled);
 `)])]),cNotM("disabled",[cE("eye",`
 color: var(--n-icon-color);
 cursor: pointer;
 `,[c("&:hover",`
 color: var(--n-icon-color-hover);
 `),c("&:active",`
 color: var(--n-icon-color-pressed);
 `)]),c("&:hover",[cE("state-border","border: var(--n-border-hover);")]),cM("focus","background-color: var(--n-color-focus);",[cE("state-border",`
 border: var(--n-border-focus);
 box-shadow: var(--n-box-shadow-focus);
 `)])]),cE("border, state-border",`
 box-sizing: border-box;
 position: absolute;
 left: 0;
 right: 0;
 top: 0;
 bottom: 0;
 pointer-events: none;
 border-radius: inherit;
 border: var(--n-border);
 transition:
 box-shadow .3s var(--n-bezier),
 border-color .3s var(--n-bezier);
 `),cE("state-border",`
 border-color: #0000;
 z-index: 1;
 `),cE("prefix","margin-right: 4px;"),cE("suffix",`
 margin-left: 4px;
 `),cE("suffix, prefix",`
 transition: color .3s var(--n-bezier);
 flex-wrap: nowrap;
 flex-shrink: 0;
 line-height: var(--n-height);
 white-space: nowrap;
 display: inline-flex;
 align-items: center;
 justify-content: center;
 color: var(--n-suffix-text-color);
 `,[cB("base-loading",`
 font-size: var(--n-icon-size);
 margin: 0 2px;
 color: var(--n-loading-color);
 `),cB("base-clear",`
 font-size: var(--n-icon-size);
 `,[cE("placeholder",[cB("base-icon",`
 transition: color .3s var(--n-bezier);
 color: var(--n-icon-color);
 font-size: var(--n-icon-size);
 `)])]),c(">",[cB("icon",`
 transition: color .3s var(--n-bezier);
 color: var(--n-icon-color);
 font-size: var(--n-icon-size);
 `)]),cB("base-icon",`
 font-size: var(--n-icon-size);
 `)]),cB("input-word-count",`
 pointer-events: none;
 line-height: 1.5;
 font-size: .85em;
 color: var(--n-count-text-color);
 transition: color .3s var(--n-bezier);
 margin-left: 4px;
 font-variant: tabular-nums;
 `),["warning","error"].map(e=>cM(`${e}-status`,[cNotM("disabled",[cB("base-loading",`
 color: var(--n-loading-color-${e})
 `),cE("input-el, textarea-el",`
 caret-color: var(--n-caret-color-${e});
 `),cE("state-border",`
 border: var(--n-border-${e});
 `),c("&:hover",[cE("state-border",`
 border: var(--n-border-hover-${e});
 `)]),c("&:focus",`
 background-color: var(--n-color-focus-${e});
 `,[cE("state-border",`
 box-shadow: var(--n-box-shadow-focus-${e});
 border: var(--n-border-focus-${e});
 `)]),cM("focus",`
 background-color: var(--n-color-focus-${e});
 `,[cE("state-border",`
 box-shadow: var(--n-box-shadow-focus-${e});
 border: var(--n-border-focus-${e});
 `)])])]))]),safariStyle=cB("input",[cM("disabled",[cE("input-el, textarea-el",`
 -webkit-text-fill-color: var(--n-text-color-disabled);
 `)])]),inputProps=Object.assign(Object.assign({},useTheme.props),{bordered:{type:Boolean,default:void 0},type:{type:String,default:"text"},placeholder:[Array,String],defaultValue:{type:[String,Array],default:null},value:[String,Array],disabled:{type:Boolean,default:void 0},size:String,rows:{type:[Number,String],default:3},round:Boolean,minlength:[String,Number],maxlength:[String,Number],clearable:Boolean,autosize:{type:[Boolean,Object],default:!1},pair:Boolean,separator:String,readonly:{type:[String,Boolean],default:!1},passivelyActivated:Boolean,showPasswordOn:String,stateful:{type:Boolean,default:!0},autofocus:Boolean,inputProps:Object,resizable:{type:Boolean,default:!0},showCount:Boolean,loading:{type:Boolean,default:void 0},allowInput:Function,renderCount:Function,onMousedown:Function,onKeydown:Function,onKeyup:[Function,Array],onInput:[Function,Array],onFocus:[Function,Array],onBlur:[Function,Array],onClick:[Function,Array],onChange:[Function,Array],onClear:[Function,Array],countGraphemes:Function,status:String,"onUpdate:value":[Function,Array],onUpdateValue:[Function,Array],textDecoration:[String,Array],attrSize:{type:Number,default:20},onInputBlur:[Function,Array],onInputFocus:[Function,Array],onDeactivate:[Function,Array],onActivate:[Function,Array],onWrapperFocus:[Function,Array],onWrapperBlur:[Function,Array],internalDeactivateOnEnter:Boolean,internalForceFocus:Boolean,internalLoadingBeforeSuffix:{type:Boolean,default:!0},showPasswordToggle:Boolean}),__unplugin_components_0$1=defineComponent({name:"Input",props:inputProps,setup(e){const{mergedClsPrefixRef:t,mergedBorderedRef:o,inlineThemeDisabled:r,mergedRtlRef:n}=useConfig(e),i=useTheme("Input","-input",style$8,inputLight$1,e,t);isSafari&&useStyle("-input-safari",safariStyle,t);const a=ref(null),l=ref(null),s=ref(null),d=ref(null),u=ref(null),f=ref(null),m=ref(null),v=useCursor(m),g=ref(null),{localeRef:x}=useLocale("Input"),C=ref(e.defaultValue),y=toRef(e,"value"),I=useMergedState(y,C),N=useFormItem(e),{mergedSizeRef:E,mergedDisabledRef:P,mergedStatusRef:F}=N,S=ref(!1),$=ref(!1),D=ref(!1),j=ref(!1);let K=null;const z=computed(()=>{const{placeholder:T,pair:W}=e;return W?Array.isArray(T)?T:T===void 0?["",""]:[T,T]:T===void 0?[x.value.placeholder]:[T]}),ee=computed(()=>{const{value:T}=D,{value:W}=I,{value:de}=z;return!T&&(isEmptyInputValue(W)||Array.isArray(W)&&isEmptyInputValue(W[0]))&&de[0]}),ae=computed(()=>{const{value:T}=D,{value:W}=I,{value:de}=z;return!T&&de[1]&&(isEmptyInputValue(W)||Array.isArray(W)&&isEmptyInputValue(W[1]))}),se=useMemo(()=>e.internalForceFocus||S.value),Z=useMemo(()=>{if(P.value||e.readonly||!e.clearable||!se.value&&!$.value)return!1;const{value:T}=I,{value:W}=se;return e.pair?!!(Array.isArray(T)&&(T[0]||T[1]))&&($.value||W):!!T&&($.value||W)}),q=computed(()=>{const{showPasswordOn:T}=e;if(T)return T;if(e.showPasswordToggle)return"click"}),ne=ref(!1),he=computed(()=>{const{textDecoration:T}=e;return T?Array.isArray(T)?T.map(W=>({textDecoration:W})):[{textDecoration:T}]:["",""]}),ge=ref(void 0),ve=()=>{var T,W;if(e.type==="textarea"){const{autosize:de}=e;if(de&&(ge.value=(W=(T=g.value)===null||T===void 0?void 0:T.$el)===null||W===void 0?void 0:W.offsetWidth),!l.value||typeof de=="boolean")return;const{paddingTop:Se,paddingBottom:Te,lineHeight:xe}=window.getComputedStyle(l.value),He=Number(Se.slice(0,-2)),Fe=Number(Te.slice(0,-2)),Le=Number(xe.slice(0,-2)),{value:We}=s;if(!We)return;if(de.minRows){const Ve=Math.max(de.minRows,1),Ge=`${He+Fe+Le*Ve}px`;We.style.minHeight=Ge}if(de.maxRows){const Ve=`${He+Fe+Le*de.maxRows}px`;We.style.maxHeight=Ve}}},te=computed(()=>{const{maxlength:T}=e;return T===void 0?void 0:Number(T)});onMounted(()=>{const{value:T}=I;Array.isArray(T)||Ce(T)});const Q=getCurrentInstance().proxy;function G(T){const{onUpdateValue:W,"onUpdate:value":de,onInput:Se}=e,{nTriggerFormInput:Te}=N;W&&call(W,T),de&&call(de,T),Se&&call(Se,T),C.value=T,Te()}function oe(T){const{onChange:W}=e,{nTriggerFormChange:de}=N;W&&call(W,T),C.value=T,de()}function U(T){const{onBlur:W}=e,{nTriggerFormBlur:de}=N;W&&call(W,T),de()}function le(T){const{onFocus:W}=e,{nTriggerFormFocus:de}=N;W&&call(W,T),de()}function $e(T){const{onClear:W}=e;W&&call(W,T)}function ce(T){const{onInputBlur:W}=e;W&&call(W,T)}function Ie(T){const{onInputFocus:W}=e;W&&call(W,T)}function De(){const{onDeactivate:T}=e;T&&call(T)}function b(){const{onActivate:T}=e;T&&call(T)}function w(T){const{onClick:W}=e;W&&call(W,T)}function R(T){const{onWrapperFocus:W}=e;W&&call(W,T)}function H(T){const{onWrapperBlur:W}=e;W&&call(W,T)}function L(){D.value=!0}function k(T){D.value=!1,T.target===f.value?O(T,1):O(T,0)}function O(T,W=0,de="input"){const Se=T.target.value;if(Ce(Se),T instanceof InputEvent&&!T.isComposing&&(D.value=!1),e.type==="textarea"){const{value:xe}=g;xe&&xe.syncUnifiedContainer()}if(K=Se,D.value)return;v.recordCursor();const Te=B(Se);if(Te)if(!e.pair)de==="input"?G(Se):oe(Se);else{let{value:xe}=I;Array.isArray(xe)?xe=[xe[0],xe[1]]:xe=["",""],xe[W]=Se,de==="input"?G(xe):oe(xe)}Q.$forceUpdate(),Te||nextTick(v.restoreCursor)}function B(T){const{countGraphemes:W,maxlength:de,minlength:Se}=e;if(W){let xe;if(de!==void 0&&(xe===void 0&&(xe=W(T)),xe>Number(de))||Se!==void 0&&(xe===void 0&&(xe=W(T)),xe<Number(de)))return!1}const{allowInput:Te}=e;return typeof Te=="function"?Te(T):!0}function A(T){ce(T),T.relatedTarget===a.value&&De(),T.relatedTarget!==null&&(T.relatedTarget===u.value||T.relatedTarget===f.value||T.relatedTarget===l.value)||(j.value=!1),X(T,"blur"),m.value=null}function M(T,W){Ie(T),S.value=!0,j.value=!0,b(),X(T,"focus"),W===0?m.value=u.value:W===1?m.value=f.value:W===2&&(m.value=l.value)}function Y(T){e.passivelyActivated&&(H(T),X(T,"blur"))}function J(T){e.passivelyActivated&&(S.value=!0,R(T),X(T,"focus"))}function X(T,W){T.relatedTarget!==null&&(T.relatedTarget===u.value||T.relatedTarget===f.value||T.relatedTarget===l.value||T.relatedTarget===a.value)||(W==="focus"?(le(T),S.value=!0):W==="blur"&&(U(T),S.value=!1))}function ie(T,W){O(T,W,"change")}function fe(T){w(T)}function pe(T){$e(T),e.pair?(G(["",""]),oe(["",""])):(G(""),oe(""))}function me(T){const{onMousedown:W}=e;W&&W(T);const{tagName:de}=T.target;if(de!=="INPUT"&&de!=="TEXTAREA"){if(e.resizable){const{value:Se}=a;if(Se){const{left:Te,top:xe,width:He,height:Fe}=Se.getBoundingClientRect(),Le=14;if(Te+He-Le<T.clientX&&T.clientX<Te+He&&xe+Fe-Le<T.clientY&&T.clientY<xe+Fe)return}}T.preventDefault(),S.value||re()}}function ye(){var T;$.value=!0,e.type==="textarea"&&((T=g.value)===null||T===void 0||T.handleMouseEnterWrapper())}function ke(){var T;$.value=!1,e.type==="textarea"&&((T=g.value)===null||T===void 0||T.handleMouseLeaveWrapper())}function Re(){P.value||q.value==="click"&&(ne.value=!ne.value)}function Ne(T){if(P.value)return;T.preventDefault();const W=Se=>{Se.preventDefault(),off("mouseup",document,W)};if(on("mouseup",document,W),q.value!=="mousedown")return;ne.value=!0;const de=()=>{ne.value=!1,off("mouseup",document,de)};on("mouseup",document,de)}function Be(T){e.onKeyup&&call(e.onKeyup,T)}function _e(T){switch(e.onKeydown&&call(e.onKeydown,T),T.key){case"Escape":V();break;case"Enter":Pe(T);break}}function Pe(T){var W,de;if(e.passivelyActivated){const{value:Se}=j;if(Se){e.internalDeactivateOnEnter&&V();return}T.preventDefault(),e.type==="textarea"?(W=l.value)===null||W===void 0||W.focus():(de=u.value)===null||de===void 0||de.focus()}}function V(){e.passivelyActivated&&(j.value=!1,nextTick(()=>{var T;(T=a.value)===null||T===void 0||T.focus()}))}function re(){var T,W,de;P.value||(e.passivelyActivated?(T=a.value)===null||T===void 0||T.focus():((W=l.value)===null||W===void 0||W.focus(),(de=u.value)===null||de===void 0||de.focus()))}function ue(){var T;!((T=a.value)===null||T===void 0)&&T.contains(document.activeElement)&&document.activeElement.blur()}function be(){var T,W;(T=l.value)===null||T===void 0||T.select(),(W=u.value)===null||W===void 0||W.select()}function we(){P.value||(l.value?l.value.focus():u.value&&u.value.focus())}function Ee(){const{value:T}=a;T!=null&&T.contains(document.activeElement)&&T!==document.activeElement&&V()}function Me(T){if(e.type==="textarea"){const{value:W}=l;W==null||W.scrollTo(T)}else{const{value:W}=u;W==null||W.scrollTo(T)}}function Ce(T){const{type:W,pair:de,autosize:Se}=e;if(!de&&Se)if(W==="textarea"){const{value:Te}=s;Te&&(Te.textContent=(T??"")+`\r
`)}else{const{value:Te}=d;Te&&(T?Te.textContent=T:Te.innerHTML="&nbsp;")}}function Oe(){ve()}const Ae=ref({top:"0"});function Je(T){var W;const{scrollTop:de}=T.target;Ae.value.top=`${-de}px`,(W=g.value)===null||W===void 0||W.syncUnifiedContainer()}let Ue=null;watchEffect(()=>{const{autosize:T,type:W}=e;T&&W==="textarea"?Ue=watch(I,de=>{!Array.isArray(de)&&de!==K&&Ce(de)}):Ue==null||Ue()});let qe=null;watchEffect(()=>{e.type==="textarea"?qe=watch(I,T=>{var W;!Array.isArray(T)&&T!==K&&((W=g.value)===null||W===void 0||W.syncUnifiedContainer())}):qe==null||qe()}),provide(inputInjectionKey,{mergedValueRef:I,maxlengthRef:te,mergedClsPrefixRef:t,countGraphemesRef:toRef(e,"countGraphemes")});const Xe={wrapperElRef:a,inputElRef:u,textareaElRef:l,isCompositing:D,focus:re,blur:ue,select:be,deactivate:Ee,activate:we,scrollTo:Me},Ze=useRtl("Input",n,t),Ye=computed(()=>{const{value:T}=E,{common:{cubicBezierEaseInOut:W},self:{color:de,borderRadius:Se,textColor:Te,caretColor:xe,caretColorError:He,caretColorWarning:Fe,textDecorationColor:Le,border:We,borderDisabled:Ve,borderHover:Ge,borderFocus:Qe,placeholderColor:et,placeholderColorDisabled:tt,lineHeightTextarea:ot,colorDisabled:rt,colorFocus:nt,textColorDisabled:it,boxShadowFocus:at,iconSize:lt,colorFocusWarning:st,boxShadowFocusWarning:ct,borderWarning:dt,borderFocusWarning:ut,borderHoverWarning:ft,colorFocusError:pt,boxShadowFocusError:ht,borderError:mt,borderFocusError:gt,borderHoverError:vt,clearSize:bt,clearColor:Ct,clearColorHover:xt,clearColorPressed:yt,iconColor:St,iconColorDisabled:$t,suffixTextColor:wt,countTextColor:Tt,countTextColorDisabled:kt,iconColorHover:Pt,iconColorPressed:Et,loadingColor:It,loadingColorError:Dt,loadingColorWarning:Rt,[createKey("padding",T)]:_t,[createKey("fontSize",T)]:Mt,[createKey("height",T)]:Ot}}=i.value,{left:Bt,right:At}=getMargin(_t);return{"--n-bezier":W,"--n-count-text-color":Tt,"--n-count-text-color-disabled":kt,"--n-color":de,"--n-font-size":Mt,"--n-border-radius":Se,"--n-height":Ot,"--n-padding-left":Bt,"--n-padding-right":At,"--n-text-color":Te,"--n-caret-color":xe,"--n-text-decoration-color":Le,"--n-border":We,"--n-border-disabled":Ve,"--n-border-hover":Ge,"--n-border-focus":Qe,"--n-placeholder-color":et,"--n-placeholder-color-disabled":tt,"--n-icon-size":lt,"--n-line-height-textarea":ot,"--n-color-disabled":rt,"--n-color-focus":nt,"--n-text-color-disabled":it,"--n-box-shadow-focus":at,"--n-loading-color":It,"--n-caret-color-warning":Fe,"--n-color-focus-warning":st,"--n-box-shadow-focus-warning":ct,"--n-border-warning":dt,"--n-border-focus-warning":ut,"--n-border-hover-warning":ft,"--n-loading-color-warning":Rt,"--n-caret-color-error":He,"--n-color-focus-error":pt,"--n-box-shadow-focus-error":ht,"--n-border-error":mt,"--n-border-focus-error":gt,"--n-border-hover-error":vt,"--n-loading-color-error":Dt,"--n-clear-color":Ct,"--n-clear-size":bt,"--n-clear-color-hover":xt,"--n-clear-color-pressed":yt,"--n-icon-color":St,"--n-icon-color-hover":Pt,"--n-icon-color-pressed":Et,"--n-icon-color-disabled":$t,"--n-suffix-text-color":wt}}),je=r?useThemeClass("input",computed(()=>{const{value:T}=E;return T[0]}),Ye,e):void 0;return Object.assign(Object.assign({},Xe),{wrapperElRef:a,inputElRef:u,inputMirrorElRef:d,inputEl2Ref:f,textareaElRef:l,textareaMirrorElRef:s,textareaScrollbarInstRef:g,rtlEnabled:Ze,uncontrolledValue:C,mergedValue:I,passwordVisible:ne,mergedPlaceholder:z,showPlaceholder1:ee,showPlaceholder2:ae,mergedFocus:se,isComposing:D,activated:j,showClearButton:Z,mergedSize:E,mergedDisabled:P,textDecorationStyle:he,mergedClsPrefix:t,mergedBordered:o,mergedShowPasswordOn:q,placeholderStyle:Ae,mergedStatus:F,textAreaScrollContainerWidth:ge,handleTextAreaScroll:Je,handleCompositionStart:L,handleCompositionEnd:k,handleInput:O,handleInputBlur:A,handleInputFocus:M,handleWrapperBlur:Y,handleWrapperFocus:J,handleMouseEnter:ye,handleMouseLeave:ke,handleMouseDown:me,handleChange:ie,handleClick:fe,handleClear:pe,handlePasswordToggleClick:Re,handlePasswordToggleMousedown:Ne,handleWrapperKeydown:_e,handleWrapperKeyup:Be,handleTextAreaMirrorResize:Oe,getTextareaScrollContainer:()=>l.value,mergedTheme:i,cssVars:r?void 0:Ye,themeClass:je==null?void 0:je.themeClass,onRender:je==null?void 0:je.onRender})},render(){var e,t;const{mergedClsPrefix:o,mergedStatus:r,themeClass:n,type:i,countGraphemes:a,onRender:l}=this,s=this.$slots;return l==null||l(),h("div",{ref:"wrapperElRef",class:[`${o}-input`,n,r&&`${o}-input--${r}-status`,{[`${o}-input--rtl`]:this.rtlEnabled,[`${o}-input--disabled`]:this.mergedDisabled,[`${o}-input--textarea`]:i==="textarea",[`${o}-input--resizable`]:this.resizable&&!this.autosize,[`${o}-input--autosize`]:this.autosize,[`${o}-input--round`]:this.round&&i!=="textarea",[`${o}-input--pair`]:this.pair,[`${o}-input--focus`]:this.mergedFocus,[`${o}-input--stateful`]:this.stateful}],style:this.cssVars,tabindex:!this.mergedDisabled&&this.passivelyActivated&&!this.activated?0:void 0,onFocus:this.handleWrapperFocus,onBlur:this.handleWrapperBlur,onClick:this.handleClick,onMousedown:this.handleMouseDown,onMouseenter:this.handleMouseEnter,onMouseleave:this.handleMouseLeave,onCompositionstart:this.handleCompositionStart,onCompositionend:this.handleCompositionEnd,onKeyup:this.handleWrapperKeyup,onKeydown:this.handleWrapperKeydown},h("div",{class:`${o}-input-wrapper`},resolveWrappedSlot(s.prefix,d=>d&&h("div",{class:`${o}-input__prefix`},d)),i==="textarea"?h(NScrollbar,{ref:"textareaScrollbarInstRef",class:`${o}-input__textarea`,container:this.getTextareaScrollContainer,triggerDisplayManually:!0,useUnifiedContainer:!0,internalHoistYRail:!0},{default:()=>{var d,u;const{textAreaScrollContainerWidth:f}=this,m={width:this.autosize&&f&&`${f}px`};return h(Fragment,null,h("textarea",Object.assign({},this.inputProps,{ref:"textareaElRef",class:[`${o}-input__textarea-el`,(d=this.inputProps)===null||d===void 0?void 0:d.class],autofocus:this.autofocus,rows:Number(this.rows),placeholder:this.placeholder,value:this.mergedValue,disabled:this.mergedDisabled,maxlength:a?void 0:this.maxlength,minlength:a?void 0:this.minlength,readonly:this.readonly,tabindex:this.passivelyActivated&&!this.activated?-1:void 0,style:[this.textDecorationStyle[0],(u=this.inputProps)===null||u===void 0?void 0:u.style,m],onBlur:this.handleInputBlur,onFocus:v=>{this.handleInputFocus(v,2)},onInput:this.handleInput,onChange:this.handleChange,onScroll:this.handleTextAreaScroll})),this.showPlaceholder1?h("div",{class:`${o}-input__placeholder`,style:[this.placeholderStyle,m],key:"placeholder"},this.mergedPlaceholder[0]):null,this.autosize?h(VResizeObserver,{onResize:this.handleTextAreaMirrorResize},{default:()=>h("div",{ref:"textareaMirrorElRef",class:`${o}-input__textarea-mirror`,key:"mirror"})}):null)}}):h("div",{class:`${o}-input__input`},h("input",Object.assign({type:i==="password"&&this.mergedShowPasswordOn&&this.passwordVisible?"text":i},this.inputProps,{ref:"inputElRef",class:[`${o}-input__input-el`,(e=this.inputProps)===null||e===void 0?void 0:e.class],style:[this.textDecorationStyle[0],(t=this.inputProps)===null||t===void 0?void 0:t.style],tabindex:this.passivelyActivated&&!this.activated?-1:void 0,placeholder:this.mergedPlaceholder[0],disabled:this.mergedDisabled,maxlength:a?void 0:this.maxlength,minlength:a?void 0:this.minlength,value:Array.isArray(this.mergedValue)?this.mergedValue[0]:this.mergedValue,readonly:this.readonly,autofocus:this.autofocus,size:this.attrSize,onBlur:this.handleInputBlur,onFocus:d=>{this.handleInputFocus(d,0)},onInput:d=>{this.handleInput(d,0)},onChange:d=>{this.handleChange(d,0)}})),this.showPlaceholder1?h("div",{class:`${o}-input__placeholder`},h("span",null,this.mergedPlaceholder[0])):null,this.autosize?h("div",{class:`${o}-input__input-mirror`,key:"mirror",ref:"inputMirrorElRef"}," "):null),!this.pair&&resolveWrappedSlot(s.suffix,d=>d||this.clearable||this.showCount||this.mergedShowPasswordOn||this.loading!==void 0?h("div",{class:`${o}-input__suffix`},[resolveWrappedSlot(s["clear-icon-placeholder"],u=>(this.clearable||u)&&h(NBaseClear,{clsPrefix:o,show:this.showClearButton,onClear:this.handleClear},{placeholder:()=>u,icon:()=>{var f,m;return(m=(f=this.$slots)["clear-icon"])===null||m===void 0?void 0:m.call(f)}})),this.internalLoadingBeforeSuffix?null:d,this.loading!==void 0?h(NBaseSuffix,{clsPrefix:o,loading:this.loading,showArrow:!1,showClear:!1,style:this.cssVars}):null,this.internalLoadingBeforeSuffix?d:null,this.showCount&&this.type!=="textarea"?h(WordCount,null,{default:u=>{var f;return(f=s.count)===null||f===void 0?void 0:f.call(s,u)}}):null,this.mergedShowPasswordOn&&this.type==="password"?h("div",{class:`${o}-input__eye`,onMousedown:this.handlePasswordToggleMousedown,onClick:this.handlePasswordToggleClick},this.passwordVisible?resolveSlot(s["password-visible-icon"],()=>[h(NBaseIcon,{clsPrefix:o},{default:()=>h(EyeIcon,null)})]):resolveSlot(s["password-invisible-icon"],()=>[h(NBaseIcon,{clsPrefix:o},{default:()=>h(EyeOffIcon,null)})])):null]):null)),this.pair?h("span",{class:`${o}-input__separator`},resolveSlot(s.separator,()=>[this.separator])):null,this.pair?h("div",{class:`${o}-input-wrapper`},h("div",{class:`${o}-input__input`},h("input",{ref:"inputEl2Ref",type:this.type,class:`${o}-input__input-el`,tabindex:this.passivelyActivated&&!this.activated?-1:void 0,placeholder:this.mergedPlaceholder[1],disabled:this.mergedDisabled,maxlength:a?void 0:this.maxlength,minlength:a?void 0:this.minlength,value:Array.isArray(this.mergedValue)?this.mergedValue[1]:void 0,readonly:this.readonly,style:this.textDecorationStyle[1],onBlur:this.handleInputBlur,onFocus:d=>{this.handleInputFocus(d,1)},onInput:d=>{this.handleInput(d,1)},onChange:d=>{this.handleChange(d,1)}}),this.showPlaceholder2?h("div",{class:`${o}-input__placeholder`},h("span",null,this.mergedPlaceholder[1])):null),resolveWrappedSlot(s.suffix,d=>(this.clearable||d)&&h("div",{class:`${o}-input__suffix`},[this.clearable&&h(NBaseClear,{clsPrefix:o,show:this.showClearButton,onClear:this.handleClear},{icon:()=>{var u;return(u=s["clear-icon"])===null||u===void 0?void 0:u.call(s)},placeholder:()=>{var u;return(u=s["clear-icon-placeholder"])===null||u===void 0?void 0:u.call(s)}}),d]))):null,this.mergedBordered?h("div",{class:`${o}-input__border`}):null,this.mergedBordered?h("div",{class:`${o}-input__state-border`}):null,this.showCount&&i==="textarea"?h(WordCount,null,{default:d=>{var u;const{renderCount:f}=this;return f?f(d):(u=s.count)===null||u===void 0?void 0:u.call(s,d)}}):null)}});function self$L(e){const{boxShadow2:t}=e;return{menuBoxShadow:t}}const autoCompleteDark={name:"AutoComplete",common:commonDark,peers:{InternalSelectMenu:internalSelectMenuDark$1,Input:inputDark$1},self:self$L},autoCompleteDark$1=autoCompleteDark,self$K=e=>{const{borderRadius:t,avatarColor:o,cardColor:r,fontSize:n,heightTiny:i,heightSmall:a,heightMedium:l,heightLarge:s,heightHuge:d,modalColor:u,popoverColor:f}=e;return{borderRadius:t,fontSize:n,border:`2px solid ${r}`,heightTiny:i,heightSmall:a,heightMedium:l,heightLarge:s,heightHuge:d,color:composite(r,o),colorModal:composite(u,o),colorPopover:composite(f,o)}},avatarDark={name:"Avatar",common:commonDark,self:self$K},avatarDark$1=avatarDark,self$J=()=>({gap:"-12px"}),avatarGroupDark={name:"AvatarGroup",common:commonDark,peers:{Avatar:avatarDark$1},self:self$J},avatarGroupDark$1=avatarGroupDark,commonVariables$h={width:"44px",height:"44px",borderRadius:"22px",iconSize:"26px"},backTopDark={name:"BackTop",common:commonDark,self(e){const{popoverColor:t,textColor2:o,primaryColorHover:r,primaryColorPressed:n}=e;return Object.assign(Object.assign({},commonVariables$h),{color:t,textColor:o,iconColor:o,iconColorHover:r,iconColorPressed:n,boxShadow:"0 2px 8px 0px rgba(0, 0, 0, .12)",boxShadowHover:"0 2px 12px 0px rgba(0, 0, 0, .18)",boxShadowPressed:"0 2px 12px 0px rgba(0, 0, 0, .18)"})}},backTopDark$1=backTopDark,badgeDark={name:"Badge",common:commonDark,self(e){const{errorColorSuppl:t,infoColorSuppl:o,successColorSuppl:r,warningColorSuppl:n,fontFamily:i}=e;return{color:t,colorInfo:o,colorSuccess:r,colorError:t,colorWarning:n,fontSize:"12px",fontFamily:i}}},badgeDark$1=badgeDark,commonVariables$g={fontWeightActive:"400"},self$I=e=>{const{fontSize:t,textColor3:o,textColor2:r,borderRadius:n,buttonColor2Hover:i,buttonColor2Pressed:a}=e;return Object.assign(Object.assign({},commonVariables$g),{fontSize:t,itemLineHeight:"1.25",itemTextColor:o,itemTextColorHover:r,itemTextColorPressed:r,itemTextColorActive:r,itemBorderRadius:n,itemColorHover:i,itemColorPressed:a,separatorColor:o})},breadcrumbDark={name:"Breadcrumb",common:commonDark,self:self$I},breadcrumbDark$1=breadcrumbDark;function createHoverColor(e){return composite(e,[255,255,255,.16])}function createPressedColor(e){return composite(e,[0,0,0,.12])}const buttonGroupInjectionKey="n-button-group",commonVariables$f={paddingTiny:"0 6px",paddingSmall:"0 10px",paddingMedium:"0 14px",paddingLarge:"0 18px",paddingRoundTiny:"0 10px",paddingRoundSmall:"0 14px",paddingRoundMedium:"0 18px",paddingRoundLarge:"0 22px",iconMarginTiny:"6px",iconMarginSmall:"6px",iconMarginMedium:"6px",iconMarginLarge:"6px",iconSizeTiny:"14px",iconSizeSmall:"18px",iconSizeMedium:"18px",iconSizeLarge:"20px",rippleDuration:".6s"},self$H=e=>{const{heightTiny:t,heightSmall:o,heightMedium:r,heightLarge:n,borderRadius:i,fontSizeTiny:a,fontSizeSmall:l,fontSizeMedium:s,fontSizeLarge:d,opacityDisabled:u,textColor2:f,textColor3:m,primaryColorHover:v,primaryColorPressed:g,borderColor:x,primaryColor:C,baseColor:y,infoColor:I,infoColorHover:N,infoColorPressed:E,successColor:P,successColorHover:F,successColorPressed:S,warningColor:$,warningColorHover:D,warningColorPressed:j,errorColor:K,errorColorHover:z,errorColorPressed:ee,fontWeight:ae,buttonColor2:se,buttonColor2Hover:Z,buttonColor2Pressed:q,fontWeightStrong:ne}=e;return Object.assign(Object.assign({},commonVariables$f),{heightTiny:t,heightSmall:o,heightMedium:r,heightLarge:n,borderRadiusTiny:i,borderRadiusSmall:i,borderRadiusMedium:i,borderRadiusLarge:i,fontSizeTiny:a,fontSizeSmall:l,fontSizeMedium:s,fontSizeLarge:d,opacityDisabled:u,colorOpacitySecondary:"0.16",colorOpacitySecondaryHover:"0.22",colorOpacitySecondaryPressed:"0.28",colorSecondary:se,colorSecondaryHover:Z,colorSecondaryPressed:q,colorTertiary:se,colorTertiaryHover:Z,colorTertiaryPressed:q,colorQuaternary:"#0000",colorQuaternaryHover:Z,colorQuaternaryPressed:q,color:"#0000",colorHover:"#0000",colorPressed:"#0000",colorFocus:"#0000",colorDisabled:"#0000",textColor:f,textColorTertiary:m,textColorHover:v,textColorPressed:g,textColorFocus:v,textColorDisabled:f,textColorText:f,textColorTextHover:v,textColorTextPressed:g,textColorTextFocus:v,textColorTextDisabled:f,textColorGhost:f,textColorGhostHover:v,textColorGhostPressed:g,textColorGhostFocus:v,textColorGhostDisabled:f,border:`1px solid ${x}`,borderHover:`1px solid ${v}`,borderPressed:`1px solid ${g}`,borderFocus:`1px solid ${v}`,borderDisabled:`1px solid ${x}`,rippleColor:C,colorPrimary:C,colorHoverPrimary:v,colorPressedPrimary:g,colorFocusPrimary:v,colorDisabledPrimary:C,textColorPrimary:y,textColorHoverPrimary:y,textColorPressedPrimary:y,textColorFocusPrimary:y,textColorDisabledPrimary:y,textColorTextPrimary:C,textColorTextHoverPrimary:v,textColorTextPressedPrimary:g,textColorTextFocusPrimary:v,textColorTextDisabledPrimary:f,textColorGhostPrimary:C,textColorGhostHoverPrimary:v,textColorGhostPressedPrimary:g,textColorGhostFocusPrimary:v,textColorGhostDisabledPrimary:C,borderPrimary:`1px solid ${C}`,borderHoverPrimary:`1px solid ${v}`,borderPressedPrimary:`1px solid ${g}`,borderFocusPrimary:`1px solid ${v}`,borderDisabledPrimary:`1px solid ${C}`,rippleColorPrimary:C,colorInfo:I,colorHoverInfo:N,colorPressedInfo:E,colorFocusInfo:N,colorDisabledInfo:I,textColorInfo:y,textColorHoverInfo:y,textColorPressedInfo:y,textColorFocusInfo:y,textColorDisabledInfo:y,textColorTextInfo:I,textColorTextHoverInfo:N,textColorTextPressedInfo:E,textColorTextFocusInfo:N,textColorTextDisabledInfo:f,textColorGhostInfo:I,textColorGhostHoverInfo:N,textColorGhostPressedInfo:E,textColorGhostFocusInfo:N,textColorGhostDisabledInfo:I,borderInfo:`1px solid ${I}`,borderHoverInfo:`1px solid ${N}`,borderPressedInfo:`1px solid ${E}`,borderFocusInfo:`1px solid ${N}`,borderDisabledInfo:`1px solid ${I}`,rippleColorInfo:I,colorSuccess:P,colorHoverSuccess:F,colorPressedSuccess:S,colorFocusSuccess:F,colorDisabledSuccess:P,textColorSuccess:y,textColorHoverSuccess:y,textColorPressedSuccess:y,textColorFocusSuccess:y,textColorDisabledSuccess:y,textColorTextSuccess:P,textColorTextHoverSuccess:F,textColorTextPressedSuccess:S,textColorTextFocusSuccess:F,textColorTextDisabledSuccess:f,textColorGhostSuccess:P,textColorGhostHoverSuccess:F,textColorGhostPressedSuccess:S,textColorGhostFocusSuccess:F,textColorGhostDisabledSuccess:P,borderSuccess:`1px solid ${P}`,borderHoverSuccess:`1px solid ${F}`,borderPressedSuccess:`1px solid ${S}`,borderFocusSuccess:`1px solid ${F}`,borderDisabledSuccess:`1px solid ${P}`,rippleColorSuccess:P,colorWarning:$,colorHoverWarning:D,colorPressedWarning:j,colorFocusWarning:D,colorDisabledWarning:$,textColorWarning:y,textColorHoverWarning:y,textColorPressedWarning:y,textColorFocusWarning:y,textColorDisabledWarning:y,textColorTextWarning:$,textColorTextHoverWarning:D,textColorTextPressedWarning:j,textColorTextFocusWarning:D,textColorTextDisabledWarning:f,textColorGhostWarning:$,textColorGhostHoverWarning:D,textColorGhostPressedWarning:j,textColorGhostFocusWarning:D,textColorGhostDisabledWarning:$,borderWarning:`1px solid ${$}`,borderHoverWarning:`1px solid ${D}`,borderPressedWarning:`1px solid ${j}`,borderFocusWarning:`1px solid ${D}`,borderDisabledWarning:`1px solid ${$}`,rippleColorWarning:$,colorError:K,colorHoverError:z,colorPressedError:ee,colorFocusError:z,colorDisabledError:K,textColorError:y,textColorHoverError:y,textColorPressedError:y,textColorFocusError:y,textColorDisabledError:y,textColorTextError:K,textColorTextHoverError:z,textColorTextPressedError:ee,textColorTextFocusError:z,textColorTextDisabledError:f,textColorGhostError:K,textColorGhostHoverError:z,textColorGhostPressedError:ee,textColorGhostFocusError:z,textColorGhostDisabledError:K,borderError:`1px solid ${K}`,borderHoverError:`1px solid ${z}`,borderPressedError:`1px solid ${ee}`,borderFocusError:`1px solid ${z}`,borderDisabledError:`1px solid ${K}`,rippleColorError:K,waveOpacity:"0.6",fontWeight:ae,fontWeightStrong:ne})},buttonLight={name:"Button",common:commonLight,self:self$H},buttonDark={name:"Button",common:commonDark,self(e){const t=self$H(e);return t.waveOpacity="0.8",t.colorOpacitySecondary="0.16",t.colorOpacitySecondaryHover="0.2",t.colorOpacitySecondaryPressed="0.12",t}},buttonDark$1=buttonDark,style$7=c([cB("button",`
 margin: 0;
 font-weight: var(--n-font-weight);
 line-height: 1;
 font-family: inherit;
 padding: var(--n-padding);
 height: var(--n-height);
 font-size: var(--n-font-size);
 border-radius: var(--n-border-radius);
 color: var(--n-text-color);
 background-color: var(--n-color);
 width: var(--n-width);
 white-space: nowrap;
 outline: none;
 position: relative;
 z-index: auto;
 border: none;
 display: inline-flex;
 flex-wrap: nowrap;
 flex-shrink: 0;
 align-items: center;
 justify-content: center;
 user-select: none;
 -webkit-user-select: none;
 text-align: center;
 cursor: pointer;
 text-decoration: none;
 transition:
 color .3s var(--n-bezier),
 background-color .3s var(--n-bezier),
 opacity .3s var(--n-bezier),
 border-color .3s var(--n-bezier);
 `,[cM("color",[cE("border",{borderColor:"var(--n-border-color)"}),cM("disabled",[cE("border",{borderColor:"var(--n-border-color-disabled)"})]),cNotM("disabled",[c("&:focus",[cE("state-border",{borderColor:"var(--n-border-color-focus)"})]),c("&:hover",[cE("state-border",{borderColor:"var(--n-border-color-hover)"})]),c("&:active",[cE("state-border",{borderColor:"var(--n-border-color-pressed)"})]),cM("pressed",[cE("state-border",{borderColor:"var(--n-border-color-pressed)"})])])]),cM("disabled",{backgroundColor:"var(--n-color-disabled)",color:"var(--n-text-color-disabled)"},[cE("border",{border:"var(--n-border-disabled)"})]),cNotM("disabled",[c("&:focus",{backgroundColor:"var(--n-color-focus)",color:"var(--n-text-color-focus)"},[cE("state-border",{border:"var(--n-border-focus)"})]),c("&:hover",{backgroundColor:"var(--n-color-hover)",color:"var(--n-text-color-hover)"},[cE("state-border",{border:"var(--n-border-hover)"})]),c("&:active",{backgroundColor:"var(--n-color-pressed)",color:"var(--n-text-color-pressed)"},[cE("state-border",{border:"var(--n-border-pressed)"})]),cM("pressed",{backgroundColor:"var(--n-color-pressed)",color:"var(--n-text-color-pressed)"},[cE("state-border",{border:"var(--n-border-pressed)"})])]),cM("loading","cursor: wait;"),cB("base-wave",`
 pointer-events: none;
 top: 0;
 right: 0;
 bottom: 0;
 left: 0;
 animation-iteration-count: 1;
 animation-duration: var(--n-ripple-duration);
 animation-timing-function: var(--n-bezier-ease-out), var(--n-bezier-ease-out);
 `,[cM("active",{zIndex:1,animationName:"button-wave-spread, button-wave-opacity"})]),isBrowser$2&&"MozBoxSizing"in document.createElement("div").style?c("&::moz-focus-inner",{border:0}):null,cE("border, state-border",`
 position: absolute;
 left: 0;
 top: 0;
 right: 0;
 bottom: 0;
 border-radius: inherit;
 transition: border-color .3s var(--n-bezier);
 pointer-events: none;
 `),cE("border",{border:"var(--n-border)"}),cE("state-border",{border:"var(--n-border)",borderColor:"#0000",zIndex:1}),cE("icon",`
 margin: var(--n-icon-margin);
 margin-left: 0;
 height: var(--n-icon-size);
 width: var(--n-icon-size);
 max-width: var(--n-icon-size);
 font-size: var(--n-icon-size);
 position: relative;
 flex-shrink: 0;
 `,[cB("icon-slot",`
 height: var(--n-icon-size);
 width: var(--n-icon-size);
 position: absolute;
 left: 0;
 top: 50%;
 transform: translateY(-50%);
 display: flex;
 align-items: center;
 justify-content: center;
 `,[iconSwitchTransition({top:"50%",originalTransform:"translateY(-50%)"})]),fadeInWidthExpandTransition()]),cE("content",`
 display: flex;
 align-items: center;
 flex-wrap: nowrap;
 min-width: 0;
 `,[c("~",[cE("icon",{margin:"var(--n-icon-margin)",marginRight:0})])]),cM("block",`
 display: flex;
 width: 100%;
 `),cM("dashed",[cE("border, state-border",{borderStyle:"dashed !important"})]),cM("disabled",{cursor:"not-allowed",opacity:"var(--n-opacity-disabled)"})]),c("@keyframes button-wave-spread",{from:{boxShadow:"0 0 0.5px 0 var(--n-ripple-color)"},to:{boxShadow:"0 0 0.5px 4.5px var(--n-ripple-color)"}}),c("@keyframes button-wave-opacity",{from:{opacity:"var(--n-wave-opacity)"},to:{opacity:0}})]),buttonProps=Object.assign(Object.assign({},useTheme.props),{color:String,textColor:String,text:Boolean,block:Boolean,loading:Boolean,disabled:Boolean,circle:Boolean,size:String,ghost:Boolean,round:Boolean,secondary:Boolean,tertiary:Boolean,quaternary:Boolean,strong:Boolean,focusable:{type:Boolean,default:!0},keyboard:{type:Boolean,default:!0},tag:{type:String,default:"button"},type:{type:String,default:"default"},dashed:Boolean,renderIcon:Function,iconPlacement:{type:String,default:"left"},attrType:{type:String,default:"button"},bordered:{type:Boolean,default:!0},onClick:[Function,Array],nativeFocusBehavior:{type:Boolean,default:!isSafari}}),Button=defineComponent({name:"Button",props:buttonProps,setup(e){const t=ref(null),o=ref(null),r=ref(!1),n=useMemo(()=>!e.quaternary&&!e.tertiary&&!e.secondary&&!e.text&&(!e.color||e.ghost||e.dashed)&&e.bordered),i=inject(buttonGroupInjectionKey,{}),{mergedSizeRef:a}=useFormItem({},{defaultSize:"medium",mergedSize:E=>{const{size:P}=e;if(P)return P;const{size:F}=i;if(F)return F;const{mergedSize:S}=E||{};return S?S.value:"medium"}}),l=computed(()=>e.focusable&&!e.disabled),s=E=>{var P;l.value||E.preventDefault(),!e.nativeFocusBehavior&&(E.preventDefault(),!e.disabled&&l.value&&((P=t.value)===null||P===void 0||P.focus({preventScroll:!0})))},d=E=>{var P;if(!e.disabled&&!e.loading){const{onClick:F}=e;F&&call(F,E),e.text||(P=o.value)===null||P===void 0||P.play()}},u=E=>{switch(E.key){case"Enter":if(!e.keyboard)return;r.value=!1}},f=E=>{switch(E.key){case"Enter":if(!e.keyboard||e.loading){E.preventDefault();return}r.value=!0}},m=()=>{r.value=!1},{inlineThemeDisabled:v,mergedClsPrefixRef:g,mergedRtlRef:x}=useConfig(e),C=useTheme("Button","-button",style$7,buttonLight,e,g),y=useRtl("Button",x,g),I=computed(()=>{const E=C.value,{common:{cubicBezierEaseInOut:P,cubicBezierEaseOut:F},self:S}=E,{rippleDuration:$,opacityDisabled:D,fontWeight:j,fontWeightStrong:K}=S,z=a.value,{dashed:ee,type:ae,ghost:se,text:Z,color:q,round:ne,circle:he,textColor:ge,secondary:ve,tertiary:te,quaternary:Q,strong:G}=e,oe={"font-weight":G?K:j};let U={"--n-color":"initial","--n-color-hover":"initial","--n-color-pressed":"initial","--n-color-focus":"initial","--n-color-disabled":"initial","--n-ripple-color":"initial","--n-text-color":"initial","--n-text-color-hover":"initial","--n-text-color-pressed":"initial","--n-text-color-focus":"initial","--n-text-color-disabled":"initial"};const le=ae==="tertiary",$e=ae==="default",ce=le?"default":ae;if(Z){const A=ge||q;U={"--n-color":"#0000","--n-color-hover":"#0000","--n-color-pressed":"#0000","--n-color-focus":"#0000","--n-color-disabled":"#0000","--n-ripple-color":"#0000","--n-text-color":A||S[createKey("textColorText",ce)],"--n-text-color-hover":A?createHoverColor(A):S[createKey("textColorTextHover",ce)],"--n-text-color-pressed":A?createPressedColor(A):S[createKey("textColorTextPressed",ce)],"--n-text-color-focus":A?createHoverColor(A):S[createKey("textColorTextHover",ce)],"--n-text-color-disabled":A||S[createKey("textColorTextDisabled",ce)]}}else if(se||ee){const A=ge||q;U={"--n-color":"#0000","--n-color-hover":"#0000","--n-color-pressed":"#0000","--n-color-focus":"#0000","--n-color-disabled":"#0000","--n-ripple-color":q||S[createKey("rippleColor",ce)],"--n-text-color":A||S[createKey("textColorGhost",ce)],"--n-text-color-hover":A?createHoverColor(A):S[createKey("textColorGhostHover",ce)],"--n-text-color-pressed":A?createPressedColor(A):S[createKey("textColorGhostPressed",ce)],"--n-text-color-focus":A?createHoverColor(A):S[createKey("textColorGhostHover",ce)],"--n-text-color-disabled":A||S[createKey("textColorGhostDisabled",ce)]}}else if(ve){const A=$e?S.textColor:le?S.textColorTertiary:S[createKey("color",ce)],M=q||A,Y=ae!=="default"&&ae!=="tertiary";U={"--n-color":Y?changeColor(M,{alpha:Number(S.colorOpacitySecondary)}):S.colorSecondary,"--n-color-hover":Y?changeColor(M,{alpha:Number(S.colorOpacitySecondaryHover)}):S.colorSecondaryHover,"--n-color-pressed":Y?changeColor(M,{alpha:Number(S.colorOpacitySecondaryPressed)}):S.colorSecondaryPressed,"--n-color-focus":Y?changeColor(M,{alpha:Number(S.colorOpacitySecondaryHover)}):S.colorSecondaryHover,"--n-color-disabled":S.colorSecondary,"--n-ripple-color":"#0000","--n-text-color":M,"--n-text-color-hover":M,"--n-text-color-pressed":M,"--n-text-color-focus":M,"--n-text-color-disabled":M}}else if(te||Q){const A=$e?S.textColor:le?S.textColorTertiary:S[createKey("color",ce)],M=q||A;te?(U["--n-color"]=S.colorTertiary,U["--n-color-hover"]=S.colorTertiaryHover,U["--n-color-pressed"]=S.colorTertiaryPressed,U["--n-color-focus"]=S.colorSecondaryHover,U["--n-color-disabled"]=S.colorTertiary):(U["--n-color"]=S.colorQuaternary,U["--n-color-hover"]=S.colorQuaternaryHover,U["--n-color-pressed"]=S.colorQuaternaryPressed,U["--n-color-focus"]=S.colorQuaternaryHover,U["--n-color-disabled"]=S.colorQuaternary),U["--n-ripple-color"]="#0000",U["--n-text-color"]=M,U["--n-text-color-hover"]=M,U["--n-text-color-pressed"]=M,U["--n-text-color-focus"]=M,U["--n-text-color-disabled"]=M}else U={"--n-color":q||S[createKey("color",ce)],"--n-color-hover":q?createHoverColor(q):S[createKey("colorHover",ce)],"--n-color-pressed":q?createPressedColor(q):S[createKey("colorPressed",ce)],"--n-color-focus":q?createHoverColor(q):S[createKey("colorFocus",ce)],"--n-color-disabled":q||S[createKey("colorDisabled",ce)],"--n-ripple-color":q||S[createKey("rippleColor",ce)],"--n-text-color":ge||(q?S.textColorPrimary:le?S.textColorTertiary:S[createKey("textColor",ce)]),"--n-text-color-hover":ge||(q?S.textColorHoverPrimary:S[createKey("textColorHover",ce)]),"--n-text-color-pressed":ge||(q?S.textColorPressedPrimary:S[createKey("textColorPressed",ce)]),"--n-text-color-focus":ge||(q?S.textColorFocusPrimary:S[createKey("textColorFocus",ce)]),"--n-text-color-disabled":ge||(q?S.textColorDisabledPrimary:S[createKey("textColorDisabled",ce)])};let Ie={"--n-border":"initial","--n-border-hover":"initial","--n-border-pressed":"initial","--n-border-focus":"initial","--n-border-disabled":"initial"};Z?Ie={"--n-border":"none","--n-border-hover":"none","--n-border-pressed":"none","--n-border-focus":"none","--n-border-disabled":"none"}:Ie={"--n-border":S[createKey("border",ce)],"--n-border-hover":S[createKey("borderHover",ce)],"--n-border-pressed":S[createKey("borderPressed",ce)],"--n-border-focus":S[createKey("borderFocus",ce)],"--n-border-disabled":S[createKey("borderDisabled",ce)]};const{[createKey("height",z)]:De,[createKey("fontSize",z)]:b,[createKey("padding",z)]:w,[createKey("paddingRound",z)]:R,[createKey("iconSize",z)]:H,[createKey("borderRadius",z)]:L,[createKey("iconMargin",z)]:k,waveOpacity:O}=S,B={"--n-width":he&&!Z?De:"initial","--n-height":Z?"initial":De,"--n-font-size":b,"--n-padding":he||Z?"initial":ne?R:w,"--n-icon-size":H,"--n-icon-margin":k,"--n-border-radius":Z?"initial":he||ne?De:L};return Object.assign(Object.assign(Object.assign(Object.assign({"--n-bezier":P,"--n-bezier-ease-out":F,"--n-ripple-duration":$,"--n-opacity-disabled":D,"--n-wave-opacity":O},oe),U),Ie),B)}),N=v?useThemeClass("button",computed(()=>{let E="";const{dashed:P,type:F,ghost:S,text:$,color:D,round:j,circle:K,textColor:z,secondary:ee,tertiary:ae,quaternary:se,strong:Z}=e;P&&(E+="a"),S&&(E+="b"),$&&(E+="c"),j&&(E+="d"),K&&(E+="e"),ee&&(E+="f"),ae&&(E+="g"),se&&(E+="h"),Z&&(E+="i"),D&&(E+="j"+color2Class(D)),z&&(E+="k"+color2Class(z));const{value:q}=a;return E+="l"+q[0],E+="m"+F[0],E}),I,e):void 0;return{selfElRef:t,waveElRef:o,mergedClsPrefix:g,mergedFocusable:l,mergedSize:a,showBorder:n,enterPressed:r,rtlEnabled:y,handleMousedown:s,handleKeydown:f,handleBlur:m,handleKeyup:u,handleClick:d,customColorCssVars:computed(()=>{const{color:E}=e;if(!E)return null;const P=createHoverColor(E);return{"--n-border-color":E,"--n-border-color-hover":P,"--n-border-color-pressed":createPressedColor(E),"--n-border-color-focus":P,"--n-border-color-disabled":E}}),cssVars:v?void 0:I,themeClass:N==null?void 0:N.themeClass,onRender:N==null?void 0:N.onRender}},render(){const{mergedClsPrefix:e,tag:t,onRender:o}=this;o==null||o();const r=resolveWrappedSlot(this.$slots.default,n=>n&&h("span",{class:`${e}-button__content`},n));return h(t,{ref:"selfElRef",class:[this.themeClass,`${e}-button`,`${e}-button--${this.type}-type`,`${e}-button--${this.mergedSize}-type`,this.rtlEnabled&&`${e}-button--rtl`,this.disabled&&`${e}-button--disabled`,this.block&&`${e}-button--block`,this.enterPressed&&`${e}-button--pressed`,!this.text&&this.dashed&&`${e}-button--dashed`,this.color&&`${e}-button--color`,this.secondary&&`${e}-button--secondary`,this.loading&&`${e}-button--loading`,this.ghost&&`${e}-button--ghost`],tabindex:this.mergedFocusable?0:-1,type:this.attrType,style:this.cssVars,disabled:this.disabled,onClick:this.handleClick,onBlur:this.handleBlur,onMousedown:this.handleMousedown,onKeyup:this.handleKeyup,onKeydown:this.handleKeydown},this.iconPlacement==="right"&&r,h(NFadeInExpandTransition,{width:!0},{default:()=>resolveWrappedSlot(this.$slots.icon,n=>(this.loading||this.renderIcon||n)&&h("span",{class:`${e}-button__icon`,style:{margin:isSlotEmpty(this.$slots.default)?"0":""}},h(NIconSwitchTransition,null,{default:()=>this.loading?h(NBaseLoading,{clsPrefix:e,key:"loading",class:`${e}-icon-slot`,strokeWidth:20}):h("div",{key:"icon",class:`${e}-icon-slot`,role:"none"},this.renderIcon?this.renderIcon():n)})))}),this.iconPlacement==="left"&&r,this.text?null:h(NBaseWave,{ref:"waveElRef",clsPrefix:e}),this.showBorder?h("div",{"aria-hidden":!0,class:`${e}-button__border`,style:this.customColorCssVars}):null,this.showBorder?h("div",{"aria-hidden":!0,class:`${e}-button__state-border`,style:this.customColorCssVars}):null)}}),NButton=Button,XButton=Button,commonVariables$e={titleFontSize:"22px"},self$G=e=>{const{borderRadius:t,fontSize:o,lineHeight:r,textColor2:n,textColor1:i,textColorDisabled:a,dividerColor:l,fontWeightStrong:s,primaryColor:d,baseColor:u,hoverColor:f,cardColor:m,modalColor:v,popoverColor:g}=e;return Object.assign(Object.assign({},commonVariables$e),{borderRadius:t,borderColor:composite(m,l),borderColorModal:composite(v,l),borderColorPopover:composite(g,l),textColor:n,titleFontWeight:s,titleTextColor:i,dayTextColor:a,fontSize:o,lineHeight:r,dateColorCurrent:d,dateTextColorCurrent:u,cellColorHover:composite(m,f),cellColorHoverModal:composite(v,f),cellColorHoverPopover:composite(g,f),cellColor:m,cellColorModal:v,cellColorPopover:g,barColor:d})},calendarDark={name:"Calendar",common:commonDark,peers:{Button:buttonDark$1},self:self$G},calendarDark$1=calendarDark,self$F=e=>{const{fontSize:t,boxShadow2:o,popoverColor:r,textColor2:n,borderRadius:i,borderColor:a,heightSmall:l,heightMedium:s,heightLarge:d,fontSizeSmall:u,fontSizeMedium:f,fontSizeLarge:m,dividerColor:v}=e;return{panelFontSize:t,boxShadow:o,color:r,textColor:n,borderRadius:i,border:`1px solid ${a}`,heightSmall:l,heightMedium:s,heightLarge:d,fontSizeSmall:u,fontSizeMedium:f,fontSizeLarge:m,dividerColor:v}},colorPickerDark={name:"ColorPicker",common:commonDark,peers:{Input:inputDark$1,Button:buttonDark$1},self:self$F},colorPickerDark$1=colorPickerDark,commonVariables$d={paddingSmall:"12px 16px 12px",paddingMedium:"19px 24px 20px",paddingLarge:"23px 32px 24px",paddingHuge:"27px 40px 28px",titleFontSizeSmall:"16px",titleFontSizeMedium:"18px",titleFontSizeLarge:"18px",titleFontSizeHuge:"18px",closeIconSize:"18px",closeSize:"22px"},self$E=e=>{const{primaryColor:t,borderRadius:o,lineHeight:r,fontSize:n,cardColor:i,textColor2:a,textColor1:l,dividerColor:s,fontWeightStrong:d,closeIconColor:u,closeIconColorHover:f,closeIconColorPressed:m,closeColorHover:v,closeColorPressed:g,modalColor:x,boxShadow1:C,popoverColor:y,actionColor:I}=e;return Object.assign(Object.assign({},commonVariables$d),{lineHeight:r,color:i,colorModal:x,colorPopover:y,colorTarget:t,colorEmbedded:I,colorEmbeddedModal:I,colorEmbeddedPopover:I,textColor:a,titleTextColor:l,borderColor:s,actionColor:I,titleFontWeight:d,closeColorHover:v,closeColorPressed:g,closeBorderRadius:o,closeIconColor:u,closeIconColorHover:f,closeIconColorPressed:m,fontSizeSmall:n,fontSizeMedium:n,fontSizeLarge:n,fontSizeHuge:n,boxShadow:C,borderRadius:o})},cardLight={name:"Card",common:commonLight,self:self$E},cardDark={name:"Card",common:commonDark,self(e){const t=self$E(e),{cardColor:o,modalColor:r,popoverColor:n}=e;return t.colorEmbedded=o,t.colorEmbeddedModal=r,t.colorEmbeddedPopover=n,t}},cardDark$1=cardDark,style$6=c([cB("card",`
 font-size: var(--n-font-size);
 line-height: var(--n-line-height);
 display: flex;
 flex-direction: column;
 width: 100%;
 box-sizing: border-box;
 position: relative;
 border-radius: var(--n-border-radius);
 background-color: var(--n-color);
 color: var(--n-text-color);
 word-break: break-word;
 transition: 
 color .3s var(--n-bezier),
 background-color .3s var(--n-bezier),
 box-shadow .3s var(--n-bezier),
 border-color .3s var(--n-bezier);
 `,[asModal({background:"var(--n-color-modal)"}),cM("hoverable",[c("&:hover","box-shadow: var(--n-box-shadow);")]),cM("content-segmented",[c(">",[cE("content",{paddingTop:"var(--n-padding-bottom)"})])]),cM("content-soft-segmented",[c(">",[cE("content",`
 margin: 0 var(--n-padding-left);
 padding: var(--n-padding-bottom) 0;
 `)])]),cM("footer-segmented",[c(">",[cE("footer",{paddingTop:"var(--n-padding-bottom)"})])]),cM("footer-soft-segmented",[c(">",[cE("footer",`
 padding: var(--n-padding-bottom) 0;
 margin: 0 var(--n-padding-left);
 `)])]),c(">",[cB("card-header",`
 box-sizing: border-box;
 display: flex;
 align-items: center;
 font-size: var(--n-title-font-size);
 padding:
 var(--n-padding-top)
 var(--n-padding-left)
 var(--n-padding-bottom)
 var(--n-padding-left);
 `,[cE("main",`
 font-weight: var(--n-title-font-weight);
 transition: color .3s var(--n-bezier);
 flex: 1;
 min-width: 0;
 color: var(--n-title-text-color);
 `),cE("extra",`
 display: flex;
 align-items: center;
 font-size: var(--n-font-size);
 font-weight: 400;
 transition: color .3s var(--n-bezier);
 color: var(--n-text-color);
 `),cE("close",`
 margin: 0 0 0 8px;
 transition:
 background-color .3s var(--n-bezier),
 color .3s var(--n-bezier);
 `)]),cE("action",`
 box-sizing: border-box;
 transition:
 background-color .3s var(--n-bezier),
 border-color .3s var(--n-bezier);
 background-clip: padding-box;
 background-color: var(--n-action-color);
 `),cE("content","flex: 1; min-width: 0;"),cE("content, footer",`
 box-sizing: border-box;
 padding: 0 var(--n-padding-left) var(--n-padding-bottom) var(--n-padding-left);
 font-size: var(--n-font-size);
 `,[c("&:first-child",{paddingTop:"var(--n-padding-bottom)"})]),cE("action",`
 background-color: var(--n-action-color);
 padding: var(--n-padding-bottom) var(--n-padding-left);
 border-bottom-left-radius: var(--n-border-radius);
 border-bottom-right-radius: var(--n-border-radius);
 `)]),cB("card-cover",`
 overflow: hidden;
 width: 100%;
 border-radius: var(--n-border-radius) var(--n-border-radius) 0 0;
 `,[c("img",`
 display: block;
 width: 100%;
 `)]),cM("bordered",`
 border: 1px solid var(--n-border-color);
 `,[c("&:target","border-color: var(--n-color-target);")]),cM("action-segmented",[c(">",[cE("action",[c("&:not(:first-child)",{borderTop:"1px solid var(--n-border-color)"})])])]),cM("content-segmented, content-soft-segmented",[c(">",[cE("content",{transition:"border-color 0.3s var(--n-bezier)"},[c("&:not(:first-child)",{borderTop:"1px solid var(--n-border-color)"})])])]),cM("footer-segmented, footer-soft-segmented",[c(">",[cE("footer",{transition:"border-color 0.3s var(--n-bezier)"},[c("&:not(:first-child)",{borderTop:"1px solid var(--n-border-color)"})])])]),cM("embedded",`
 background-color: var(--n-color-embedded);
 `)]),insideModal(cB("card",`
 background: var(--n-color-modal);
 `,[cM("embedded",`
 background-color: var(--n-color-embedded-modal);
 `)])),insidePopover(cB("card",`
 background: var(--n-color-popover);
 `,[cM("embedded",`
 background-color: var(--n-color-embedded-popover);
 `)]))]),cardBaseProps={title:String,contentClass:String,contentStyle:[Object,String],headerClass:String,headerStyle:[Object,String],headerExtraClass:String,headerExtraStyle:[Object,String],footerClass:String,footerStyle:[Object,String],embedded:Boolean,segmented:{type:[Boolean,Object],default:!1},size:{type:String,default:"medium"},bordered:{type:Boolean,default:!0},closable:Boolean,hoverable:Boolean,role:String,onClose:[Function,Array],tag:{type:String,default:"div"}},cardBasePropKeys=keysOf(cardBaseProps),cardProps=Object.assign(Object.assign({},useTheme.props),cardBaseProps),NCard=defineComponent({name:"Card",props:cardProps,setup(e){const t=()=>{const{onClose:d}=e;d&&call(d)},{inlineThemeDisabled:o,mergedClsPrefixRef:r,mergedRtlRef:n}=useConfig(e),i=useTheme("Card","-card",style$6,cardLight,e,r),a=useRtl("Card",n,r),l=computed(()=>{const{size:d}=e,{self:{color:u,colorModal:f,colorTarget:m,textColor:v,titleTextColor:g,titleFontWeight:x,borderColor:C,actionColor:y,borderRadius:I,lineHeight:N,closeIconColor:E,closeIconColorHover:P,closeIconColorPressed:F,closeColorHover:S,closeColorPressed:$,closeBorderRadius:D,closeIconSize:j,closeSize:K,boxShadow:z,colorPopover:ee,colorEmbedded:ae,colorEmbeddedModal:se,colorEmbeddedPopover:Z,[createKey("padding",d)]:q,[createKey("fontSize",d)]:ne,[createKey("titleFontSize",d)]:he},common:{cubicBezierEaseInOut:ge}}=i.value,{top:ve,left:te,bottom:Q}=getMargin(q);return{"--n-bezier":ge,"--n-border-radius":I,"--n-color":u,"--n-color-modal":f,"--n-color-popover":ee,"--n-color-embedded":ae,"--n-color-embedded-modal":se,"--n-color-embedded-popover":Z,"--n-color-target":m,"--n-text-color":v,"--n-line-height":N,"--n-action-color":y,"--n-title-text-color":g,"--n-title-font-weight":x,"--n-close-icon-color":E,"--n-close-icon-color-hover":P,"--n-close-icon-color-pressed":F,"--n-close-color-hover":S,"--n-close-color-pressed":$,"--n-border-color":C,"--n-box-shadow":z,"--n-padding-top":ve,"--n-padding-bottom":Q,"--n-padding-left":te,"--n-font-size":ne,"--n-title-font-size":he,"--n-close-size":K,"--n-close-icon-size":j,"--n-close-border-radius":D}}),s=o?useThemeClass("card",computed(()=>e.size[0]),l,e):void 0;return{rtlEnabled:a,mergedClsPrefix:r,mergedTheme:i,handleCloseClick:t,cssVars:o?void 0:l,themeClass:s==null?void 0:s.themeClass,onRender:s==null?void 0:s.onRender}},render(){const{footerStyle:e,headerExtraClass:t,headerClass:o,contentClass:r,segmented:n,bordered:i,hoverable:a,mergedClsPrefix:l,rtlEnabled:s,onRender:d,embedded:u,tag:f,$slots:m}=this;return d==null||d(),h(f,{class:[`${l}-card`,this.themeClass,u&&`${l}-card--embedded`,{[`${l}-card--rtl`]:s,[`${l}-card--content${typeof n!="boolean"&&n.content==="soft"?"-soft":""}-segmented`]:n===!0||n!==!1&&n.content,[`${l}-card--footer${typeof n!="boolean"&&n.footer==="soft"?"-soft":""}-segmented`]:n===!0||n!==!1&&n.footer,[`${l}-card--action-segmented`]:n===!0||n!==!1&&n.action,[`${l}-card--bordered`]:i,[`${l}-card--hoverable`]:a}],style:this.cssVars,role:this.role},resolveWrappedSlot(m.cover,v=>v&&h("div",{class:`${l}-card-cover`,role:"none"},v)),resolveWrappedSlot(m.header,v=>v||this.title||this.closable?h("div",{class:[`${l}-card-header`,o],style:this.headerStyle},h("div",{class:`${l}-card-header__main`,role:"heading"},v||this.title),resolveWrappedSlot(m["header-extra"],g=>g&&h("div",{class:[`${l}-card-header__extra`,t],style:this.headerExtraStyle},g)),this.closable?h(NBaseClose,{clsPrefix:l,class:`${l}-card-header__close`,onClick:this.handleCloseClick,absolute:!0}):null):null),resolveWrappedSlot(m.default,v=>v&&h("div",{class:[`${l}-card__content`,r],style:this.contentStyle,role:"none"},v)),resolveWrappedSlot(m.footer,v=>v&&[h("div",{class:[`${l}-card__footer`,e],style:this.footerStyle,role:"none"},v)]),resolveWrappedSlot(m.action,v=>v&&h("div",{class:`${l}-card__action`,role:"none"},v)))}}),self$D=e=>({dotSize:"8px",dotColor:"rgba(255, 255, 255, .3)",dotColorActive:"rgba(255, 255, 255, 1)",dotColorFocus:"rgba(255, 255, 255, .5)",dotLineWidth:"16px",dotLineWidthActive:"24px",arrowColor:"#eee"}),carouselDark={name:"Carousel",common:commonDark,self:self$D},carouselDark$1=carouselDark,commonVariables$c={sizeSmall:"14px",sizeMedium:"16px",sizeLarge:"18px",labelPadding:"0 8px",labelFontWeight:"400"},self$C=e=>{const{baseColor:t,inputColorDisabled:o,cardColor:r,modalColor:n,popoverColor:i,textColorDisabled:a,borderColor:l,primaryColor:s,textColor2:d,fontSizeSmall:u,fontSizeMedium:f,fontSizeLarge:m,borderRadiusSmall:v,lineHeight:g}=e;return Object.assign(Object.assign({},commonVariables$c),{labelLineHeight:g,fontSizeSmall:u,fontSizeMedium:f,fontSizeLarge:m,borderRadius:v,color:t,colorChecked:s,colorDisabled:o,colorDisabledChecked:o,colorTableHeader:r,colorTableHeaderModal:n,colorTableHeaderPopover:i,checkMarkColor:t,checkMarkColorDisabled:a,checkMarkColorDisabledChecked:a,border:`1px solid ${l}`,borderDisabled:`1px solid ${l}`,borderDisabledChecked:`1px solid ${l}`,borderChecked:`1px solid ${s}`,borderFocus:`1px solid ${s}`,boxShadowFocus:`0 0 0 2px ${changeColor(s,{alpha:.3})}`,textColor:d,textColorDisabled:a})},checkboxDark={name:"Checkbox",common:commonDark,self(e){const{cardColor:t}=e,o=self$C(e);return o.color="#0000",o.checkMarkColor=t,o}},checkboxDark$1=checkboxDark,self$B=e=>{const{borderRadius:t,boxShadow2:o,popoverColor:r,textColor2:n,textColor3:i,primaryColor:a,textColorDisabled:l,dividerColor:s,hoverColor:d,fontSizeMedium:u,heightMedium:f}=e;return{menuBorderRadius:t,menuColor:r,menuBoxShadow:o,menuDividerColor:s,menuHeight:"calc(var(--n-option-height) * 6.6)",optionArrowColor:i,optionHeight:f,optionFontSize:u,optionColorHover:d,optionTextColor:n,optionTextColorActive:a,optionTextColorDisabled:l,optionCheckMarkColor:a,loadingColor:a,columnWidth:"180px"}},cascaderDark={name:"Cascader",common:commonDark,peers:{InternalSelectMenu:internalSelectMenuDark$1,InternalSelection:internalSelectionDark$1,Scrollbar:scrollbarDark$1,Checkbox:checkboxDark$1,Empty:emptyLight$1},self:self$B},cascaderDark$1=cascaderDark,codeDark={name:"Code",common:commonDark,self(e){const{textColor2:t,fontSize:o,fontWeightStrong:r,textColor3:n}=e;return{textColor:t,fontSize:o,fontWeightStrong:r,"mono-3":"#5c6370","hue-1":"#56b6c2","hue-2":"#61aeee","hue-3":"#c678dd","hue-4":"#98c379","hue-5":"#e06c75","hue-5-2":"#be5046","hue-6":"#d19a66","hue-6-2":"#e6c07b",lineNumberTextColor:n}}},codeDark$1=codeDark,self$A=e=>{const{fontWeight:t,textColor1:o,textColor2:r,textColorDisabled:n,dividerColor:i,fontSize:a}=e;return{titleFontSize:a,titleFontWeight:t,dividerColor:i,titleTextColor:o,titleTextColorDisabled:n,fontSize:a,textColor:r,arrowColor:r,arrowColorDisabled:n,itemMargin:"16px 0 0 0",titlePadding:"16px 0 0 0"}},collapseDark={name:"Collapse",common:commonDark,self:self$A},collapseDark$1=collapseDark,self$z=e=>{const{cubicBezierEaseInOut:t}=e;return{bezier:t}},collapseTransitionDark={name:"CollapseTransition",common:commonDark,self:self$z},collapseTransitionDark$1=collapseTransitionDark,configProviderProps={abstract:Boolean,bordered:{type:Boolean,default:void 0},clsPrefix:{type:String,default:defaultClsPrefix},locale:Object,dateLocale:Object,namespace:String,rtl:Array,tag:{type:String,default:"div"},hljs:Object,katex:Object,theme:Object,themeOverrides:Object,componentOptions:Object,icons:Object,breakpoints:Object,preflightStyleDisabled:Boolean,inlineThemeDisabled:{type:Boolean,default:void 0},as:{type:String,validator:()=>(warn$2("config-provider","`as` is deprecated, please use `tag` instead."),!0),default:void 0}},__unplugin_components_4=defineComponent({name:"ConfigProvider",alias:["App"],props:configProviderProps,setup(e){const t=inject(configProviderInjectionKey,null),o=computed(()=>{const{theme:g}=e;if(g===null)return;const x=t==null?void 0:t.mergedThemeRef.value;return g===void 0?x:x===void 0?g:Object.assign({},x,g)}),r=computed(()=>{const{themeOverrides:g}=e;if(g!==null){if(g===void 0)return t==null?void 0:t.mergedThemeOverridesRef.value;{const x=t==null?void 0:t.mergedThemeOverridesRef.value;return x===void 0?g:merge$1({},x,g)}}}),n=useMemo(()=>{const{namespace:g}=e;return g===void 0?t==null?void 0:t.mergedNamespaceRef.value:g}),i=useMemo(()=>{const{bordered:g}=e;return g===void 0?t==null?void 0:t.mergedBorderedRef.value:g}),a=computed(()=>{const{icons:g}=e;return g===void 0?t==null?void 0:t.mergedIconsRef.value:g}),l=computed(()=>{const{componentOptions:g}=e;return g!==void 0?g:t==null?void 0:t.mergedComponentPropsRef.value}),s=computed(()=>{const{clsPrefix:g}=e;return g!==void 0?g:t?t.mergedClsPrefixRef.value:defaultClsPrefix}),d=computed(()=>{var g;const{rtl:x}=e;if(x===void 0)return t==null?void 0:t.mergedRtlRef.value;const C={};for(const y of x)C[y.name]=markRaw(y),(g=y.peers)===null||g===void 0||g.forEach(I=>{I.name in C||(C[I.name]=markRaw(I))});return C}),u=computed(()=>e.breakpoints||(t==null?void 0:t.mergedBreakpointsRef.value)),f=e.inlineThemeDisabled||(t==null?void 0:t.inlineThemeDisabled),m=e.preflightStyleDisabled||(t==null?void 0:t.preflightStyleDisabled),v=computed(()=>{const{value:g}=o,{value:x}=r,C=x&&Object.keys(x).length!==0,y=g==null?void 0:g.name;return y?C?`${y}-${murmur2(JSON.stringify(r.value))}`:y:C?murmur2(JSON.stringify(r.value)):""});return provide(configProviderInjectionKey,{mergedThemeHashRef:v,mergedBreakpointsRef:u,mergedRtlRef:d,mergedIconsRef:a,mergedComponentPropsRef:l,mergedBorderedRef:i,mergedNamespaceRef:n,mergedClsPrefixRef:s,mergedLocaleRef:computed(()=>{const{locale:g}=e;if(g!==null)return g===void 0?t==null?void 0:t.mergedLocaleRef.value:g}),mergedDateLocaleRef:computed(()=>{const{dateLocale:g}=e;if(g!==null)return g===void 0?t==null?void 0:t.mergedDateLocaleRef.value:g}),mergedHljsRef:computed(()=>{const{hljs:g}=e;return g===void 0?t==null?void 0:t.mergedHljsRef.value:g}),mergedKatexRef:computed(()=>{const{katex:g}=e;return g===void 0?t==null?void 0:t.mergedKatexRef.value:g}),mergedThemeRef:o,mergedThemeOverridesRef:r,inlineThemeDisabled:f||!1,preflightStyleDisabled:m||!1}),{mergedClsPrefix:s,mergedBordered:i,mergedNamespace:n,mergedTheme:o,mergedThemeOverrides:r}},render(){var e,t,o,r;return this.abstract?(r=(o=this.$slots).default)===null||r===void 0?void 0:r.call(o):h(this.as||this.tag,{class:`${this.mergedClsPrefix||defaultClsPrefix}-config-provider`},(t=(e=this.$slots).default)===null||t===void 0?void 0:t.call(e))}}),popselect={name:"Popselect",common:commonDark,peers:{Popover:popoverDark$1,InternalSelectMenu:internalSelectMenuDark$1}},popselectDark=popselect;function self$y(e){const{boxShadow2:t}=e;return{menuBoxShadow:t}}const selectDark={name:"Select",common:commonDark,peers:{InternalSelection:internalSelectionDark$1,InternalSelectMenu:internalSelectMenuDark$1},self:self$y},selectDark$1=selectDark,commonVariables$b={itemPaddingSmall:"0 4px",itemMarginSmall:"0 0 0 8px",itemMarginSmallRtl:"0 8px 0 0",itemPaddingMedium:"0 4px",itemMarginMedium:"0 0 0 8px",itemMarginMediumRtl:"0 8px 0 0",itemPaddingLarge:"0 4px",itemMarginLarge:"0 0 0 8px",itemMarginLargeRtl:"0 8px 0 0",buttonIconSizeSmall:"14px",buttonIconSizeMedium:"16px",buttonIconSizeLarge:"18px",inputWidthSmall:"60px",selectWidthSmall:"unset",inputMarginSmall:"0 0 0 8px",inputMarginSmallRtl:"0 8px 0 0",selectMarginSmall:"0 0 0 8px",prefixMarginSmall:"0 8px 0 0",suffixMarginSmall:"0 0 0 8px",inputWidthMedium:"60px",selectWidthMedium:"unset",inputMarginMedium:"0 0 0 8px",inputMarginMediumRtl:"0 8px 0 0",selectMarginMedium:"0 0 0 8px",prefixMarginMedium:"0 8px 0 0",suffixMarginMedium:"0 0 0 8px",inputWidthLarge:"60px",selectWidthLarge:"unset",inputMarginLarge:"0 0 0 8px",inputMarginLargeRtl:"0 8px 0 0",selectMarginLarge:"0 0 0 8px",prefixMarginLarge:"0 8px 0 0",suffixMarginLarge:"0 0 0 8px"},self$x=e=>{const{textColor2:t,primaryColor:o,primaryColorHover:r,primaryColorPressed:n,inputColorDisabled:i,textColorDisabled:a,borderColor:l,borderRadius:s,fontSizeTiny:d,fontSizeSmall:u,fontSizeMedium:f,heightTiny:m,heightSmall:v,heightMedium:g}=e;return Object.assign(Object.assign({},commonVariables$b),{buttonColor:"#0000",buttonColorHover:"#0000",buttonColorPressed:"#0000",buttonBorder:`1px solid ${l}`,buttonBorderHover:`1px solid ${l}`,buttonBorderPressed:`1px solid ${l}`,buttonIconColor:t,buttonIconColorHover:t,buttonIconColorPressed:t,itemTextColor:t,itemTextColorHover:r,itemTextColorPressed:n,itemTextColorActive:o,itemTextColorDisabled:a,itemColor:"#0000",itemColorHover:"#0000",itemColorPressed:"#0000",itemColorActive:"#0000",itemColorActiveHover:"#0000",itemColorDisabled:i,itemBorder:"1px solid #0000",itemBorderHover:"1px solid #0000",itemBorderPressed:"1px solid #0000",itemBorderActive:`1px solid ${o}`,itemBorderDisabled:`1px solid ${l}`,itemBorderRadius:s,itemSizeSmall:m,itemSizeMedium:v,itemSizeLarge:g,itemFontSizeSmall:d,itemFontSizeMedium:u,itemFontSizeLarge:f,jumperFontSizeSmall:d,jumperFontSizeMedium:u,jumperFontSizeLarge:f,jumperTextColor:t,jumperTextColorDisabled:a})},paginationDark={name:"Pagination",common:commonDark,peers:{Select:selectDark$1,Input:inputDark$1,Popselect:popselectDark},self(e){const{primaryColor:t,opacity3:o}=e,r=changeColor(t,{alpha:Number(o)}),n=self$x(e);return n.itemBorderActive=`1px solid ${r}`,n.itemBorderDisabled="1px solid #0000",n}},paginationDark$1=paginationDark,commonVars$8={padding:"8px 14px"},tooltipDark={name:"Tooltip",common:commonDark,peers:{Popover:popoverDark$1},self(e){const{borderRadius:t,boxShadow2:o,popoverColor:r,textColor2:n}=e;return Object.assign(Object.assign({},commonVars$8),{borderRadius:t,boxShadow:o,color:r,textColor:n})}},tooltipDark$1=tooltipDark,ellipsisDark={name:"Ellipsis",common:commonDark,peers:{Tooltip:tooltipDark$1}},ellipsisDark$1=ellipsisDark,commonVariables$a={radioSizeSmall:"14px",radioSizeMedium:"16px",radioSizeLarge:"18px",labelPadding:"0 8px",labelFontWeight:"400"},radioDark={name:"Radio",common:commonDark,self(e){const{borderColor:t,primaryColor:o,baseColor:r,textColorDisabled:n,inputColorDisabled:i,textColor2:a,opacityDisabled:l,borderRadius:s,fontSizeSmall:d,fontSizeMedium:u,fontSizeLarge:f,heightSmall:m,heightMedium:v,heightLarge:g,lineHeight:x}=e;return Object.assign(Object.assign({},commonVariables$a),{labelLineHeight:x,buttonHeightSmall:m,buttonHeightMedium:v,buttonHeightLarge:g,fontSizeSmall:d,fontSizeMedium:u,fontSizeLarge:f,boxShadow:`inset 0 0 0 1px ${t}`,boxShadowActive:`inset 0 0 0 1px ${o}`,boxShadowFocus:`inset 0 0 0 1px ${o}, 0 0 0 2px ${changeColor(o,{alpha:.3})}`,boxShadowHover:`inset 0 0 0 1px ${o}`,boxShadowDisabled:`inset 0 0 0 1px ${t}`,color:"#0000",colorDisabled:i,colorActive:"#0000",textColor:a,textColorDisabled:n,dotColorActive:o,dotColorDisabled:t,buttonBorderColor:t,buttonBorderColorActive:o,buttonBorderColorHover:o,buttonColor:"#0000",buttonColorActive:o,buttonTextColor:a,buttonTextColorActive:r,buttonTextColorHover:o,opacityDisabled:l,buttonBoxShadowFocus:`inset 0 0 0 1px ${o}, 0 0 0 2px ${changeColor(o,{alpha:.3})}`,buttonBoxShadowHover:`inset 0 0 0 1px ${o}`,buttonBoxShadow:"inset 0 0 0 1px #0000",buttonBorderRadius:s})}},radioDark$1=radioDark,commonVariables$9={padding:"4px 0",optionIconSizeSmall:"14px",optionIconSizeMedium:"16px",optionIconSizeLarge:"16px",optionIconSizeHuge:"18px",optionSuffixWidthSmall:"14px",optionSuffixWidthMedium:"14px",optionSuffixWidthLarge:"16px",optionSuffixWidthHuge:"16px",optionIconSuffixWidthSmall:"32px",optionIconSuffixWidthMedium:"32px",optionIconSuffixWidthLarge:"36px",optionIconSuffixWidthHuge:"36px",optionPrefixWidthSmall:"14px",optionPrefixWidthMedium:"14px",optionPrefixWidthLarge:"16px",optionPrefixWidthHuge:"16px",optionIconPrefixWidthSmall:"36px",optionIconPrefixWidthMedium:"36px",optionIconPrefixWidthLarge:"40px",optionIconPrefixWidthHuge:"40px"},self$w=e=>{const{primaryColor:t,textColor2:o,dividerColor:r,hoverColor:n,popoverColor:i,invertedColor:a,borderRadius:l,fontSizeSmall:s,fontSizeMedium:d,fontSizeLarge:u,fontSizeHuge:f,heightSmall:m,heightMedium:v,heightLarge:g,heightHuge:x,textColor3:C,opacityDisabled:y}=e;return Object.assign(Object.assign({},commonVariables$9),{optionHeightSmall:m,optionHeightMedium:v,optionHeightLarge:g,optionHeightHuge:x,borderRadius:l,fontSizeSmall:s,fontSizeMedium:d,fontSizeLarge:u,fontSizeHuge:f,optionTextColor:o,optionTextColorHover:o,optionTextColorActive:t,optionTextColorChildActive:t,color:i,dividerColor:r,suffixColor:o,prefixColor:o,optionColorHover:n,optionColorActive:changeColor(t,{alpha:.1}),groupHeaderTextColor:C,optionTextColorInverted:"#BBB",optionTextColorHoverInverted:"#FFF",optionTextColorActiveInverted:"#FFF",optionTextColorChildActiveInverted:"#FFF",colorInverted:a,dividerColorInverted:"#BBB",suffixColorInverted:"#BBB",prefixColorInverted:"#BBB",optionColorHoverInverted:t,optionColorActiveInverted:t,groupHeaderTextColorInverted:"#AAA",optionOpacityDisabled:y})},dropdownDark={name:"Dropdown",common:commonDark,peers:{Popover:popoverDark$1},self(e){const{primaryColorSuppl:t,primaryColor:o,popoverColor:r}=e,n=self$w(e);return n.colorInverted=r,n.optionColorActive=changeColor(o,{alpha:.15}),n.optionColorActiveInverted=t,n.optionColorHoverInverted=t,n}},dropdownDark$1=dropdownDark,commonVariables$8={thPaddingSmall:"8px",thPaddingMedium:"12px",thPaddingLarge:"12px",tdPaddingSmall:"8px",tdPaddingMedium:"12px",tdPaddingLarge:"12px",sorterSize:"15px",resizableContainerSize:"8px",resizableSize:"2px",filterSize:"15px",paginationMargin:"12px 0 0 0",emptyPadding:"48px 0",actionPadding:"8px 12px",actionButtonMargin:"0 8px 0 0"},self$v=e=>{const{cardColor:t,modalColor:o,popoverColor:r,textColor2:n,textColor1:i,tableHeaderColor:a,tableColorHover:l,iconColor:s,primaryColor:d,fontWeightStrong:u,borderRadius:f,lineHeight:m,fontSizeSmall:v,fontSizeMedium:g,fontSizeLarge:x,dividerColor:C,heightSmall:y,opacityDisabled:I,tableColorStriped:N}=e;return Object.assign(Object.assign({},commonVariables$8),{actionDividerColor:C,lineHeight:m,borderRadius:f,fontSizeSmall:v,fontSizeMedium:g,fontSizeLarge:x,borderColor:composite(t,C),tdColorHover:composite(t,l),tdColorStriped:composite(t,N),thColor:composite(t,a),thColorHover:composite(composite(t,a),l),tdColor:t,tdTextColor:n,thTextColor:i,thFontWeight:u,thButtonColorHover:l,thIconColor:s,thIconColorActive:d,borderColorModal:composite(o,C),tdColorHoverModal:composite(o,l),tdColorStripedModal:composite(o,N),thColorModal:composite(o,a),thColorHoverModal:composite(composite(o,a),l),tdColorModal:o,borderColorPopover:composite(r,C),tdColorHoverPopover:composite(r,l),tdColorStripedPopover:composite(r,N),thColorPopover:composite(r,a),thColorHoverPopover:composite(composite(r,a),l),tdColorPopover:r,boxShadowBefore:"inset -12px 0 8px -12px rgba(0, 0, 0, .18)",boxShadowAfter:"inset 12px 0 8px -12px rgba(0, 0, 0, .18)",loadingColor:d,loadingSize:y,opacityLoading:I})},dataTableDark={name:"DataTable",common:commonDark,peers:{Button:buttonDark$1,Checkbox:checkboxDark$1,Radio:radioDark$1,Pagination:paginationDark$1,Scrollbar:scrollbarDark$1,Empty:emptyDark$1,Popover:popoverDark$1,Ellipsis:ellipsisDark$1,Dropdown:dropdownDark$1},self(e){const t=self$v(e);return t.boxShadowAfter="inset 12px 0 8px -12px rgba(0, 0, 0, .36)",t.boxShadowBefore="inset -12px 0 8px -12px rgba(0, 0, 0, .36)",t}},dataTableDark$1=dataTableDark,self$u=e=>{const{textColorBase:t,opacity1:o,opacity2:r,opacity3:n,opacity4:i,opacity5:a}=e;return{color:t,opacity1Depth:o,opacity2Depth:r,opacity3Depth:n,opacity4Depth:i,opacity5Depth:a}},iconDark$1={name:"Icon",common:commonDark,self:self$u},iconDark$2=iconDark$1,commonVars$7={itemFontSize:"12px",itemHeight:"36px",itemWidth:"52px",panelActionPadding:"8px 0"},self$t=e=>{const{popoverColor:t,textColor2:o,primaryColor:r,hoverColor:n,dividerColor:i,opacityDisabled:a,boxShadow2:l,borderRadius:s,iconColor:d,iconColorDisabled:u}=e;return Object.assign(Object.assign({},commonVars$7),{panelColor:t,panelBoxShadow:l,panelDividerColor:i,itemTextColor:o,itemTextColorActive:r,itemColorHover:n,itemOpacityDisabled:a,itemBorderRadius:s,borderRadius:s,iconColor:d,iconColorDisabled:u})},timePickerDark={name:"TimePicker",common:commonDark,peers:{Scrollbar:scrollbarDark$1,Button:buttonDark$1,Input:inputDark$1},self:self$t},timePickerDark$1=timePickerDark,commonVars$6={itemSize:"24px",itemCellWidth:"38px",itemCellHeight:"32px",scrollItemWidth:"80px",scrollItemHeight:"40px",panelExtraFooterPadding:"8px 12px",panelActionPadding:"8px 12px",calendarTitlePadding:"0",calendarTitleHeight:"28px",arrowSize:"14px",panelHeaderPadding:"8px 12px",calendarDaysHeight:"32px",calendarTitleGridTempateColumns:"28px 28px 1fr 28px 28px",calendarLeftPaddingDate:"6px 12px 4px 12px",calendarLeftPaddingDatetime:"4px 12px",calendarLeftPaddingDaterange:"6px 12px 4px 12px",calendarLeftPaddingDatetimerange:"4px 12px",calendarLeftPaddingMonth:"0",calendarLeftPaddingYear:"0",calendarLeftPaddingQuarter:"0",calendarLeftPaddingMonthrange:"0",calendarLeftPaddingQuarterrange:"0",calendarLeftPaddingYearrange:"0",calendarRightPaddingDate:"6px 12px 4px 12px",calendarRightPaddingDatetime:"4px 12px",calendarRightPaddingDaterange:"6px 12px 4px 12px",calendarRightPaddingDatetimerange:"4px 12px",calendarRightPaddingMonth:"0",calendarRightPaddingYear:"0",calendarRightPaddingQuarter:"0",calendarRightPaddingMonthrange:"0",calendarRightPaddingQuarterrange:"0",calendarRightPaddingYearrange:"0"},self$s=e=>{const{hoverColor:t,fontSize:o,textColor2:r,textColorDisabled:n,popoverColor:i,primaryColor:a,borderRadiusSmall:l,iconColor:s,iconColorDisabled:d,textColor1:u,dividerColor:f,boxShadow2:m,borderRadius:v,fontWeightStrong:g}=e;return Object.assign(Object.assign({},commonVars$6),{itemFontSize:o,calendarDaysFontSize:o,calendarTitleFontSize:o,itemTextColor:r,itemTextColorDisabled:n,itemTextColorActive:i,itemTextColorCurrent:a,itemColorIncluded:changeColor(a,{alpha:.1}),itemColorHover:t,itemColorDisabled:t,itemColorActive:a,itemBorderRadius:l,panelColor:i,panelTextColor:r,arrowColor:s,calendarTitleTextColor:u,calendarTitleColorHover:t,calendarDaysTextColor:r,panelHeaderDividerColor:f,calendarDaysDividerColor:f,calendarDividerColor:f,panelActionDividerColor:f,panelBoxShadow:m,panelBorderRadius:v,calendarTitleFontWeight:g,scrollItemBorderRadius:v,iconColor:s,iconColorDisabled:d})},datePickerDark={name:"DatePicker",common:commonDark,peers:{Input:inputDark$1,Button:buttonDark$1,TimePicker:timePickerDark$1,Scrollbar:scrollbarDark$1},self(e){const{popoverColor:t,hoverColor:o,primaryColor:r}=e,n=self$s(e);return n.itemColorDisabled=composite(t,o),n.itemColorIncluded=changeColor(r,{alpha:.15}),n.itemColorHover=composite(t,o),n}},datePickerDark$1=datePickerDark;var commonjsGlobal=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};function getDefaultExportFromCjs(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}const commonVariables$7={thPaddingBorderedSmall:"8px 12px",thPaddingBorderedMedium:"12px 16px",thPaddingBorderedLarge:"16px 24px",thPaddingSmall:"0",thPaddingMedium:"0",thPaddingLarge:"0",tdPaddingBorderedSmall:"8px 12px",tdPaddingBorderedMedium:"12px 16px",tdPaddingBorderedLarge:"16px 24px",tdPaddingSmall:"0 0 8px 0",tdPaddingMedium:"0 0 12px 0",tdPaddingLarge:"0 0 16px 0"},self$r=e=>{const{tableHeaderColor:t,textColor2:o,textColor1:r,cardColor:n,modalColor:i,popoverColor:a,dividerColor:l,borderRadius:s,fontWeightStrong:d,lineHeight:u,fontSizeSmall:f,fontSizeMedium:m,fontSizeLarge:v}=e;return Object.assign(Object.assign({},commonVariables$7),{lineHeight:u,fontSizeSmall:f,fontSizeMedium:m,fontSizeLarge:v,titleTextColor:r,thColor:composite(n,t),thColorModal:composite(i,t),thColorPopover:composite(a,t),thTextColor:r,thFontWeight:d,tdTextColor:o,tdColor:n,tdColorModal:i,tdColorPopover:a,borderColor:composite(n,l),borderColorModal:composite(i,l),borderColorPopover:composite(a,l),borderRadius:s})},descriptionsDark={name:"Descriptions",common:commonDark,self:self$r},descriptionsDark$1=descriptionsDark,commonVars$5={titleFontSize:"18px",padding:"16px 28px 20px 28px",iconSize:"28px",actionSpace:"12px",contentMargin:"8px 0 16px 0",iconMargin:"0 4px 0 0",iconMarginIconTop:"4px 0 8px 0",closeSize:"22px",closeIconSize:"18px",closeMargin:"20px 26px 0 0",closeMarginIconTop:"10px 16px 0 0"},self$q=e=>{const{textColor1:t,textColor2:o,modalColor:r,closeIconColor:n,closeIconColorHover:i,closeIconColorPressed:a,closeColorHover:l,closeColorPressed:s,infoColor:d,successColor:u,warningColor:f,errorColor:m,primaryColor:v,dividerColor:g,borderRadius:x,fontWeightStrong:C,lineHeight:y,fontSize:I}=e;return Object.assign(Object.assign({},commonVars$5),{fontSize:I,lineHeight:y,border:`1px solid ${g}`,titleTextColor:t,textColor:o,color:r,closeColorHover:l,closeColorPressed:s,closeIconColor:n,closeIconColorHover:i,closeIconColorPressed:a,closeBorderRadius:x,iconColor:v,iconColorInfo:d,iconColorSuccess:u,iconColorWarning:f,iconColorError:m,borderRadius:x,titleFontWeight:C})},dialogLight={name:"Dialog",common:commonLight,peers:{Button:buttonLight},self:self$q},dialogDark={name:"Dialog",common:commonDark,peers:{Button:buttonDark$1},self:self$q},dialogDark$1=dialogDark,dialogProps={icon:Function,type:{type:String,default:"default"},title:[String,Function],closable:{type:Boolean,default:!0},negativeText:String,positiveText:String,positiveButtonProps:Object,negativeButtonProps:Object,content:[String,Function],action:Function,showIcon:{type:Boolean,default:!0},loading:Boolean,bordered:Boolean,iconPlacement:String,onPositiveClick:Function,onNegativeClick:Function,onClose:Function},dialogPropKeys=keysOf(dialogProps),style$5=c([cB("dialog",`
 word-break: break-word;
 line-height: var(--n-line-height);
 position: relative;
 background: var(--n-color);
 color: var(--n-text-color);
 box-sizing: border-box;
 margin: auto;
 border-radius: var(--n-border-radius);
 padding: var(--n-padding);
 transition: 
 border-color .3s var(--n-bezier),
 background-color .3s var(--n-bezier),
 color .3s var(--n-bezier);
 `,[cE("icon",{color:"var(--n-icon-color)"}),cM("bordered",{border:"var(--n-border)"}),cM("icon-top",[cE("close",{margin:"var(--n-close-margin)"}),cE("icon",{margin:"var(--n-icon-margin)"}),cE("content",{textAlign:"center"}),cE("title",{justifyContent:"center"}),cE("action",{justifyContent:"center"})]),cM("icon-left",[cE("icon",{margin:"var(--n-icon-margin)"}),cM("closable",[cE("title",`
 padding-right: calc(var(--n-close-size) + 6px);
 `)])]),cE("close",`
 position: absolute;
 right: 0;
 top: 0;
 margin: var(--n-close-margin);
 transition:
 background-color .3s var(--n-bezier),
 color .3s var(--n-bezier);
 z-index: 1;
 `),cE("content",`
 font-size: var(--n-font-size);
 margin: var(--n-content-margin);
 position: relative;
 word-break: break-word;
 `,[cM("last","margin-bottom: 0;")]),cE("action",`
 display: flex;
 justify-content: flex-end;
 `,[c("> *:not(:last-child)",{marginRight:"var(--n-action-space)"})]),cE("icon",{fontSize:"var(--n-icon-size)",transition:"color .3s var(--n-bezier)"}),cE("title",`
 transition: color .3s var(--n-bezier);
 display: flex;
 align-items: center;
 font-size: var(--n-title-font-size);
 font-weight: var(--n-title-font-weight);
 color: var(--n-title-text-color);
 `),cB("dialog-icon-container",{display:"flex",justifyContent:"center"})]),insideModal(cB("dialog",`
 width: 446px;
 max-width: calc(100vw - 32px);
 `)),cB("dialog",[asModal(`
 width: 446px;
 max-width: calc(100vw - 32px);
 `)])]),iconRenderMap$1={default:()=>h(InfoIcon,null),info:()=>h(InfoIcon,null),success:()=>h(SuccessIcon,null),warning:()=>h(WarningIcon,null),error:()=>h(ErrorIcon,null)},NDialog=defineComponent({name:"Dialog",alias:["NimbusConfirmCard","Confirm"],props:Object.assign(Object.assign({},useTheme.props),dialogProps),setup(e){const{mergedComponentPropsRef:t,mergedClsPrefixRef:o,inlineThemeDisabled:r}=useConfig(e),n=computed(()=>{var f,m;const{iconPlacement:v}=e;return v||((m=(f=t==null?void 0:t.value)===null||f===void 0?void 0:f.Dialog)===null||m===void 0?void 0:m.iconPlacement)||"left"});function i(f){const{onPositiveClick:m}=e;m&&m(f)}function a(f){const{onNegativeClick:m}=e;m&&m(f)}function l(){const{onClose:f}=e;f&&f()}const s=useTheme("Dialog","-dialog",style$5,dialogLight,e,o),d=computed(()=>{const{type:f}=e,m=n.value,{common:{cubicBezierEaseInOut:v},self:{fontSize:g,lineHeight:x,border:C,titleTextColor:y,textColor:I,color:N,closeBorderRadius:E,closeColorHover:P,closeColorPressed:F,closeIconColor:S,closeIconColorHover:$,closeIconColorPressed:D,closeIconSize:j,borderRadius:K,titleFontWeight:z,titleFontSize:ee,padding:ae,iconSize:se,actionSpace:Z,contentMargin:q,closeSize:ne,[m==="top"?"iconMarginIconTop":"iconMargin"]:he,[m==="top"?"closeMarginIconTop":"closeMargin"]:ge,[createKey("iconColor",f)]:ve}}=s.value;return{"--n-font-size":g,"--n-icon-color":ve,"--n-bezier":v,"--n-close-margin":ge,"--n-icon-margin":he,"--n-icon-size":se,"--n-close-size":ne,"--n-close-icon-size":j,"--n-close-border-radius":E,"--n-close-color-hover":P,"--n-close-color-pressed":F,"--n-close-icon-color":S,"--n-close-icon-color-hover":$,"--n-close-icon-color-pressed":D,"--n-color":N,"--n-text-color":I,"--n-border-radius":K,"--n-padding":ae,"--n-line-height":x,"--n-border":C,"--n-content-margin":q,"--n-title-font-size":ee,"--n-title-font-weight":z,"--n-title-text-color":y,"--n-action-space":Z}}),u=r?useThemeClass("dialog",computed(()=>`${e.type[0]}${n.value[0]}`),d,e):void 0;return{mergedClsPrefix:o,mergedIconPlacement:n,mergedTheme:s,handlePositiveClick:i,handleNegativeClick:a,handleCloseClick:l,cssVars:r?void 0:d,themeClass:u==null?void 0:u.themeClass,onRender:u==null?void 0:u.onRender}},render(){var e;const{bordered:t,mergedIconPlacement:o,cssVars:r,closable:n,showIcon:i,title:a,content:l,action:s,negativeText:d,positiveText:u,positiveButtonProps:f,negativeButtonProps:m,handlePositiveClick:v,handleNegativeClick:g,mergedTheme:x,loading:C,type:y,mergedClsPrefix:I}=this;(e=this.onRender)===null||e===void 0||e.call(this);const N=i?h(NBaseIcon,{clsPrefix:I,class:`${I}-dialog__icon`},{default:()=>resolveWrappedSlot(this.$slots.icon,P=>P||(this.icon?render$1(this.icon):iconRenderMap$1[this.type]()))}):null,E=resolveWrappedSlot(this.$slots.action,P=>P||u||d||s?h("div",{class:`${I}-dialog__action`},P||(s?[render$1(s)]:[this.negativeText&&h(NButton,Object.assign({theme:x.peers.Button,themeOverrides:x.peerOverrides.Button,ghost:!0,size:"small",onClick:g},m),{default:()=>render$1(this.negativeText)}),this.positiveText&&h(NButton,Object.assign({theme:x.peers.Button,themeOverrides:x.peerOverrides.Button,size:"small",type:y==="default"?"primary":y,disabled:C,loading:C,onClick:v},f),{default:()=>render$1(this.positiveText)})])):null);return h("div",{class:[`${I}-dialog`,this.themeClass,this.closable&&`${I}-dialog--closable`,`${I}-dialog--icon-${o}`,t&&`${I}-dialog--bordered`],style:r,role:"dialog"},n?resolveWrappedSlot(this.$slots.close,P=>P?h("div",{class:`${I}-dialog__close`},P):h(NBaseClose,{clsPrefix:I,class:`${I}-dialog__close`,onClick:this.handleCloseClick})):null,i&&o==="top"?h("div",{class:`${I}-dialog-icon-container`},N):null,h("div",{class:`${I}-dialog__title`},i&&o==="left"?N:null,resolveSlot(this.$slots.header,()=>[render$1(a)])),h("div",{class:[`${I}-dialog__content`,E?"":`${I}-dialog__content--last`]},resolveSlot(this.$slots.default,()=>[render$1(l)])),E)}}),dialogProviderInjectionKey="n-dialog-provider",dialogApiInjectionKey="n-dialog-api",dialogReactiveListInjectionKey="n-dialog-reactive-list",self$p=e=>{const{modalColor:t,textColor2:o,boxShadow3:r}=e;return{color:t,textColor:o,boxShadow:r}},modalLight={name:"Modal",common:commonLight,peers:{Scrollbar:scrollbarLight,Dialog:dialogLight,Card:cardLight},self:self$p},modalDark={name:"Modal",common:commonDark,peers:{Scrollbar:scrollbarDark$1,Dialog:dialogDark$1,Card:cardDark$1},self:self$p},modalDark$1=modalDark,presetProps=Object.assign(Object.assign({},cardBaseProps),dialogProps),presetPropsKeys=keysOf(presetProps),NModalBodyWrapper=defineComponent({name:"ModalBody",inheritAttrs:!1,props:Object.assign(Object.assign({show:{type:Boolean,required:!0},preset:String,displayDirective:{type:String,required:!0},trapFocus:{type:Boolean,default:!0},autoFocus:{type:Boolean,default:!0},blockScroll:Boolean},presetProps),{renderMask:Function,onClickoutside:Function,onBeforeLeave:{type:Function,required:!0},onAfterLeave:{type:Function,required:!0},onPositiveClick:{type:Function,required:!0},onNegativeClick:{type:Function,required:!0},onClose:{type:Function,required:!0},onAfterEnter:Function,onEsc:Function}),setup(e){const t=ref(null),o=ref(null),r=ref(e.show),n=ref(null),i=ref(null);watch(toRef(e,"show"),C=>{C&&(r.value=!0)}),useLockHtmlScroll(computed(()=>e.blockScroll&&r.value));const a=inject(modalInjectionKey);function l(){if(a.transformOriginRef.value==="center")return"";const{value:C}=n,{value:y}=i;if(C===null||y===null)return"";if(o.value){const I=o.value.containerScrollTop;return`${C}px ${y+I}px`}return""}function s(C){if(a.transformOriginRef.value==="center")return;const y=a.getMousePosition();if(!y||!o.value)return;const I=o.value.containerScrollTop,{offsetLeft:N,offsetTop:E}=C;if(y){const P=y.y,F=y.x;n.value=-(N-F),i.value=-(E-P-I)}C.style.transformOrigin=l()}function d(C){nextTick(()=>{s(C)})}function u(C){C.style.transformOrigin=l(),e.onBeforeLeave()}function f(){r.value=!1,n.value=null,i.value=null,e.onAfterLeave()}function m(){const{onClose:C}=e;C&&C()}function v(){e.onNegativeClick()}function g(){e.onPositiveClick()}const x=ref(null);return watch(x,C=>{C&&nextTick(()=>{const y=C.el;y&&t.value!==y&&(t.value=y)})}),provide(modalBodyInjectionKey,t),provide(drawerBodyInjectionKey,null),provide(popoverBodyInjectionKey,null),{mergedTheme:a.mergedThemeRef,appear:a.appearRef,isMounted:a.isMountedRef,mergedClsPrefix:a.mergedClsPrefixRef,bodyRef:t,scrollbarRef:o,displayed:r,childNodeRef:x,handlePositiveClick:g,handleNegativeClick:v,handleCloseClick:m,handleAfterLeave:f,handleBeforeLeave:u,handleEnter:d}},render(){const{$slots:e,$attrs:t,handleEnter:o,handleAfterLeave:r,handleBeforeLeave:n,preset:i,mergedClsPrefix:a}=this;let l=null;if(!i){if(l=getFirstSlotVNode(e),!l){warn$2("modal","default slot is empty");return}l=cloneVNode(l),l.props=mergeProps({class:`${a}-modal`},t,l.props||{})}return this.displayDirective==="show"||this.displayed||this.show?withDirectives(h("div",{role:"none",class:`${a}-modal-body-wrapper`},h(NScrollbar,{ref:"scrollbarRef",theme:this.mergedTheme.peers.Scrollbar,themeOverrides:this.mergedTheme.peerOverrides.Scrollbar,contentClass:`${a}-modal-scroll-content`},{default:()=>{var s;return[(s=this.renderMask)===null||s===void 0?void 0:s.call(this),h(FocusTrap,{disabled:!this.trapFocus,active:this.show,onEsc:this.onEsc,autoFocus:this.autoFocus},{default:()=>{var d;return h(Transition,{name:"fade-in-scale-up-transition",appear:(d=this.appear)!==null&&d!==void 0?d:this.isMounted,onEnter:o,onAfterEnter:this.onAfterEnter,onAfterLeave:r,onBeforeLeave:n},{default:()=>{const u=[[vShow,this.show]],{onClickoutside:f}=this;return f&&u.push([clickoutside$1,this.onClickoutside,void 0,{capture:!0}]),withDirectives(this.preset==="confirm"||this.preset==="dialog"?h(NDialog,Object.assign({},this.$attrs,{class:[`${a}-modal`,this.$attrs.class],ref:"bodyRef",theme:this.mergedTheme.peers.Dialog,themeOverrides:this.mergedTheme.peerOverrides.Dialog},keep(this.$props,dialogPropKeys),{"aria-modal":"true"}),e):this.preset==="card"?h(NCard,Object.assign({},this.$attrs,{ref:"bodyRef",class:[`${a}-modal`,this.$attrs.class],theme:this.mergedTheme.peers.Card,themeOverrides:this.mergedTheme.peerOverrides.Card},keep(this.$props,cardBasePropKeys),{"aria-modal":"true",role:"dialog"}),e):this.childNodeRef=l,u)}})}})]}})),[[vShow,this.displayDirective==="if"||this.displayed||this.show]]):null}}),style$4=c([cB("modal-container",`
 position: fixed;
 left: 0;
 top: 0;
 height: 0;
 width: 0;
 display: flex;
 `),cB("modal-mask",`
 position: fixed;
 left: 0;
 right: 0;
 top: 0;
 bottom: 0;
 background-color: rgba(0, 0, 0, .4);
 `,[fadeInTransition({enterDuration:".25s",leaveDuration:".25s",enterCubicBezier:"var(--n-bezier-ease-out)",leaveCubicBezier:"var(--n-bezier-ease-out)"})]),cB("modal-body-wrapper",`
 position: fixed;
 left: 0;
 right: 0;
 top: 0;
 bottom: 0;
 overflow: visible;
 `,[cB("modal-scroll-content",`
 min-height: 100%;
 display: flex;
 position: relative;
 `)]),cB("modal",`
 position: relative;
 align-self: center;
 color: var(--n-text-color);
 margin: auto;
 box-shadow: var(--n-box-shadow);
 `,[fadeInScaleUpTransition({duration:".25s",enterScale:".5"})])]),modalProps=Object.assign(Object.assign(Object.assign(Object.assign({},useTheme.props),{show:Boolean,unstableShowMask:{type:Boolean,default:!0},maskClosable:{type:Boolean,default:!0},preset:String,to:[String,Object],displayDirective:{type:String,default:"if"},transformOrigin:{type:String,default:"mouse"},zIndex:Number,autoFocus:{type:Boolean,default:!0},trapFocus:{type:Boolean,default:!0},closeOnEsc:{type:Boolean,default:!0},blockScroll:{type:Boolean,default:!0}}),presetProps),{onEsc:Function,"onUpdate:show":[Function,Array],onUpdateShow:[Function,Array],onAfterEnter:Function,onBeforeLeave:Function,onAfterLeave:Function,onClose:Function,onPositiveClick:Function,onNegativeClick:Function,onMaskClick:Function,internalDialog:Boolean,internalAppear:{type:Boolean,default:void 0},overlayStyle:[String,Object],onBeforeHide:Function,onAfterHide:Function,onHide:Function}),NModal=defineComponent({name:"Modal",inheritAttrs:!1,props:modalProps,setup(e){const t=ref(null),{mergedClsPrefixRef:o,namespaceRef:r,inlineThemeDisabled:n}=useConfig(e),i=useTheme("Modal","-modal",style$4,modalLight,e,o),a=useClicked(64),l=useClickPosition(),s=isMounted(),d=e.internalDialog?inject(dialogProviderInjectionKey,null):null,u=useIsComposing();function f(P){const{onUpdateShow:F,"onUpdate:show":S,onHide:$}=e;F&&call(F,P),S&&call(S,P),$&&!P&&$(P)}function m(){const{onClose:P}=e;P?Promise.resolve(P()).then(F=>{F!==!1&&f(!1)}):f(!1)}function v(){const{onPositiveClick:P}=e;P?Promise.resolve(P()).then(F=>{F!==!1&&f(!1)}):f(!1)}function g(){const{onNegativeClick:P}=e;P?Promise.resolve(P()).then(F=>{F!==!1&&f(!1)}):f(!1)}function x(){const{onBeforeLeave:P,onBeforeHide:F}=e;P&&call(P),F&&F()}function C(){const{onAfterLeave:P,onAfterHide:F}=e;P&&call(P),F&&F()}function y(P){var F;const{onMaskClick:S}=e;S&&S(P),e.maskClosable&&!((F=t.value)===null||F===void 0)&&F.contains(getPreciseEventTarget(P))&&f(!1)}function I(P){var F;(F=e.onEsc)===null||F===void 0||F.call(e),e.show&&e.closeOnEsc&&eventEffectNotPerformed(P)&&!u.value&&f(!1)}provide(modalInjectionKey,{getMousePosition:()=>{if(d){const{clickedRef:P,clickPositionRef:F}=d;if(P.value&&F.value)return F.value}return a.value?l.value:null},mergedClsPrefixRef:o,mergedThemeRef:i,isMountedRef:s,appearRef:toRef(e,"internalAppear"),transformOriginRef:toRef(e,"transformOrigin")});const N=computed(()=>{const{common:{cubicBezierEaseOut:P},self:{boxShadow:F,color:S,textColor:$}}=i.value;return{"--n-bezier-ease-out":P,"--n-box-shadow":F,"--n-color":S,"--n-text-color":$}}),E=n?useThemeClass("theme-class",void 0,N,e):void 0;return{mergedClsPrefix:o,namespace:r,isMounted:s,containerRef:t,presetProps:computed(()=>keep(e,presetPropsKeys)),handleEsc:I,handleAfterLeave:C,handleClickoutside:y,handleBeforeLeave:x,doUpdateShow:f,handleNegativeClick:g,handlePositiveClick:v,handleCloseClick:m,cssVars:n?void 0:N,themeClass:E==null?void 0:E.themeClass,onRender:E==null?void 0:E.onRender}},render(){const{mergedClsPrefix:e}=this;return h(LazyTeleport,{to:this.to,show:this.show},{default:()=>{var t;(t=this.onRender)===null||t===void 0||t.call(this);const{unstableShowMask:o}=this;return withDirectives(h("div",{role:"none",ref:"containerRef",class:[`${e}-modal-container`,this.themeClass,this.namespace],style:this.cssVars},h(NModalBodyWrapper,Object.assign({style:this.overlayStyle},this.$attrs,{ref:"bodyWrapper",displayDirective:this.displayDirective,show:this.show,preset:this.preset,autoFocus:this.autoFocus,trapFocus:this.trapFocus,blockScroll:this.blockScroll},this.presetProps,{onEsc:this.handleEsc,onClose:this.handleCloseClick,onNegativeClick:this.handleNegativeClick,onPositiveClick:this.handlePositiveClick,onBeforeLeave:this.handleBeforeLeave,onAfterEnter:this.onAfterEnter,onAfterLeave:this.handleAfterLeave,onClickoutside:o?void 0:this.handleClickoutside,renderMask:o?()=>{var r;return h(Transition,{name:"fade-in-transition",key:"mask",appear:(r=this.internalAppear)!==null&&r!==void 0?r:this.isMounted},{default:()=>this.show?h("div",{"aria-hidden":!0,ref:"containerRef",class:`${e}-modal-mask`,onClick:this.handleClickoutside}):null})}:void 0}),this.$slots)),[[zindexable$1,{zIndex:this.zIndex,enabled:this.show}]])}})}}),exposedDialogEnvProps=Object.assign(Object.assign({},dialogProps),{onAfterEnter:Function,onAfterLeave:Function,transformOrigin:String,blockScroll:{type:Boolean,default:!0},closeOnEsc:{type:Boolean,default:!0},onEsc:Function,autoFocus:{type:Boolean,default:!0},internalStyle:[String,Object],maskClosable:{type:Boolean,default:!0},onPositiveClick:Function,onNegativeClick:Function,onClose:Function,onMaskClick:Function}),NDialogEnvironment=defineComponent({name:"DialogEnvironment",props:Object.assign(Object.assign({},exposedDialogEnvProps),{internalKey:{type:String,required:!0},to:[String,Object],onInternalAfterLeave:{type:Function,required:!0}}),setup(e){const t=ref(!0);function o(){const{onInternalAfterLeave:u,internalKey:f,onAfterLeave:m}=e;u&&u(f),m&&m()}function r(u){const{onPositiveClick:f}=e;f?Promise.resolve(f(u)).then(m=>{m!==!1&&s()}):s()}function n(u){const{onNegativeClick:f}=e;f?Promise.resolve(f(u)).then(m=>{m!==!1&&s()}):s()}function i(){const{onClose:u}=e;u?Promise.resolve(u()).then(f=>{f!==!1&&s()}):s()}function a(u){const{onMaskClick:f,maskClosable:m}=e;f&&(f(u),m&&s())}function l(){const{onEsc:u}=e;u&&u()}function s(){t.value=!1}function d(u){t.value=u}return{show:t,hide:s,handleUpdateShow:d,handleAfterLeave:o,handleCloseClick:i,handleNegativeClick:n,handlePositiveClick:r,handleMaskClick:a,handleEsc:l}},render(){const{handlePositiveClick:e,handleUpdateShow:t,handleNegativeClick:o,handleCloseClick:r,handleAfterLeave:n,handleMaskClick:i,handleEsc:a,to:l,maskClosable:s,show:d}=this;return h(NModal,{show:d,onUpdateShow:t,onMaskClick:i,onEsc:a,to:l,maskClosable:s,onAfterEnter:this.onAfterEnter,onAfterLeave:n,closeOnEsc:this.closeOnEsc,blockScroll:this.blockScroll,autoFocus:this.autoFocus,transformOrigin:this.transformOrigin,internalAppear:!0,internalDialog:!0},{default:()=>h(NDialog,Object.assign({},keep(this.$props,dialogPropKeys),{style:this.internalStyle,onClose:r,onNegativeClick:o,onPositiveClick:e}))})}}),dialogProviderProps={injectionKey:String,to:[String,Object]},NDialogProvider=defineComponent({name:"DialogProvider",props:dialogProviderProps,setup(){const e=ref([]),t={};function o(l={}){const s=createId(),d=reactive(Object.assign(Object.assign({},l),{key:s,destroy:()=>{t[`n-dialog-${s}`].hide()}}));return e.value.push(d),d}const r=["info","success","warning","error"].map(l=>s=>o(Object.assign(Object.assign({},s),{type:l})));function n(l){const{value:s}=e;s.splice(s.findIndex(d=>d.key===l),1)}function i(){Object.values(t).forEach(l=>{l.hide()})}const a={create:o,destroyAll:i,info:r[0],success:r[1],warning:r[2],error:r[3]};return provide(dialogApiInjectionKey,a),provide(dialogProviderInjectionKey,{clickedRef:useClicked(64),clickPositionRef:useClickPosition()}),provide(dialogReactiveListInjectionKey,e),Object.assign(Object.assign({},a),{dialogList:e,dialogInstRefs:t,handleAfterLeave:n})},render(){var e,t;return h(Fragment,null,[this.dialogList.map(o=>h(NDialogEnvironment,omit(o,["destroy","style"],{internalStyle:o.style,to:this.to,ref:r=>{r===null?delete this.dialogInstRefs[`n-dialog-${o.key}`]:this.dialogInstRefs[`n-dialog-${o.key}`]=r},internalKey:o.key,onInternalAfterLeave:this.handleAfterLeave}))),(t=(e=this.$slots).default)===null||t===void 0?void 0:t.call(e)])}});function useDialog(){const e=inject(dialogApiInjectionKey,null);return e===null&&throwError("use-dialog","No outer <n-dialog-provider /> founded."),e}function useDialogReactiveList(){const e=inject(dialogReactiveListInjectionKey,null);return e===null&&throwError("use-dialog-reactive-list","No outer <n-dialog-provider /> founded."),e}const self$o=e=>{const{textColor1:t,dividerColor:o,fontWeightStrong:r}=e;return{textColor:t,color:o,fontWeight:r}},dividerDark={name:"Divider",common:commonDark,self:self$o},dividerDark$1=dividerDark,self$n=e=>{const{modalColor:t,textColor1:o,textColor2:r,boxShadow3:n,lineHeight:i,fontWeightStrong:a,dividerColor:l,closeColorHover:s,closeColorPressed:d,closeIconColor:u,closeIconColorHover:f,closeIconColorPressed:m,borderRadius:v,primaryColorHover:g}=e;return{bodyPadding:"16px 24px",headerPadding:"16px 24px",footerPadding:"16px 24px",color:t,textColor:r,titleTextColor:o,titleFontSize:"18px",titleFontWeight:a,boxShadow:n,lineHeight:i,headerBorderBottom:`1px solid ${l}`,footerBorderTop:`1px solid ${l}`,closeIconColor:u,closeIconColorHover:f,closeIconColorPressed:m,closeSize:"22px",closeIconSize:"18px",closeColorHover:s,closeColorPressed:d,closeBorderRadius:v,resizableTriggerColorHover:g}},drawerDark={name:"Drawer",common:commonDark,peers:{Scrollbar:scrollbarDark$1},self:self$n},drawerDark$1=drawerDark,commonVariables$6={actionMargin:"0 0 0 20px",actionMarginRtl:"0 20px 0 0"},dynamicInputDark={name:"DynamicInput",common:commonDark,peers:{Input:inputDark$1,Button:buttonDark$1},self(){return commonVariables$6}},dynamicInputDark$1=dynamicInputDark,commonVars$4={gapSmall:"4px 8px",gapMedium:"8px 12px",gapLarge:"12px 16px"},spaceDark={name:"Space",self(){return commonVars$4}},spaceDark$1=spaceDark,self$m=()=>commonVars$4,spaceLight={name:"Space",self:self$m},spaceLight$1=spaceLight;let supportFlexGap;const ensureSupportFlexGap=()=>{if(!isBrowser$2)return!0;if(supportFlexGap===void 0){const e=document.createElement("div");e.style.display="flex",e.style.flexDirection="column",e.style.rowGap="1px",e.appendChild(document.createElement("div")),e.appendChild(document.createElement("div")),document.body.appendChild(e);const t=e.scrollHeight===1;return document.body.removeChild(e),supportFlexGap=t}return supportFlexGap},spaceProps=Object.assign(Object.assign({},useTheme.props),{align:String,justify:{type:String,default:"start"},inline:Boolean,vertical:Boolean,size:{type:[String,Number,Array],default:"medium"},wrapItem:{type:Boolean,default:!0},itemClass:String,itemStyle:[String,Object],wrap:{type:Boolean,default:!0},internalUseGap:{type:Boolean,default:void 0}}),__unplugin_components_1=defineComponent({name:"Space",props:spaceProps,setup(e){const{mergedClsPrefixRef:t,mergedRtlRef:o}=useConfig(e),r=useTheme("Space","-space",void 0,spaceLight$1,e,t),n=useRtl("Space",o,t);return{useGap:ensureSupportFlexGap(),rtlEnabled:n,mergedClsPrefix:t,margin:computed(()=>{const{size:i}=e;if(Array.isArray(i))return{horizontal:i[0],vertical:i[1]};if(typeof i=="number")return{horizontal:i,vertical:i};const{self:{[createKey("gap",i)]:a}}=r.value,{row:l,col:s}=getGap(a);return{horizontal:depx(s),vertical:depx(l)}})}},render(){const{vertical:e,align:t,inline:o,justify:r,itemClass:n,itemStyle:i,margin:a,wrap:l,mergedClsPrefix:s,rtlEnabled:d,useGap:u,wrapItem:f,internalUseGap:m}=this,v=flatten(getSlot$1(this));if(!v.length)return null;const g=`${a.horizontal}px`,x=`${a.horizontal/2}px`,C=`${a.vertical}px`,y=`${a.vertical/2}px`,I=v.length-1,N=r.startsWith("space-");return h("div",{role:"none",class:[`${s}-space`,d&&`${s}-space--rtl`],style:{display:o?"inline-flex":"flex",flexDirection:e?"column":"row",justifyContent:["start","end"].includes(r)?"flex-"+r:r,flexWrap:!l||e?"nowrap":"wrap",marginTop:u||e?"":`-${y}`,marginBottom:u||e?"":`-${y}`,alignItems:t,gap:u?`${a.vertical}px ${a.horizontal}px`:""}},!f&&(u||m)?v:v.map((E,P)=>h("div",{role:"none",class:n,style:[i,{maxWidth:"100%"},u?"":e?{marginBottom:P!==I?C:""}:d?{marginLeft:N?r==="space-between"&&P===I?"":x:P!==I?g:"",marginRight:N?r==="space-between"&&P===0?"":x:"",paddingTop:y,paddingBottom:y}:{marginRight:N?r==="space-between"&&P===I?"":x:P!==I?g:"",marginLeft:N?r==="space-between"&&P===0?"":x:"",paddingTop:y,paddingBottom:y}]},E)))}}),dynamicTagsDark={name:"DynamicTags",common:commonDark,peers:{Input:inputDark$1,Button:buttonDark$1,Tag:tagDark$1,Space:spaceDark$1},self(){return{inputWidth:"64px"}}},dynamicTagsDark$1=dynamicTagsDark,elementDark={name:"Element",common:commonDark},elementDark$1=elementDark,commonVariables$5={feedbackPadding:"4px 0 0 2px",feedbackHeightSmall:"24px",feedbackHeightMedium:"24px",feedbackHeightLarge:"26px",feedbackFontSizeSmall:"13px",feedbackFontSizeMedium:"14px",feedbackFontSizeLarge:"14px",labelFontSizeLeftSmall:"14px",labelFontSizeLeftMedium:"14px",labelFontSizeLeftLarge:"15px",labelFontSizeTopSmall:"13px",labelFontSizeTopMedium:"14px",labelFontSizeTopLarge:"14px",labelHeightSmall:"24px",labelHeightMedium:"26px",labelHeightLarge:"28px",labelPaddingVertical:"0 0 6px 2px",labelPaddingHorizontal:"0 12px 0 0",labelTextAlignVertical:"left",labelTextAlignHorizontal:"right",labelFontWeight:"400"},self$l=e=>{const{heightSmall:t,heightMedium:o,heightLarge:r,textColor1:n,errorColor:i,warningColor:a,lineHeight:l,textColor3:s}=e;return Object.assign(Object.assign({},commonVariables$5),{blankHeightSmall:t,blankHeightMedium:o,blankHeightLarge:r,lineHeight:l,labelTextColor:n,asteriskColor:i,feedbackTextColorError:i,feedbackTextColorWarning:a,feedbackTextColor:s})},formItemDark={name:"Form",common:commonDark,self:self$l},formDark=formItemDark,__unplugin_components_3$1=defineComponent({name:"GlobalStyle",setup(){if(typeof document>"u")return;const e=inject(configProviderInjectionKey,null),{body:t}=document,{style:o}=t;let r=!1,n=!0;onBeforeMount(()=>{watchEffect(()=>{var i,a;const{textColor2:l,fontSize:s,fontFamily:d,bodyColor:u,cubicBezierEaseInOut:f,lineHeight:m}=e?merge$1({},((i=e.mergedThemeRef.value)===null||i===void 0?void 0:i.common)||commonLight,(a=e.mergedThemeOverridesRef.value)===null||a===void 0?void 0:a.common):commonLight;if(r||!t.hasAttribute("n-styled")){o.setProperty("-webkit-text-size-adjust","100%"),o.setProperty("-webkit-tap-highlight-color","transparent"),o.padding="0",o.margin="0",o.backgroundColor=u,o.color=l,o.fontSize=s,o.fontFamily=d,o.lineHeight=m;const v=`color .3s ${f}, background-color .3s ${f}`;n?setTimeout(()=>{o.transition=v},0):o.transition=v,t.setAttribute("n-styled",""),r=!0,n=!1}})}),onUnmounted(()=>{r&&t.removeAttribute("n-styled")})},render(){return null}}),gradientTextDark={name:"GradientText",common:commonDark,self(e){const{primaryColor:t,successColor:o,warningColor:r,errorColor:n,infoColor:i,primaryColorSuppl:a,successColorSuppl:l,warningColorSuppl:s,errorColorSuppl:d,infoColorSuppl:u,fontWeightStrong:f}=e;return{fontWeight:f,rotate:"252deg",colorStartPrimary:t,colorEndPrimary:a,colorStartInfo:i,colorEndInfo:u,colorStartWarning:r,colorEndWarning:s,colorStartError:n,colorEndError:d,colorStartSuccess:o,colorEndSuccess:l}}},gradientTextDark$1=gradientTextDark,self$k=e=>{const{primaryColor:t,baseColor:o}=e;return{color:t,iconColor:o}},iconDark={name:"IconWrapper",common:commonDark,self:self$k},iconWrapperDark=iconDark,commonVars$3={closeMargin:"16px 12px",closeSize:"20px",closeIconSize:"16px",width:"365px",padding:"16px",titleFontSize:"16px",metaFontSize:"12px",descriptionFontSize:"12px"},self$j=e=>{const{textColor2:t,successColor:o,infoColor:r,warningColor:n,errorColor:i,popoverColor:a,closeIconColor:l,closeIconColorHover:s,closeIconColorPressed:d,closeColorHover:u,closeColorPressed:f,textColor1:m,textColor3:v,borderRadius:g,fontWeightStrong:x,boxShadow2:C,lineHeight:y,fontSize:I}=e;return Object.assign(Object.assign({},commonVars$3),{borderRadius:g,lineHeight:y,fontSize:I,headerFontWeight:x,iconColor:t,iconColorSuccess:o,iconColorInfo:r,iconColorWarning:n,iconColorError:i,color:a,textColor:t,closeIconColor:l,closeIconColorHover:s,closeIconColorPressed:d,closeBorderRadius:g,closeColorHover:u,closeColorPressed:f,headerTextColor:m,descriptionTextColor:v,actionTextColor:t,boxShadow:C})},notificationDark={name:"Notification",common:commonDark,peers:{Scrollbar:scrollbarDark$1},self:self$j},notificationDark$1=notificationDark,commonVariables$4={margin:"0 0 8px 0",padding:"10px 20px",maxWidth:"720px",minWidth:"420px",iconMargin:"0 10px 0 0",closeMargin:"0 0 0 10px",closeSize:"20px",closeIconSize:"16px",iconSize:"20px",fontSize:"14px"},self$i=e=>{const{textColor2:t,closeIconColor:o,closeIconColorHover:r,closeIconColorPressed:n,infoColor:i,successColor:a,errorColor:l,warningColor:s,popoverColor:d,boxShadow2:u,primaryColor:f,lineHeight:m,borderRadius:v,closeColorHover:g,closeColorPressed:x}=e;return Object.assign(Object.assign({},commonVariables$4),{closeBorderRadius:v,textColor:t,textColorInfo:t,textColorSuccess:t,textColorError:t,textColorWarning:t,textColorLoading:t,color:d,colorInfo:d,colorSuccess:d,colorError:d,colorWarning:d,colorLoading:d,boxShadow:u,boxShadowInfo:u,boxShadowSuccess:u,boxShadowError:u,boxShadowWarning:u,boxShadowLoading:u,iconColor:t,iconColorInfo:i,iconColorSuccess:a,iconColorWarning:s,iconColorError:l,iconColorLoading:f,closeColorHover:g,closeColorPressed:x,closeIconColor:o,closeIconColorHover:r,closeIconColorPressed:n,closeColorHoverInfo:g,closeColorPressedInfo:x,closeIconColorInfo:o,closeIconColorHoverInfo:r,closeIconColorPressedInfo:n,closeColorHoverSuccess:g,closeColorPressedSuccess:x,closeIconColorSuccess:o,closeIconColorHoverSuccess:r,closeIconColorPressedSuccess:n,closeColorHoverError:g,closeColorPressedError:x,closeIconColorError:o,closeIconColorHoverError:r,closeIconColorPressedError:n,closeColorHoverWarning:g,closeColorPressedWarning:x,closeIconColorWarning:o,closeIconColorHoverWarning:r,closeIconColorPressedWarning:n,closeColorHoverLoading:g,closeColorPressedLoading:x,closeIconColorLoading:o,closeIconColorHoverLoading:r,closeIconColorPressedLoading:n,loadingColor:f,lineHeight:m,borderRadius:v})},messageLight={name:"Message",common:commonLight,self:self$i},messageDark={name:"Message",common:commonDark,self:self$i},messageDark$1=messageDark,buttonGroupDark={name:"ButtonGroup",common:commonDark},buttonGroupDark$1=buttonGroupDark,inputNumberDark={name:"InputNumber",common:commonDark,peers:{Button:buttonDark$1,Input:inputDark$1},self(e){const{textColorDisabled:t}=e;return{iconColorDisabled:t}}},inputNumberDark$1=inputNumberDark,self$h=e=>{const{textColorDisabled:t}=e;return{iconColorDisabled:t}},inputNumberLight={name:"InputNumber",common:commonLight,peers:{Button:buttonLight,Input:inputLight$1},self:self$h},inputNumberLight$1=inputNumberLight,layoutDark={name:"Layout",common:commonDark,peers:{Scrollbar:scrollbarDark$1},self(e){const{textColor2:t,bodyColor:o,popoverColor:r,cardColor:n,dividerColor:i,scrollbarColor:a,scrollbarColorHover:l}=e;return{textColor:t,textColorInverted:t,color:o,colorEmbedded:o,headerColor:n,headerColorInverted:n,footerColor:n,footerColorInverted:n,headerBorderColor:i,headerBorderColorInverted:i,footerBorderColor:i,footerBorderColorInverted:i,siderBorderColor:i,siderBorderColorInverted:i,siderColor:n,siderColorInverted:n,siderToggleButtonBorder:"1px solid transparent",siderToggleButtonColor:r,siderToggleButtonIconColor:t,siderToggleButtonIconColorInverted:t,siderToggleBarColor:composite(o,a),siderToggleBarColorHover:composite(o,l),__invertScrollbar:"false"}}},layoutDark$1=layoutDark,self$g=e=>{const{textColor2:t,cardColor:o,modalColor:r,popoverColor:n,dividerColor:i,borderRadius:a,fontSize:l,hoverColor:s}=e;return{textColor:t,color:o,colorHover:s,colorModal:r,colorHoverModal:composite(r,s),colorPopover:n,colorHoverPopover:composite(n,s),borderColor:i,borderColorModal:composite(r,i),borderColorPopover:composite(n,i),borderRadius:a,fontSize:l}},listDark$1={name:"List",common:commonDark,self:self$g},listDark$2=listDark$1,loadingBarDark={name:"LoadingBar",common:commonDark,self(e){const{primaryColor:t}=e;return{colorError:"red",colorLoading:t,height:"2px"}}},loadingBarDark$1=loadingBarDark,logDark={name:"Log",common:commonDark,peers:{Scrollbar:scrollbarDark$1,Code:codeDark$1},self(e){const{textColor2:t,inputColor:o,fontSize:r,primaryColor:n}=e;return{loaderFontSize:r,loaderTextColor:t,loaderColor:o,loaderBorder:"1px solid #0000",loadingColor:n}}},logDark$1=logDark,listDark={name:"Mention",common:commonDark,peers:{InternalSelectMenu:internalSelectMenuDark$1,Input:inputDark$1},self(e){const{boxShadow2:t}=e;return{menuBoxShadow:t}}},mentionDark=listDark;function createPartialInvertedVars(e,t,o,r){return{itemColorHoverInverted:"#0000",itemColorActiveInverted:t,itemColorActiveHoverInverted:t,itemColorActiveCollapsedInverted:t,itemTextColorInverted:e,itemTextColorHoverInverted:o,itemTextColorChildActiveInverted:o,itemTextColorChildActiveHoverInverted:o,itemTextColorActiveInverted:o,itemTextColorActiveHoverInverted:o,itemTextColorHorizontalInverted:e,itemTextColorHoverHorizontalInverted:o,itemTextColorChildActiveHorizontalInverted:o,itemTextColorChildActiveHoverHorizontalInverted:o,itemTextColorActiveHorizontalInverted:o,itemTextColorActiveHoverHorizontalInverted:o,itemIconColorInverted:e,itemIconColorHoverInverted:o,itemIconColorActiveInverted:o,itemIconColorActiveHoverInverted:o,itemIconColorChildActiveInverted:o,itemIconColorChildActiveHoverInverted:o,itemIconColorCollapsedInverted:e,itemIconColorHorizontalInverted:e,itemIconColorHoverHorizontalInverted:o,itemIconColorActiveHorizontalInverted:o,itemIconColorActiveHoverHorizontalInverted:o,itemIconColorChildActiveHorizontalInverted:o,itemIconColorChildActiveHoverHorizontalInverted:o,arrowColorInverted:e,arrowColorHoverInverted:o,arrowColorActiveInverted:o,arrowColorActiveHoverInverted:o,arrowColorChildActiveInverted:o,arrowColorChildActiveHoverInverted:o,groupTextColorInverted:r}}const self$f=e=>{const{borderRadius:t,textColor3:o,primaryColor:r,textColor2:n,textColor1:i,fontSize:a,dividerColor:l,hoverColor:s,primaryColorHover:d}=e;return Object.assign({borderRadius:t,color:"#0000",groupTextColor:o,itemColorHover:s,itemColorActive:changeColor(r,{alpha:.1}),itemColorActiveHover:changeColor(r,{alpha:.1}),itemColorActiveCollapsed:changeColor(r,{alpha:.1}),itemTextColor:n,itemTextColorHover:n,itemTextColorActive:r,itemTextColorActiveHover:r,itemTextColorChildActive:r,itemTextColorChildActiveHover:r,itemTextColorHorizontal:n,itemTextColorHoverHorizontal:d,itemTextColorActiveHorizontal:r,itemTextColorActiveHoverHorizontal:r,itemTextColorChildActiveHorizontal:r,itemTextColorChildActiveHoverHorizontal:r,itemIconColor:i,itemIconColorHover:i,itemIconColorActive:r,itemIconColorActiveHover:r,itemIconColorChildActive:r,itemIconColorChildActiveHover:r,itemIconColorCollapsed:i,itemIconColorHorizontal:i,itemIconColorHoverHorizontal:d,itemIconColorActiveHorizontal:r,itemIconColorActiveHoverHorizontal:r,itemIconColorChildActiveHorizontal:r,itemIconColorChildActiveHoverHorizontal:r,itemHeight:"42px",arrowColor:n,arrowColorHover:n,arrowColorActive:r,arrowColorActiveHover:r,arrowColorChildActive:r,arrowColorChildActiveHover:r,colorInverted:"#0000",borderColorHorizontal:"#0000",fontSize:a,dividerColor:l},createPartialInvertedVars("#BBB",r,"#FFF","#AAA"))},menuDark={name:"Menu",common:commonDark,peers:{Tooltip:tooltipDark$1,Dropdown:dropdownDark$1},self(e){const{primaryColor:t,primaryColorSuppl:o}=e,r=self$f(e);return r.itemColorActive=changeColor(t,{alpha:.15}),r.itemColorActiveHover=changeColor(t,{alpha:.15}),r.itemColorActiveCollapsed=changeColor(t,{alpha:.15}),r.itemColorActiveInverted=o,r.itemColorActiveHoverInverted=o,r.itemColorActiveCollapsedInverted=o,r}},menuDark$1=menuDark,common={titleFontSize:"18px",backSize:"22px"};function self$e(e){const{textColor1:t,textColor2:o,textColor3:r,fontSize:n,fontWeightStrong:i,primaryColorHover:a,primaryColorPressed:l}=e;return Object.assign(Object.assign({},common),{titleFontWeight:i,fontSize:n,titleTextColor:t,backColor:o,backColorHover:a,backColorPressed:l,subtitleTextColor:r})}const pageHeaderDark={name:"PageHeader",common:commonDark,self:self$e},commonVars$2={iconSize:"22px"},self$d=e=>{const{fontSize:t,warningColor:o}=e;return Object.assign(Object.assign({},commonVars$2),{fontSize:t,iconColor:o})},popconfirmDark={name:"Popconfirm",common:commonDark,peers:{Button:buttonDark$1,Popover:popoverDark$1},self:self$d},popconfirmDark$1=popconfirmDark,self$c=e=>{const{infoColor:t,successColor:o,warningColor:r,errorColor:n,textColor2:i,progressRailColor:a,fontSize:l,fontWeight:s}=e;return{fontSize:l,fontSizeCircle:"28px",fontWeightCircle:s,railColor:a,railHeight:"8px",iconSizeCircle:"36px",iconSizeLine:"18px",iconColor:t,iconColorInfo:t,iconColorSuccess:o,iconColorWarning:r,iconColorError:n,textColorCircle:i,textColorLineInner:"rgb(255, 255, 255)",textColorLineOuter:i,fillColor:t,fillColorInfo:t,fillColorSuccess:o,fillColorWarning:r,fillColorError:n,lineBgProcessing:"linear-gradient(90deg, rgba(255, 255, 255, .3) 0%, rgba(255, 255, 255, .5) 100%)"}},progressDark={name:"Progress",common:commonDark,self(e){const t=self$c(e);return t.textColorLineInner="rgb(0, 0, 0)",t.lineBgProcessing="linear-gradient(90deg, rgba(255, 255, 255, .3) 0%, rgba(255, 255, 255, .5) 100%)",t}},progressDark$1=progressDark,rateDark={name:"Rate",common:commonDark,self(e){const{railColor:t}=e;return{itemColor:t,itemColorActive:"#CCAA33",itemSize:"20px",sizeSmall:"16px",sizeMedium:"20px",sizeLarge:"24px"}}},rateDark$1=rateDark,commonVariables$3={titleFontSizeSmall:"26px",titleFontSizeMedium:"32px",titleFontSizeLarge:"40px",titleFontSizeHuge:"48px",fontSizeSmall:"14px",fontSizeMedium:"14px",fontSizeLarge:"15px",fontSizeHuge:"16px",iconSizeSmall:"64px",iconSizeMedium:"80px",iconSizeLarge:"100px",iconSizeHuge:"125px",iconColor418:void 0,iconColor404:void 0,iconColor403:void 0,iconColor500:void 0},self$b=e=>{const{textColor2:t,textColor1:o,errorColor:r,successColor:n,infoColor:i,warningColor:a,lineHeight:l,fontWeightStrong:s}=e;return Object.assign(Object.assign({},commonVariables$3),{lineHeight:l,titleFontWeight:s,titleTextColor:o,textColor:t,iconColorError:r,iconColorSuccess:n,iconColorInfo:i,iconColorWarning:a})},resultDark={name:"Result",common:commonDark,self:self$b},resultDark$1=resultDark,sizeVariables$3={railHeight:"4px",railWidthVertical:"4px",handleSize:"18px",dotHeight:"8px",dotWidth:"8px",dotBorderRadius:"4px"},sliderDark={name:"Slider",common:commonDark,self(e){const t="0 2px 8px 0 rgba(0, 0, 0, 0.12)",{railColor:o,modalColor:r,primaryColorSuppl:n,popoverColor:i,textColor2:a,cardColor:l,borderRadius:s,fontSize:d,opacityDisabled:u}=e;return Object.assign(Object.assign({},sizeVariables$3),{fontSize:d,markFontSize:d,railColor:o,railColorHover:o,fillColor:n,fillColorHover:n,opacityDisabled:u,handleColor:"#FFF",dotColor:l,dotColorModal:r,dotColorPopover:i,handleBoxShadow:"0px 2px 4px 0 rgba(0, 0, 0, 0.4)",handleBoxShadowHover:"0px 2px 4px 0 rgba(0, 0, 0, 0.4)",handleBoxShadowActive:"0px 2px 4px 0 rgba(0, 0, 0, 0.4)",handleBoxShadowFocus:"0px 2px 4px 0 rgba(0, 0, 0, 0.4)",indicatorColor:i,indicatorBoxShadow:t,indicatorTextColor:a,indicatorBorderRadius:s,dotBorder:`2px solid ${o}`,dotBorderActive:`2px solid ${n}`,dotBoxShadow:""})}},sliderDark$1=sliderDark,self$a=e=>{const{opacityDisabled:t,heightTiny:o,heightSmall:r,heightMedium:n,heightLarge:i,heightHuge:a,primaryColor:l,fontSize:s}=e;return{fontSize:s,textColor:l,sizeTiny:o,sizeSmall:r,sizeMedium:n,sizeLarge:i,sizeHuge:a,color:l,opacitySpinning:t}},spinLight={name:"Spin",common:commonLight,self:self$a},spinDark={name:"Spin",common:commonDark,self:self$a},spinDark$1=spinDark,self$9=e=>{const{textColor2:t,textColor3:o,fontSize:r,fontWeight:n}=e;return{labelFontSize:r,labelFontWeight:n,valueFontWeight:n,valueFontSize:"24px",labelTextColor:o,valuePrefixTextColor:t,valueSuffixTextColor:t,valueTextColor:t}},statisticDark={name:"Statistic",common:commonDark,self:self$9},statisticDark$1=statisticDark,commonVariables$2={stepHeaderFontSizeSmall:"14px",stepHeaderFontSizeMedium:"16px",indicatorIndexFontSizeSmall:"14px",indicatorIndexFontSizeMedium:"16px",indicatorSizeSmall:"22px",indicatorSizeMedium:"28px",indicatorIconSizeSmall:"14px",indicatorIconSizeMedium:"18px"},self$8=e=>{const{fontWeightStrong:t,baseColor:o,textColorDisabled:r,primaryColor:n,errorColor:i,textColor1:a,textColor2:l}=e;return Object.assign(Object.assign({},commonVariables$2),{stepHeaderFontWeight:t,indicatorTextColorProcess:o,indicatorTextColorWait:r,indicatorTextColorFinish:n,indicatorTextColorError:i,indicatorBorderColorProcess:n,indicatorBorderColorWait:r,indicatorBorderColorFinish:n,indicatorBorderColorError:i,indicatorColorProcess:n,indicatorColorWait:"#0000",indicatorColorFinish:"#0000",indicatorColorError:"#0000",splitorColorProcess:r,splitorColorWait:r,splitorColorFinish:n,splitorColorError:r,headerTextColorProcess:a,headerTextColorWait:r,headerTextColorFinish:r,headerTextColorError:i,descriptionTextColorProcess:l,descriptionTextColorWait:r,descriptionTextColorFinish:r,descriptionTextColorError:i})},stepsDark={name:"Steps",common:commonDark,self:self$8},stepsDark$1=stepsDark,commonVars$1={buttonHeightSmall:"14px",buttonHeightMedium:"18px",buttonHeightLarge:"22px",buttonWidthSmall:"14px",buttonWidthMedium:"18px",buttonWidthLarge:"22px",buttonWidthPressedSmall:"20px",buttonWidthPressedMedium:"24px",buttonWidthPressedLarge:"28px",railHeightSmall:"18px",railHeightMedium:"22px",railHeightLarge:"26px",railWidthSmall:"32px",railWidthMedium:"40px",railWidthLarge:"48px"},switchDark={name:"Switch",common:commonDark,self(e){const{primaryColorSuppl:t,opacityDisabled:o,borderRadius:r,primaryColor:n,textColor2:i,baseColor:a}=e,l="rgba(255, 255, 255, .20)";return Object.assign(Object.assign({},commonVars$1),{iconColor:a,textColor:i,loadingColor:t,opacityDisabled:o,railColor:l,railColorActive:t,buttonBoxShadow:"0px 2px 4px 0 rgba(0, 0, 0, 0.4)",buttonColor:"#FFF",railBorderRadiusSmall:r,railBorderRadiusMedium:r,railBorderRadiusLarge:r,buttonBorderRadiusSmall:r,buttonBorderRadiusMedium:r,buttonBorderRadiusLarge:r,boxShadowFocus:`0 0 8px 0 ${changeColor(n,{alpha:.3})}`})}},switchDark$1=switchDark,sizeVariables$2={thPaddingSmall:"6px",thPaddingMedium:"12px",thPaddingLarge:"12px",tdPaddingSmall:"6px",tdPaddingMedium:"12px",tdPaddingLarge:"12px"},self$7=e=>{const{dividerColor:t,cardColor:o,modalColor:r,popoverColor:n,tableHeaderColor:i,tableColorStriped:a,textColor1:l,textColor2:s,borderRadius:d,fontWeightStrong:u,lineHeight:f,fontSizeSmall:m,fontSizeMedium:v,fontSizeLarge:g}=e;return Object.assign(Object.assign({},sizeVariables$2),{fontSizeSmall:m,fontSizeMedium:v,fontSizeLarge:g,lineHeight:f,borderRadius:d,borderColor:composite(o,t),borderColorModal:composite(r,t),borderColorPopover:composite(n,t),tdColor:o,tdColorModal:r,tdColorPopover:n,tdColorStriped:composite(o,a),tdColorStripedModal:composite(r,a),tdColorStripedPopover:composite(n,a),thColor:composite(o,i),thColorModal:composite(r,i),thColorPopover:composite(n,i),thTextColor:l,tdTextColor:s,thFontWeight:u})},tableDark={name:"Table",common:commonDark,self:self$7},tableDark$1=tableDark,sizeVariables$1={tabFontSizeSmall:"14px",tabFontSizeMedium:"14px",tabFontSizeLarge:"16px",tabGapSmallLine:"36px",tabGapMediumLine:"36px",tabGapLargeLine:"36px",tabGapSmallLineVertical:"8px",tabGapMediumLineVertical:"8px",tabGapLargeLineVertical:"8px",tabPaddingSmallLine:"6px 0",tabPaddingMediumLine:"10px 0",tabPaddingLargeLine:"14px 0",tabPaddingVerticalSmallLine:"6px 12px",tabPaddingVerticalMediumLine:"8px 16px",tabPaddingVerticalLargeLine:"10px 20px",tabGapSmallBar:"36px",tabGapMediumBar:"36px",tabGapLargeBar:"36px",tabGapSmallBarVertical:"8px",tabGapMediumBarVertical:"8px",tabGapLargeBarVertical:"8px",tabPaddingSmallBar:"4px 0",tabPaddingMediumBar:"6px 0",tabPaddingLargeBar:"10px 0",tabPaddingVerticalSmallBar:"6px 12px",tabPaddingVerticalMediumBar:"8px 16px",tabPaddingVerticalLargeBar:"10px 20px",tabGapSmallCard:"4px",tabGapMediumCard:"4px",tabGapLargeCard:"4px",tabGapSmallCardVertical:"4px",tabGapMediumCardVertical:"4px",tabGapLargeCardVertical:"4px",tabPaddingSmallCard:"8px 16px",tabPaddingMediumCard:"10px 20px",tabPaddingLargeCard:"12px 24px",tabPaddingSmallSegment:"4px 0",tabPaddingMediumSegment:"6px 0",tabPaddingLargeSegment:"8px 0",tabPaddingVerticalLargeSegment:"0 8px",tabPaddingVerticalSmallCard:"8px 12px",tabPaddingVerticalMediumCard:"10px 16px",tabPaddingVerticalLargeCard:"12px 20px",tabPaddingVerticalSmallSegment:"0 4px",tabPaddingVerticalMediumSegment:"0 6px",tabGapSmallSegment:"0",tabGapMediumSegment:"0",tabGapLargeSegment:"0",tabGapSmallSegmentVertical:"0",tabGapMediumSegmentVertical:"0",tabGapLargeSegmentVertical:"0",panePaddingSmall:"8px 0 0 0",panePaddingMedium:"12px 0 0 0",panePaddingLarge:"16px 0 0 0",closeSize:"18px",closeIconSize:"14px"},self$6=e=>{const{textColor2:t,primaryColor:o,textColorDisabled:r,closeIconColor:n,closeIconColorHover:i,closeIconColorPressed:a,closeColorHover:l,closeColorPressed:s,tabColor:d,baseColor:u,dividerColor:f,fontWeight:m,textColor1:v,borderRadius:g,fontSize:x,fontWeightStrong:C}=e;return Object.assign(Object.assign({},sizeVariables$1),{colorSegment:d,tabFontSizeCard:x,tabTextColorLine:v,tabTextColorActiveLine:o,tabTextColorHoverLine:o,tabTextColorDisabledLine:r,tabTextColorSegment:v,tabTextColorActiveSegment:t,tabTextColorHoverSegment:t,tabTextColorDisabledSegment:r,tabTextColorBar:v,tabTextColorActiveBar:o,tabTextColorHoverBar:o,tabTextColorDisabledBar:r,tabTextColorCard:v,tabTextColorHoverCard:v,tabTextColorActiveCard:o,tabTextColorDisabledCard:r,barColor:o,closeIconColor:n,closeIconColorHover:i,closeIconColorPressed:a,closeColorHover:l,closeColorPressed:s,closeBorderRadius:g,tabColor:d,tabColorSegment:u,tabBorderColor:f,tabFontWeightActive:m,tabFontWeight:m,tabBorderRadius:g,paneTextColor:t,fontWeightStrong:C})},tabsDark={name:"Tabs",common:commonDark,self(e){const t=self$6(e),{inputColor:o}=e;return t.colorSegment=o,t.tabColorSegment=o,t}},tabsDark$1=tabsDark,self$5=e=>{const{textColor1:t,textColor2:o,fontWeightStrong:r,fontSize:n}=e;return{fontSize:n,titleTextColor:t,textColor:o,titleFontWeight:r}},thingDark={name:"Thing",common:commonDark,self:self$5},thingDark$1=thingDark,sizeVariables={titleMarginMedium:"0 0 6px 0",titleMarginLarge:"-2px 0 6px 0",titleFontSizeMedium:"14px",titleFontSizeLarge:"16px",iconSizeMedium:"14px",iconSizeLarge:"14px"},timelineDark={name:"Timeline",common:commonDark,self(e){const{textColor3:t,infoColorSuppl:o,errorColorSuppl:r,successColorSuppl:n,warningColorSuppl:i,textColor1:a,textColor2:l,railColor:s,fontWeightStrong:d,fontSize:u}=e;return Object.assign(Object.assign({},sizeVariables),{contentFontSize:u,titleFontWeight:d,circleBorder:`2px solid ${t}`,circleBorderInfo:`2px solid ${o}`,circleBorderError:`2px solid ${r}`,circleBorderSuccess:`2px solid ${n}`,circleBorderWarning:`2px solid ${i}`,iconColor:t,iconColorInfo:o,iconColorError:r,iconColorSuccess:n,iconColorWarning:i,titleTextColor:a,contentTextColor:l,metaTextColor:t,lineColor:s})}},timelineDark$1=timelineDark,commonVariables$1={extraFontSizeSmall:"12px",extraFontSizeMedium:"12px",extraFontSizeLarge:"14px",titleFontSizeSmall:"14px",titleFontSizeMedium:"16px",titleFontSizeLarge:"16px",closeSize:"20px",closeIconSize:"16px",headerHeightSmall:"44px",headerHeightMedium:"44px",headerHeightLarge:"50px"},transferDark$1={name:"Transfer",common:commonDark,peers:{Checkbox:checkboxDark$1,Scrollbar:scrollbarDark$1,Input:inputDark$1,Empty:emptyDark$1,Button:buttonDark$1},self(e){const{fontWeight:t,fontSizeLarge:o,fontSizeMedium:r,fontSizeSmall:n,heightLarge:i,heightMedium:a,borderRadius:l,inputColor:s,tableHeaderColor:d,textColor1:u,textColorDisabled:f,textColor2:m,textColor3:v,hoverColor:g,closeColorHover:x,closeColorPressed:C,closeIconColor:y,closeIconColorHover:I,closeIconColorPressed:N,dividerColor:E}=e;return Object.assign(Object.assign({},commonVariables$1),{itemHeightSmall:a,itemHeightMedium:a,itemHeightLarge:i,fontSizeSmall:n,fontSizeMedium:r,fontSizeLarge:o,borderRadius:l,dividerColor:E,borderColor:"#0000",listColor:s,headerColor:d,titleTextColor:u,titleTextColorDisabled:f,extraTextColor:v,extraTextColorDisabled:f,itemTextColor:m,itemTextColorDisabled:f,itemColorPending:g,titleFontWeight:t,closeColorHover:x,closeColorPressed:C,closeIconColor:y,closeIconColorHover:I,closeIconColorPressed:N})}},transferDark$2=transferDark$1,self$4=e=>{const{borderRadiusSmall:t,dividerColor:o,hoverColor:r,pressedColor:n,primaryColor:i,textColor3:a,textColor2:l,textColorDisabled:s,fontSize:d}=e;return{fontSize:d,lineHeight:"1.5",nodeHeight:"30px",nodeWrapperPadding:"3px 0",nodeBorderRadius:t,nodeColorHover:r,nodeColorPressed:n,nodeColorActive:changeColor(i,{alpha:.1}),arrowColor:a,nodeTextColor:l,nodeTextColorDisabled:s,loadingColor:i,dropMarkColor:i,lineColor:o}},treeDark={name:"Tree",common:commonDark,peers:{Checkbox:checkboxDark$1,Scrollbar:scrollbarDark$1,Empty:emptyDark$1},self(e){const{primaryColor:t}=e,o=self$4(e);return o.nodeColorActive=changeColor(t,{alpha:.15}),o}},treeDark$1=treeDark,treeSelectDark={name:"TreeSelect",common:commonDark,peers:{Tree:treeDark$1,Empty:emptyDark$1,InternalSelection:internalSelectionDark$1}},treeSelectDark$1=treeSelectDark,commonVars={headerFontSize1:"30px",headerFontSize2:"22px",headerFontSize3:"18px",headerFontSize4:"16px",headerFontSize5:"16px",headerFontSize6:"16px",headerMargin1:"28px 0 20px 0",headerMargin2:"28px 0 20px 0",headerMargin3:"28px 0 20px 0",headerMargin4:"28px 0 18px 0",headerMargin5:"28px 0 18px 0",headerMargin6:"28px 0 18px 0",headerPrefixWidth1:"16px",headerPrefixWidth2:"16px",headerPrefixWidth3:"12px",headerPrefixWidth4:"12px",headerPrefixWidth5:"12px",headerPrefixWidth6:"12px",headerBarWidth1:"4px",headerBarWidth2:"4px",headerBarWidth3:"3px",headerBarWidth4:"3px",headerBarWidth5:"3px",headerBarWidth6:"3px",pMargin:"16px 0 16px 0",liMargin:".25em 0 0 0",olPadding:"0 0 0 2em",ulPadding:"0 0 0 2em"},self$3=e=>{const{primaryColor:t,textColor2:o,borderColor:r,lineHeight:n,fontSize:i,borderRadiusSmall:a,dividerColor:l,fontWeightStrong:s,textColor1:d,textColor3:u,infoColor:f,warningColor:m,errorColor:v,successColor:g,codeColor:x}=e;return Object.assign(Object.assign({},commonVars),{aTextColor:t,blockquoteTextColor:o,blockquotePrefixColor:r,blockquoteLineHeight:n,blockquoteFontSize:i,codeBorderRadius:a,liTextColor:o,liLineHeight:n,liFontSize:i,hrColor:l,headerFontWeight:s,headerTextColor:d,pTextColor:o,pTextColor1Depth:d,pTextColor2Depth:o,pTextColor3Depth:u,pLineHeight:n,pFontSize:i,headerBarColor:t,headerBarColorPrimary:t,headerBarColorInfo:f,headerBarColorError:v,headerBarColorWarning:m,headerBarColorSuccess:g,textColor:o,textColor1Depth:d,textColor2Depth:o,textColor3Depth:u,textColorPrimary:t,textColorInfo:f,textColorSuccess:g,textColorWarning:m,textColorError:v,codeTextColor:o,codeColor:x,codeBorder:"1px solid #0000"})},typographyLight={name:"Typography",common:commonLight,self:self$3},typographyDark={name:"Typography",common:commonDark,self:self$3},typographyDark$1=typographyDark,self$2=e=>{const{iconColor:t,primaryColor:o,errorColor:r,textColor2:n,successColor:i,opacityDisabled:a,actionColor:l,borderColor:s,hoverColor:d,lineHeight:u,borderRadius:f,fontSize:m}=e;return{fontSize:m,lineHeight:u,borderRadius:f,draggerColor:l,draggerBorder:`1px dashed ${s}`,draggerBorderHover:`1px dashed ${o}`,itemColorHover:d,itemColorHoverError:changeColor(r,{alpha:.06}),itemTextColor:n,itemTextColorError:r,itemTextColorSuccess:i,itemIconColor:t,itemDisabledOpacity:a,itemBorderImageCardError:`1px solid ${r}`,itemBorderImageCard:`1px solid ${s}`}},uploadDark={name:"Upload",common:commonDark,peers:{Button:buttonDark$1,Progress:progressDark$1},self(e){const{errorColor:t}=e,o=self$2(e);return o.itemColorHoverError=changeColor(t,{alpha:.09}),o}},uploadDark$1=uploadDark,watermarkDark={name:"Watermark",common:commonDark,self(e){const{fontFamily:t}=e;return{fontFamily:t}}},watermarkDark$1=watermarkDark,rowDark={name:"Row",common:commonDark},rowDark$1=rowDark,imageDark={name:"Image",common:commonDark,peers:{Tooltip:tooltipDark$1},self:e=>{const{textColor2:t}=e;return{toolbarIconColor:t,toolbarColor:"rgba(0, 0, 0, .35)",toolbarBoxShadow:"none",toolbarBorderRadius:"24px"}}};function parse(e){return e==null||typeof e=="string"&&e.trim()===""?null:Number(e)}function isWipValue(e){return e.includes(".")&&(/^(-)?\d+.*(\.|0)$/.test(e)||/^\.\d+$/.test(e))}function validator(e){return e==null?!0:!Number.isNaN(e)}function format(e,t){return e==null?"":t===void 0?String(e):e.toFixed(t)}function parseNumber(e){if(e===null)return null;if(typeof e=="number")return e;{const t=Number(e);return Number.isNaN(t)?null:t}}const style$3=c([cB("input-number-suffix",`
 display: inline-block;
 margin-right: 10px;
 `),cB("input-number-prefix",`
 display: inline-block;
 margin-left: 10px;
 `)]),HOLDING_CHANGE_THRESHOLD=800,HOLDING_CHANGE_INTERVAL=100,inputNumberProps=Object.assign(Object.assign({},useTheme.props),{autofocus:Boolean,loading:{type:Boolean,default:void 0},placeholder:String,defaultValue:{type:Number,default:null},value:Number,step:{type:[Number,String],default:1},min:[Number,String],max:[Number,String],size:String,disabled:{type:Boolean,default:void 0},validator:Function,bordered:{type:Boolean,default:void 0},showButton:{type:Boolean,default:!0},buttonPlacement:{type:String,default:"right"},readonly:Boolean,clearable:Boolean,keyboard:{type:Object,default:{}},updateValueOnInput:{type:Boolean,default:!0},parse:Function,format:Function,precision:Number,status:String,"onUpdate:value":[Function,Array],onUpdateValue:[Function,Array],onFocus:[Function,Array],onBlur:[Function,Array],onClear:[Function,Array],onChange:[Function,Array]}),__unplugin_components_2$1=defineComponent({name:"InputNumber",props:inputNumberProps,setup(e){const{mergedBorderedRef:t,mergedClsPrefixRef:o,mergedRtlRef:r}=useConfig(e),n=useTheme("InputNumber","-input-number",style$3,inputNumberLight$1,e,o),{localeRef:i}=useLocale("InputNumber"),a=useFormItem(e),{mergedSizeRef:l,mergedDisabledRef:s,mergedStatusRef:d}=a,u=ref(null),f=ref(null),m=ref(null),v=ref(e.defaultValue),g=toRef(e,"value"),x=useMergedState(g,v),C=ref(""),y=k=>{const O=String(k).split(".")[1];return O?O.length:0},I=k=>{const O=[e.min,e.max,e.step,k].map(B=>B===void 0?0:y(B));return Math.max(...O)},N=useMemo(()=>{const{placeholder:k}=e;return k!==void 0?k:i.value.placeholder}),E=useMemo(()=>{const k=parseNumber(e.step);return k!==null?k===0?1:Math.abs(k):1}),P=useMemo(()=>{const k=parseNumber(e.min);return k!==null?k:null}),F=useMemo(()=>{const k=parseNumber(e.max);return k!==null?k:null}),S=k=>{const{value:O}=x;if(k===O){D();return}const{"onUpdate:value":B,onUpdateValue:A,onChange:M}=e,{nTriggerFormInput:Y,nTriggerFormChange:J}=a;M&&call(M,k),A&&call(A,k),B&&call(B,k),v.value=k,Y(),J()},$=({offset:k,doUpdateIfValid:O,fixPrecision:B,isInputing:A})=>{const{value:M}=C;if(A&&isWipValue(M))return!1;const Y=(e.parse||parse)(M);if(Y===null)return O&&S(null),null;if(validator(Y)){const J=y(Y),{precision:X}=e;if(X!==void 0&&X<J&&!B)return!1;let ie=parseFloat((Y+k).toFixed(X??I(Y)));if(validator(ie)){const{value:fe}=F,{value:pe}=P;if(fe!==null&&ie>fe){if(!O||A)return!1;ie=fe}if(pe!==null&&ie<pe){if(!O||A)return!1;ie=pe}return e.validator&&!e.validator(ie)?!1:(O&&S(ie),ie)}}return!1},D=()=>{const{value:k}=x;if(validator(k)){const{format:O,precision:B}=e;O?C.value=O(k):k===null||B===void 0||y(k)>B?C.value=format(k,void 0):C.value=format(k,B)}else C.value=String(k)};D();const j=useMemo(()=>$({offset:0,doUpdateIfValid:!1,isInputing:!1,fixPrecision:!1})===!1),K=useMemo(()=>{const{value:k}=x;if(e.validator&&k===null)return!1;const{value:O}=E;return $({offset:-O,doUpdateIfValid:!1,isInputing:!1,fixPrecision:!1})!==!1}),z=useMemo(()=>{const{value:k}=x;if(e.validator&&k===null)return!1;const{value:O}=E;return $({offset:+O,doUpdateIfValid:!1,isInputing:!1,fixPrecision:!1})!==!1});function ee(k){const{onFocus:O}=e,{nTriggerFormFocus:B}=a;O&&call(O,k),B()}function ae(k){var O,B;if(k.target===((O=u.value)===null||O===void 0?void 0:O.wrapperElRef))return;const A=$({offset:0,doUpdateIfValid:!0,isInputing:!1,fixPrecision:!0});if(A!==!1){const J=(B=u.value)===null||B===void 0?void 0:B.inputElRef;J&&(J.value=String(A||"")),x.value===A&&D()}else D();const{onBlur:M}=e,{nTriggerFormBlur:Y}=a;M&&call(M,k),Y(),nextTick(()=>{D()})}function se(k){const{onClear:O}=e;O&&call(O,k)}function Z(){const{value:k}=z;if(!k){le();return}const{value:O}=x;if(O===null)e.validator||S(ge());else{const{value:B}=E;$({offset:B,doUpdateIfValid:!0,isInputing:!1,fixPrecision:!0})}}function q(){const{value:k}=K;if(!k){U();return}const{value:O}=x;if(O===null)e.validator||S(ge());else{const{value:B}=E;$({offset:-B,doUpdateIfValid:!0,isInputing:!1,fixPrecision:!0})}}const ne=ee,he=ae;function ge(){if(e.validator)return null;const{value:k}=P,{value:O}=F;return k!==null?Math.max(0,k):O!==null?Math.min(0,O):0}function ve(k){se(k),S(null)}function te(k){var O,B,A;!((O=m.value)===null||O===void 0)&&O.$el.contains(k.target)&&k.preventDefault(),!((B=f.value)===null||B===void 0)&&B.$el.contains(k.target)&&k.preventDefault(),(A=u.value)===null||A===void 0||A.activate()}let Q=null,G=null,oe=null;function U(){oe&&(window.clearTimeout(oe),oe=null),Q&&(window.clearInterval(Q),Q=null)}function le(){ce&&(window.clearTimeout(ce),ce=null),G&&(window.clearInterval(G),G=null)}function $e(){U(),oe=window.setTimeout(()=>{Q=window.setInterval(()=>{q()},HOLDING_CHANGE_INTERVAL)},HOLDING_CHANGE_THRESHOLD),on("mouseup",document,U,{once:!0})}let ce=null;function Ie(){le(),ce=window.setTimeout(()=>{G=window.setInterval(()=>{Z()},HOLDING_CHANGE_INTERVAL)},HOLDING_CHANGE_THRESHOLD),on("mouseup",document,le,{once:!0})}const De=()=>{G||Z()},b=()=>{Q||q()};function w(k){var O,B;if(k.key==="Enter"){if(k.target===((O=u.value)===null||O===void 0?void 0:O.wrapperElRef))return;$({offset:0,doUpdateIfValid:!0,isInputing:!1,fixPrecision:!0})!==!1&&((B=u.value)===null||B===void 0||B.deactivate())}else if(k.key==="ArrowUp"){if(!z.value||e.keyboard.ArrowUp===!1)return;k.preventDefault(),$({offset:0,doUpdateIfValid:!0,isInputing:!1,fixPrecision:!0})!==!1&&Z()}else if(k.key==="ArrowDown"){if(!K.value||e.keyboard.ArrowDown===!1)return;k.preventDefault(),$({offset:0,doUpdateIfValid:!0,isInputing:!1,fixPrecision:!0})!==!1&&q()}}function R(k){C.value=k,e.updateValueOnInput&&!e.format&&!e.parse&&e.precision===void 0&&$({offset:0,doUpdateIfValid:!0,isInputing:!0,fixPrecision:!1})}watch(x,()=>{D()});const H={focus:()=>{var k;return(k=u.value)===null||k===void 0?void 0:k.focus()},blur:()=>{var k;return(k=u.value)===null||k===void 0?void 0:k.blur()},select:()=>{var k;return(k=u.value)===null||k===void 0?void 0:k.select()}},L=useRtl("InputNumber",r,o);return Object.assign(Object.assign({},H),{rtlEnabled:L,inputInstRef:u,minusButtonInstRef:f,addButtonInstRef:m,mergedClsPrefix:o,mergedBordered:t,uncontrolledValue:v,mergedValue:x,mergedPlaceholder:N,displayedValueInvalid:j,mergedSize:l,mergedDisabled:s,displayedValue:C,addable:z,minusable:K,mergedStatus:d,handleFocus:ne,handleBlur:he,handleClear:ve,handleMouseDown:te,handleAddClick:De,handleMinusClick:b,handleAddMousedown:Ie,handleMinusMousedown:$e,handleKeyDown:w,handleUpdateDisplayedValue:R,mergedTheme:n,inputThemeOverrides:{paddingSmall:"0 8px 0 10px",paddingMedium:"0 8px 0 12px",paddingLarge:"0 8px 0 14px"},buttonThemeOverrides:computed(()=>{const{self:{iconColorDisabled:k}}=n.value,[O,B,A,M]=rgba(k);return{textColorTextDisabled:`rgb(${O}, ${B}, ${A})`,opacityDisabled:`${M}`}})})},render(){const{mergedClsPrefix:e,$slots:t}=this,o=()=>h(XButton,{text:!0,disabled:!this.minusable||this.mergedDisabled||this.readonly,focusable:!1,theme:this.mergedTheme.peers.Button,themeOverrides:this.mergedTheme.peerOverrides.Button,builtinThemeOverrides:this.buttonThemeOverrides,onClick:this.handleMinusClick,onMousedown:this.handleMinusMousedown,ref:"minusButtonInstRef"},{icon:()=>resolveSlot(t["minus-icon"],()=>[h(NBaseIcon,{clsPrefix:e},{default:()=>h(RemoveIcon,null)})])}),r=()=>h(XButton,{text:!0,disabled:!this.addable||this.mergedDisabled||this.readonly,focusable:!1,theme:this.mergedTheme.peers.Button,themeOverrides:this.mergedTheme.peerOverrides.Button,builtinThemeOverrides:this.buttonThemeOverrides,onClick:this.handleAddClick,onMousedown:this.handleAddMousedown,ref:"addButtonInstRef"},{icon:()=>resolveSlot(t["add-icon"],()=>[h(NBaseIcon,{clsPrefix:e},{default:()=>h(AddIcon,null)})])});return h("div",{class:[`${e}-input-number`,this.rtlEnabled&&`${e}-input-number--rtl`]},h(__unplugin_components_0$1,{ref:"inputInstRef",autofocus:this.autofocus,status:this.mergedStatus,bordered:this.mergedBordered,loading:this.loading,value:this.displayedValue,onUpdateValue:this.handleUpdateDisplayedValue,theme:this.mergedTheme.peers.Input,themeOverrides:this.mergedTheme.peerOverrides.Input,builtinThemeOverrides:this.inputThemeOverrides,size:this.mergedSize,placeholder:this.mergedPlaceholder,disabled:this.mergedDisabled,readonly:this.readonly,textDecoration:this.displayedValueInvalid?"line-through":void 0,onFocus:this.handleFocus,onBlur:this.handleBlur,onKeydown:this.handleKeyDown,onMousedown:this.handleMouseDown,onClear:this.handleClear,clearable:this.clearable,internalLoadingBeforeSuffix:!0},{prefix:()=>{var n;return this.showButton&&this.buttonPlacement==="both"?[o(),resolveWrappedSlot(t.prefix,i=>i?h("span",{class:`${e}-input-number-prefix`},i):null)]:(n=t.prefix)===null||n===void 0?void 0:n.call(t)},suffix:()=>{var n;return this.showButton?[resolveWrappedSlot(t.suffix,i=>i?h("span",{class:`${e}-input-number-suffix`},i):null),this.buttonPlacement==="right"?o():null,r()]:(n=t.suffix)===null||n===void 0?void 0:n.call(t)}}))}}),commonVariables={extraFontSize:"12px",width:"440px"},transferDark={name:"Transfer",common:commonDark,peers:{Checkbox:checkboxDark$1,Scrollbar:scrollbarDark$1,Input:inputDark$1,Empty:emptyDark$1,Button:buttonDark$1},self(e){const{iconColorDisabled:t,iconColor:o,fontWeight:r,fontSizeLarge:n,fontSizeMedium:i,fontSizeSmall:a,heightLarge:l,heightMedium:s,heightSmall:d,borderRadius:u,inputColor:f,tableHeaderColor:m,textColor1:v,textColorDisabled:g,textColor2:x,hoverColor:C}=e;return Object.assign(Object.assign({},commonVariables),{itemHeightSmall:d,itemHeightMedium:s,itemHeightLarge:l,fontSizeSmall:a,fontSizeMedium:i,fontSizeLarge:n,borderRadius:u,borderColor:"#0000",listColor:f,headerColor:m,titleTextColor:v,titleTextColorDisabled:g,extraTextColor:x,filterDividerColor:"#0000",itemTextColor:x,itemTextColorDisabled:g,itemColorPending:C,titleFontWeight:r,iconColor:o,iconColorDisabled:t})}},legacyTransferDark=transferDark,messageProps={icon:Function,type:{type:String,default:"info"},content:[String,Number,Function],showIcon:{type:Boolean,default:!0},closable:Boolean,keepAliveOnHover:Boolean,onClose:Function,onMouseenter:Function,onMouseleave:Function},messageApiInjectionKey="n-message-api",messageProviderInjectionKey="n-message-provider",style$2=c([cB("message-wrapper",`
 margin: var(--n-margin);
 z-index: 0;
 transform-origin: top center;
 display: flex;
 `,[fadeInHeightExpandTransition({overflow:"visible",originalTransition:"transform .3s var(--n-bezier)",enterToProps:{transform:"scale(1)"},leaveToProps:{transform:"scale(0.85)"}})]),cB("message",`
 box-sizing: border-box;
 display: flex;
 align-items: center;
 transition:
 color .3s var(--n-bezier),
 box-shadow .3s var(--n-bezier),
 background-color .3s var(--n-bezier),
 opacity .3s var(--n-bezier),
 transform .3s var(--n-bezier),
 margin-bottom .3s var(--n-bezier);
 padding: var(--n-padding);
 border-radius: var(--n-border-radius);
 flex-wrap: nowrap;
 overflow: hidden;
 max-width: var(--n-max-width);
 color: var(--n-text-color);
 background-color: var(--n-color);
 box-shadow: var(--n-box-shadow);
 `,[cE("content",`
 display: inline-block;
 line-height: var(--n-line-height);
 font-size: var(--n-font-size);
 `),cE("icon",`
 position: relative;
 margin: var(--n-icon-margin);
 height: var(--n-icon-size);
 width: var(--n-icon-size);
 font-size: var(--n-icon-size);
 flex-shrink: 0;
 `,[["default","info","success","warning","error","loading"].map(e=>cM(`${e}-type`,[c("> *",`
 color: var(--n-icon-color-${e});
 transition: color .3s var(--n-bezier);
 `)])),c("> *",`
 position: absolute;
 left: 0;
 top: 0;
 right: 0;
 bottom: 0;
 `,[iconSwitchTransition()])]),cE("close",`
 margin: var(--n-close-margin);
 transition:
 background-color .3s var(--n-bezier),
 color .3s var(--n-bezier);
 flex-shrink: 0;
 `,[c("&:hover",`
 color: var(--n-close-icon-color-hover);
 `),c("&:active",`
 color: var(--n-close-icon-color-pressed);
 `)])]),cB("message-container",`
 z-index: 6000;
 position: fixed;
 height: 0;
 overflow: visible;
 display: flex;
 flex-direction: column;
 align-items: center;
 `,[cM("top",`
 top: 12px;
 left: 0;
 right: 0;
 `),cM("top-left",`
 top: 12px;
 left: 12px;
 right: 0;
 align-items: flex-start;
 `),cM("top-right",`
 top: 12px;
 left: 0;
 right: 12px;
 align-items: flex-end;
 `),cM("bottom",`
 bottom: 4px;
 left: 0;
 right: 0;
 justify-content: flex-end;
 `),cM("bottom-left",`
 bottom: 4px;
 left: 12px;
 right: 0;
 justify-content: flex-end;
 align-items: flex-start;
 `),cM("bottom-right",`
 bottom: 4px;
 left: 0;
 right: 12px;
 justify-content: flex-end;
 align-items: flex-end;
 `)])]),iconRenderMap={info:()=>h(InfoIcon,null),success:()=>h(SuccessIcon,null),warning:()=>h(WarningIcon,null),error:()=>h(ErrorIcon,null),default:()=>null},NMessage=defineComponent({name:"Message",props:Object.assign(Object.assign({},messageProps),{render:Function}),setup(e){const{inlineThemeDisabled:t,mergedRtlRef:o}=useConfig(e),{props:r,mergedClsPrefixRef:n}=inject(messageProviderInjectionKey),i=useRtl("Message",o,n),a=useTheme("Message","-message",style$2,messageLight,r,n),l=computed(()=>{const{type:d}=e,{common:{cubicBezierEaseInOut:u},self:{padding:f,margin:m,maxWidth:v,iconMargin:g,closeMargin:x,closeSize:C,iconSize:y,fontSize:I,lineHeight:N,borderRadius:E,iconColorInfo:P,iconColorSuccess:F,iconColorWarning:S,iconColorError:$,iconColorLoading:D,closeIconSize:j,closeBorderRadius:K,[createKey("textColor",d)]:z,[createKey("boxShadow",d)]:ee,[createKey("color",d)]:ae,[createKey("closeColorHover",d)]:se,[createKey("closeColorPressed",d)]:Z,[createKey("closeIconColor",d)]:q,[createKey("closeIconColorPressed",d)]:ne,[createKey("closeIconColorHover",d)]:he}}=a.value;return{"--n-bezier":u,"--n-margin":m,"--n-padding":f,"--n-max-width":v,"--n-font-size":I,"--n-icon-margin":g,"--n-icon-size":y,"--n-close-icon-size":j,"--n-close-border-radius":K,"--n-close-size":C,"--n-close-margin":x,"--n-text-color":z,"--n-color":ae,"--n-box-shadow":ee,"--n-icon-color-info":P,"--n-icon-color-success":F,"--n-icon-color-warning":S,"--n-icon-color-error":$,"--n-icon-color-loading":D,"--n-close-color-hover":se,"--n-close-color-pressed":Z,"--n-close-icon-color":q,"--n-close-icon-color-pressed":ne,"--n-close-icon-color-hover":he,"--n-line-height":N,"--n-border-radius":E}}),s=t?useThemeClass("message",computed(()=>e.type[0]),l,{}):void 0;return{mergedClsPrefix:n,rtlEnabled:i,messageProviderProps:r,handleClose(){var d;(d=e.onClose)===null||d===void 0||d.call(e)},cssVars:t?void 0:l,themeClass:s==null?void 0:s.themeClass,onRender:s==null?void 0:s.onRender,placement:r.placement}},render(){const{render:e,type:t,closable:o,content:r,mergedClsPrefix:n,cssVars:i,themeClass:a,onRender:l,icon:s,handleClose:d,showIcon:u}=this;l==null||l();let f;return h("div",{class:[`${n}-message-wrapper`,a],onMouseenter:this.onMouseenter,onMouseleave:this.onMouseleave,style:[{alignItems:this.placement.startsWith("top")?"flex-start":"flex-end"},i]},e?e(this.$props):h("div",{class:[`${n}-message ${n}-message--${t}-type`,this.rtlEnabled&&`${n}-message--rtl`]},(f=createIconVNode(s,t,n))&&u?h("div",{class:`${n}-message__icon ${n}-message__icon--${t}-type`},h(NIconSwitchTransition,null,{default:()=>f})):null,h("div",{class:`${n}-message__content`},render$1(r)),o?h(NBaseClose,{clsPrefix:n,class:`${n}-message__close`,onClick:d,absolute:!0}):null))}});function createIconVNode(e,t,o){if(typeof e=="function")return e();{const r=t==="loading"?h(NBaseLoading,{clsPrefix:o,strokeWidth:24,scale:.85}):iconRenderMap[t]();return r?h(NBaseIcon,{clsPrefix:o,key:t},{default:()=>r}):null}}const MessageEnvironment=defineComponent({name:"MessageEnvironment",props:Object.assign(Object.assign({},messageProps),{duration:{type:Number,default:3e3},onAfterLeave:Function,onLeave:Function,internalKey:{type:String,required:!0},onInternalAfterLeave:Function,onHide:Function,onAfterHide:Function}),setup(e){let t=null;const o=ref(!0);onMounted(()=>{r()});function r(){const{duration:u}=e;u&&(t=window.setTimeout(a,u))}function n(u){u.currentTarget===u.target&&t!==null&&(window.clearTimeout(t),t=null)}function i(u){u.currentTarget===u.target&&r()}function a(){const{onHide:u}=e;o.value=!1,t&&(window.clearTimeout(t),t=null),u&&u()}function l(){const{onClose:u}=e;u&&u(),a()}function s(){const{onAfterLeave:u,onInternalAfterLeave:f,onAfterHide:m,internalKey:v}=e;u&&u(),f&&f(v),m&&m()}function d(){a()}return{show:o,hide:a,handleClose:l,handleAfterLeave:s,handleMouseleave:i,handleMouseenter:n,deactivate:d}},render(){return h(NFadeInExpandTransition,{appear:!0,onAfterLeave:this.handleAfterLeave,onLeave:this.onLeave},{default:()=>[this.show?h(NMessage,{content:this.content,type:this.type,icon:this.icon,showIcon:this.showIcon,closable:this.closable,onClose:this.handleClose,onMouseenter:this.keepAliveOnHover?this.handleMouseenter:void 0,onMouseleave:this.keepAliveOnHover?this.handleMouseleave:void 0}):null]})}}),messageProviderProps=Object.assign(Object.assign({},useTheme.props),{to:[String,Object],duration:{type:Number,default:3e3},keepAliveOnHover:Boolean,max:Number,placement:{type:String,default:"top"},closable:Boolean,containerClass:String,containerStyle:[String,Object]}),__unplugin_components_0=defineComponent({name:"MessageProvider",props:messageProviderProps,setup(e){const{mergedClsPrefixRef:t}=useConfig(e),o=ref([]),r=ref({}),n={create(s,d){return i(s,Object.assign({type:"default"},d))},info(s,d){return i(s,Object.assign(Object.assign({},d),{type:"info"}))},success(s,d){return i(s,Object.assign(Object.assign({},d),{type:"success"}))},warning(s,d){return i(s,Object.assign(Object.assign({},d),{type:"warning"}))},error(s,d){return i(s,Object.assign(Object.assign({},d),{type:"error"}))},loading(s,d){return i(s,Object.assign(Object.assign({},d),{type:"loading"}))},destroyAll:l};provide(messageProviderInjectionKey,{props:e,mergedClsPrefixRef:t}),provide(messageApiInjectionKey,n);function i(s,d){const u=createId(),f=reactive(Object.assign(Object.assign({},d),{content:s,key:u,destroy:()=>{var v;(v=r.value[u])===null||v===void 0||v.hide()}})),{max:m}=e;return m&&o.value.length>=m&&o.value.shift(),o.value.push(f),f}function a(s){o.value.splice(o.value.findIndex(d=>d.key===s),1),delete r.value[s]}function l(){Object.values(r.value).forEach(s=>{s.hide()})}return Object.assign({mergedClsPrefix:t,messageRefs:r,messageList:o,handleAfterLeave:a},n)},render(){var e,t,o;return h(Fragment,null,(t=(e=this.$slots).default)===null||t===void 0?void 0:t.call(e),this.messageList.length?h(Teleport,{to:(o=this.to)!==null&&o!==void 0?o:"body"},h("div",{class:[`${this.mergedClsPrefix}-message-container`,`${this.mergedClsPrefix}-message-container--${this.placement}`,this.containerClass],key:"message-container",style:this.containerStyle},this.messageList.map(r=>h(MessageEnvironment,Object.assign({ref:n=>{n&&(this.messageRefs[r.key]=n)},internalKey:r.key,onInternalAfterLeave:this.handleAfterLeave},omit(r,["destroy"],void 0),{duration:r.duration===void 0?this.duration:r.duration,keepAliveOnHover:r.keepAliveOnHover===void 0?this.keepAliveOnHover:r.keepAliveOnHover,closable:r.closable===void 0?this.closable:r.closable}))))):null)}});function useMessage(){const e=inject(messageApiInjectionKey,null);return e===null&&throwError("use-message","No outer <n-message-provider /> founded. See prerequisite in https://www.naiveui.com/en-US/os-theme/components/message for more details. If you want to use `useMessage` outside setup, please check https://www.naiveui.com/zh-CN/os-theme/components/message#Q-&-A."),e}const qrcodeDark={name:"QrCode",common:commonDark,self:e=>({borderRadius:e.borderRadius})},qrcodeDark$1=qrcodeDark,skeletonDark={name:"Skeleton",common:commonDark,self(e){const{heightSmall:t,heightMedium:o,heightLarge:r,borderRadius:n}=e;return{color:"rgba(255, 255, 255, 0.12)",colorEnd:"rgba(255, 255, 255, 0.18)",borderRadius:n,heightSmall:t,heightMedium:o,heightLarge:r}}},style$1=c([c("@keyframes spin-rotate",`
 from {
 transform: rotate(0);
 }
 to {
 transform: rotate(360deg);
 }
 `),cB("spin-container",{position:"relative"},[cB("spin-body",`
 position: absolute;
 top: 50%;
 left: 50%;
 transform: translateX(-50%) translateY(-50%);
 `,[fadeInTransition()])]),cB("spin-body",`
 display: inline-flex;
 align-items: center;
 justify-content: center;
 flex-direction: column;
 `),cB("spin",`
 display: inline-flex;
 height: var(--n-size);
 width: var(--n-size);
 font-size: var(--n-size);
 color: var(--n-color);
 `,[cM("rotate",`
 animation: spin-rotate 2s linear infinite;
 `)]),cB("spin-description",`
 display: inline-block;
 font-size: var(--n-font-size);
 color: var(--n-text-color);
 transition: color .3s var(--n-bezier);
 margin-top: 8px;
 `),cB("spin-content",`
 opacity: 1;
 transition: opacity .3s var(--n-bezier);
 pointer-events: all;
 `,[cM("spinning",`
 user-select: none;
 -webkit-user-select: none;
 pointer-events: none;
 opacity: var(--n-opacity-spinning);
 `)])]),STROKE_WIDTH={small:20,medium:18,large:16},spinProps=Object.assign(Object.assign({},useTheme.props),{contentClass:String,contentStyle:[Object,String],description:String,stroke:String,size:{type:[String,Number],default:"medium"},show:{type:Boolean,default:!0},strokeWidth:Number,rotate:{type:Boolean,default:!0},spinning:{type:Boolean,validator:()=>!0,default:void 0},delay:Number}),__unplugin_components_3=defineComponent({name:"Spin",props:spinProps,setup(e){const{mergedClsPrefixRef:t,inlineThemeDisabled:o}=useConfig(e),r=useTheme("Spin","-spin",style$1,spinLight,e,t),n=computed(()=>{const{size:s}=e,{common:{cubicBezierEaseInOut:d},self:u}=r.value,{opacitySpinning:f,color:m,textColor:v}=u,g=typeof s=="number"?pxfy(s):u[createKey("size",s)];return{"--n-bezier":d,"--n-opacity-spinning":f,"--n-size":g,"--n-color":m,"--n-text-color":v}}),i=o?useThemeClass("spin",computed(()=>{const{size:s}=e;return typeof s=="number"?String(s):s[0]}),n,e):void 0,a=useCompitable(e,["spinning","show"]),l=ref(!1);return watchEffect(s=>{let d;if(a.value){const{delay:u}=e;if(u){d=window.setTimeout(()=>{l.value=!0},u),s(()=>{clearTimeout(d)});return}}l.value=a.value}),{mergedClsPrefix:t,active:l,mergedStrokeWidth:computed(()=>{const{strokeWidth:s}=e;if(s!==void 0)return s;const{size:d}=e;return STROKE_WIDTH[typeof d=="number"?"medium":d]}),cssVars:o?void 0:n,themeClass:i==null?void 0:i.themeClass,onRender:i==null?void 0:i.onRender}},render(){var e,t;const{$slots:o,mergedClsPrefix:r,description:n}=this,i=o.icon&&this.rotate,a=(n||o.description)&&h("div",{class:`${r}-spin-description`},n||((e=o.description)===null||e===void 0?void 0:e.call(o))),l=o.icon?h("div",{class:[`${r}-spin-body`,this.themeClass]},h("div",{class:[`${r}-spin`,i&&`${r}-spin--rotate`],style:o.default?"":this.cssVars},o.icon()),a):h("div",{class:[`${r}-spin-body`,this.themeClass]},h(NBaseLoading,{clsPrefix:r,style:o.default?"":this.cssVars,stroke:this.stroke,"stroke-width":this.mergedStrokeWidth,class:`${r}-spin`}),a);return(t=this.onRender)===null||t===void 0||t.call(this),o.default?h("div",{class:[`${r}-spin-container`,this.themeClass],style:this.cssVars},h("div",{class:[`${r}-spin-content`,this.active&&`${r}-spin-content--spinning`,this.contentClass],style:this.contentStyle},o),h(Transition,{name:"fade-in-transition"},{default:()=>this.active?l:null})):l}}),splitDark={name:"Split",common:commonDark},splitDark$1=splitDark,style=cB("text",`
 transition: color .3s var(--n-bezier);
 color: var(--n-text-color);
`,[cM("strong",`
 font-weight: var(--n-font-weight-strong);
 `),cM("italic",{fontStyle:"italic"}),cM("underline",{textDecoration:"underline"}),cM("code",`
 line-height: 1.4;
 display: inline-block;
 font-family: var(--n-font-famliy-mono);
 transition: 
 color .3s var(--n-bezier),
 border-color .3s var(--n-bezier),
 background-color .3s var(--n-bezier);
 box-sizing: border-box;
 padding: .05em .35em 0 .35em;
 border-radius: var(--n-code-border-radius);
 font-size: .9em;
 color: var(--n-code-text-color);
 background-color: var(--n-code-color);
 border: var(--n-code-border);
 `)]),textProps=Object.assign(Object.assign({},useTheme.props),{code:Boolean,type:{type:String,default:"default"},delete:Boolean,strong:Boolean,italic:Boolean,underline:Boolean,depth:[String,Number],tag:String,as:{type:String,validator:()=>!0,default:void 0}}),__unplugin_components_2=defineComponent({name:"Text",props:textProps,setup(e){const{mergedClsPrefixRef:t,inlineThemeDisabled:o}=useConfig(e),r=useTheme("Typography","-text",style,typographyLight,e,t),n=computed(()=>{const{depth:a,type:l}=e,s=l==="default"?a===void 0?"textColor":`textColor${a}Depth`:createKey("textColor",l),{common:{fontWeightStrong:d,fontFamilyMono:u,cubicBezierEaseInOut:f},self:{codeTextColor:m,codeBorderRadius:v,codeColor:g,codeBorder:x,[s]:C}}=r.value;return{"--n-bezier":f,"--n-text-color":C,"--n-font-weight-strong":d,"--n-font-famliy-mono":u,"--n-code-border-radius":v,"--n-code-text-color":m,"--n-code-color":g,"--n-code-border":x}}),i=o?useThemeClass("text",computed(()=>`${e.type[0]}${e.depth||""}`),n,e):void 0;return{mergedClsPrefix:t,compitableTag:useCompitable(e,["as","tag"]),cssVars:o?void 0:n,themeClass:i==null?void 0:i.themeClass,onRender:i==null?void 0:i.onRender}},render(){var e,t,o;const{mergedClsPrefix:r}=this;(e=this.onRender)===null||e===void 0||e.call(this);const n=[`${r}-text`,this.themeClass,{[`${r}-text--code`]:this.code,[`${r}-text--delete`]:this.delete,[`${r}-text--strong`]:this.strong,[`${r}-text--italic`]:this.italic,[`${r}-text--underline`]:this.underline}],i=(o=(t=this.$slots).default)===null||o===void 0?void 0:o.call(t);return this.code?h("code",{class:n,style:this.cssVars},this.delete?h("del",null,i):i):this.delete?h("del",{class:n,style:this.cssVars},i):h(this.compitableTag||"span",{class:n,style:this.cssVars},i)}}),self$1=()=>({}),equationDark={name:"Equation",common:commonDark,self:self$1},equationDark$1=equationDark,darkTheme={name:"dark",common:commonDark,Alert:alertDark$1,Anchor:anchorDark$1,AutoComplete:autoCompleteDark$1,Avatar:avatarDark$1,AvatarGroup:avatarGroupDark$1,BackTop:backTopDark$1,Badge:badgeDark$1,Breadcrumb:breadcrumbDark$1,Button:buttonDark$1,ButtonGroup:buttonGroupDark$1,Calendar:calendarDark$1,Card:cardDark$1,Carousel:carouselDark$1,Cascader:cascaderDark$1,Checkbox:checkboxDark$1,Code:codeDark$1,Collapse:collapseDark$1,CollapseTransition:collapseTransitionDark$1,ColorPicker:colorPickerDark$1,DataTable:dataTableDark$1,DatePicker:datePickerDark$1,Descriptions:descriptionsDark$1,Dialog:dialogDark$1,Divider:dividerDark$1,Drawer:drawerDark$1,Dropdown:dropdownDark$1,DynamicInput:dynamicInputDark$1,DynamicTags:dynamicTagsDark$1,Element:elementDark$1,Empty:emptyDark$1,Ellipsis:ellipsisDark$1,Equation:equationDark$1,Form:formDark,GradientText:gradientTextDark$1,Icon:iconDark$2,IconWrapper:iconWrapperDark,Image:imageDark,Input:inputDark$1,InputNumber:inputNumberDark$1,LegacyTransfer:legacyTransferDark,Layout:layoutDark$1,List:listDark$2,LoadingBar:loadingBarDark$1,Log:logDark$1,Menu:menuDark$1,Mention:mentionDark,Message:messageDark$1,Modal:modalDark$1,Notification:notificationDark$1,PageHeader:pageHeaderDark,Pagination:paginationDark$1,Popconfirm:popconfirmDark$1,Popover:popoverDark$1,Popselect:popselectDark,Progress:progressDark$1,QrCode:qrcodeDark$1,Radio:radioDark$1,Rate:rateDark$1,Result:resultDark$1,Row:rowDark$1,Scrollbar:scrollbarDark$1,Select:selectDark$1,Skeleton:skeletonDark,Slider:sliderDark$1,Space:spaceDark$1,Spin:spinDark$1,Statistic:statisticDark$1,Steps:stepsDark$1,Switch:switchDark$1,Table:tableDark$1,Tabs:tabsDark$1,Tag:tagDark$1,Thing:thingDark$1,TimePicker:timePickerDark$1,Timeline:timelineDark$1,Tooltip:tooltipDark$1,Transfer:transferDark$2,Tree:treeDark$1,TreeSelect:treeSelectDark$1,Typography:typographyDark$1,Upload:uploadDark$1,Watermark:watermarkDark$1,Split:splitDark$1};class Evt{static on(t,o,r=!1){const n=this.evts[t]||[];r&&(n.length=0),n.push(o),this.evts[t]=n}static emit(t,...o){const r=this.evts[t];r&&r.forEach(n=>n.exec(o))}static off(t){delete this.evts[t]}}ze(Evt,"evts",{});window.Evt=Evt;const Ke=class Ke{constructor(){ze(this,"_context");ze(this,"_method",null);ze(this,"args",null);ze(this,"_once",!1);ze(this,"_id",0)}doExec(t,o,r){let n=null;try{n=o.apply(t,r)}catch{r?n=o(...r):n=o()}return n}get isOnce(){return this._once||!this.context}exec(t){if(!this._method)return null;const o=this._id,n=t==null?this.args:(this.args||t.unshift)&&this.args?this.args.concat(t):t,i=this.doExec(this._context,this._method,n);return this._id===o&&this.isOnce,i}set(t,o,r,n,i){let a=this;return a._id=t,a._context=o,a._method=r,a.args=n,a._once=i,a}get method(){return this._method}get context(){return this._context}static alloc(t,o,r,n){return new Ke().set(Ke._guid++,t,o,r||[],!!n)}};ze(Ke,"_guid",1);let Handler=Ke;window.Handler=Handler;const _sfc_main$6=defineComponent({__name:"index",props:{action:{}},setup(e){const t=e;onMounted(()=>{});const o=(i,a)=>{let l="";return t.action&&((t.action.length>2&&a==t.action.length-1||t.action.length==1)&&(l+=" bottom-left-radius bottom-right-radius"),t.action.length==2?(l+=a?" bottom-right-radius":" bottom-left-radius",a===0&&(l+=" cs-t1")):a>0&&(l+=" cs-t0"),t.action.length===2&&(l+=" button-group-half")),l},r=useOsTheme(),n=ref(r.value==="dark");return Evt.on("THEME_CHANGE",Handler.alloc(null,i=>{n.value=i==="dark"}),!1),watchEffect(()=>{document.documentElement.style.setProperty("--default-btn-color",n.value?"rgb(255,252,252)":"#060606")}),(i,a)=>{const l=NButton;return openBlock(),createElementBlock("div",{class:normalizeClass(["container bottom-left-radius bottom-right-radius",t.action.length>0?"cs-t0":""]),style:{width:"100%"}},[(openBlock(!0),createElementBlock(Fragment,null,renderList(t.action,(s,d)=>(openBlock(),createBlock(l,{quaternary:"",class:normalizeClass(["button-group",o(s,d)]),type:s.type||"default",disabled:s.disable,onClick:s.callback},{default:withCtx(()=>[createTextVNode(toDisplayString(s.label),1)]),_:2},1032,["class","type","disabled","onClick"]))),256))],2)}}}),_export_sfc=(e,t)=>{const o=e.__vccOpts||e;for(const[r,n]of t)o[r]=n;return o},ButtonGroup=_export_sfc(_sfc_main$6,[["__scopeId","data-v-ea45a382"]]),_sfc_main$5=defineComponent({__name:"dialog-api",setup(__props){const osTheme=useOsTheme(),isDark=ref(osTheme.value==="dark");Evt.on("THEME_CHANGE",Handler.alloc(null,e=>{isDark.value=e==="dark"}),!1),watchEffect(()=>{document.documentElement.style.setProperty("--dialog-bg-color",isDark.value?"rgba(33, 32, 32, 0.63)":"rgba(255,255,255,0.6)"),document.documentElement.style.setProperty("--button-group-border-color",isDark.value?"hsla(240, 1%, 72%, 0.25)":"hsla(240,2%,18%,0.25)"),document.documentElement.style.setProperty("--button-group-hover-color",isDark.value?"hsla(240, 1%, 72%, 0.2)":"hsla(0,0%,6%,0.2)")}),window.$dialog=useDialog(),window.$dialogReactive=useDialogReactiveList();const dialogList=useDialogReactiveList();return watch(dialogList.value,e=>{for(let t=0;t<e.length-1;t++)e[t].destroy()},{immediate:!0}),window.sampleNotify=args=>{let arg=null;try{arg=JSON.parse(args)}catch(e){return}arg&&(typeof arg.type>"u"&&(arg.type="info"),typeof arg.maskClosable>"u"&&(arg.maskClosable=!0),arg.action&&arg.action.length?arg.action.forEach(act=>{const str=act.callback;str&&(act.callback=()=>eval(str))}):arg.action=[{label:"关闭",type:"default",callback:"dlg.destroy()"}],window.dlg=window.$dialog[arg.type]({title:arg.title||"",closable:!1,maskClosable:arg.maskClosable,content:()=>arg.msg||"",action:()=>h(ButtonGroup,{action:arg.action})}))},(e,t)=>(openBlock(),createElementBlock("div"))}}),_sfc_main$4=defineComponent({__name:"message-api",setup(e){return window.$message=useMessage(),(t,o)=>(openBlock(),createElementBlock("div"))}});var GameMsg=(e=>(e[e.LOGIC_INJECT_DONE=0]="LOGIC_INJECT_DONE",e))(GameMsg||{});const useGameStore=defineStore("game",()=>{ref(!1);const e=ref(!1),t=ref();function o(){e.value=!0}function r(l){switch(console.log("onMsg",l),l){case GameMsg.LOGIC_INJECT_DONE:o();break}}function n(){if(t.value)return;const l=window.DASHBOARD_EXEC("localStorage.getItem('_dashboard_keys')")||"";l&&(t.value=JSON.parse(l))}function i(l){return t.value.find(s=>s.name==l)}window.ON_UNKNOWN_KEY=l=>{n();const s=l.replace("KEY_CODE_",""),d=i(s);if(d)switch(d.sort){case 4:Evt.emit("EVT_OPEN_MAP_TOOL");break;case 6:Evt.emit("EVT_BACK_TO_CITY");break}};const a=ref([]);return{inject:e,onMsg:r,funcs:a}}),_hoisted_1$3={id:"gem-div"},_sfc_main$3=defineComponent({__name:"kill-boss",props:{call:{default:null}},setup(e){const t=ref(""),o=e;return onMounted(()=>{o.call.value=()=>{let r=Number(t.value);if(isNaN(r)||r<=0)return void window.$message.error("请输入正确的id");window._world.toBattle(r)}}),(r,n)=>{const i=__unplugin_components_0$1,a=__unplugin_components_1;return openBlock(),createElementBlock("div",_hoisted_1$3,[createVNode(a,{class:"line-space"},{default:withCtx(()=>[createVNode(i,{value:unref(t),"onUpdate:value":n[0]||(n[0]=l=>isRef(t)?t.value=l:null),size:"tiny",placeholder:"请输入要击杀的boss"},null,8,["value"])]),_:1})])}}}),KillBoss=_export_sfc(_sfc_main$3,[["__scopeId","data-v-341baa98"]]),_hoisted_1$2={id:"gem-div"},_sfc_main$2=defineComponent({__name:"da-mi-jing",props:{callback:{default:null}},setup(e){const t=ref(!1),o=ref(0),r=ref(5);onMounted(()=>{n()});const n=async()=>{t.value=!0;const i=await window.subWindow.mj.loadInfo();t.value=!1,o.value=i.unGet};return(i,a)=>{const l=__unplugin_components_2,s=__unplugin_components_1,d=__unplugin_components_2$1,u=__unplugin_components_3;return openBlock(),createElementBlock("div",_hoisted_1$2,[createVNode(u,{show:unref(t),delay:1e3},{default:withCtx(()=>[createVNode(s,{class:"line-space"},{default:withCtx(()=>[createVNode(l,null,{default:withCtx(()=>[createTextVNode("剩余秘境币：")]),_:1}),createVNode(l,null,{default:withCtx(()=>[createTextVNode(toDisplayString(unref(o)),1)]),_:1})]),_:1}),createVNode(s,{class:"line-space"},{default:withCtx(()=>[createVNode(l,null,{default:withCtx(()=>[createTextVNode("层数选择")]),_:1}),createVNode(d,{value:unref(r),"onUpdate:value":a[0]||(a[0]=f=>isRef(r)?r.value=f:null),min:"1",max:"25",size:"small"},null,8,["value"])]),_:1})]),_:1},8,["show"])])}}}),DaMiJing=_export_sfc(_sfc_main$2,[["__scopeId","data-v-852cf801"]]),_hoisted_1$1={class:"c-b-s"},_sfc_main$1=defineComponent({__name:"function",props:{data:{default:{}},index:{default:0}},setup(__props){const osTheme=useOsTheme(),game=useGameStore();watch(osTheme,e=>{},{immediate:!0});const props=__props;onMounted(()=>{if(props.data.name=="地图工具"){const e=Handler.alloc(null,()=>{exec()});Evt.on("EVT_OPEN_MAP_TOOL",e,!0)}});const exec=()=>{if(props.data.inGame&&window._world&&!window._world.isEnterGame())return void window.$message.error("请先进入游戏");switch(props.data.name){case"单人大秘境":return void showDaMi()}if(!props.data.active&&props.data.group){let e=!1;if(game.funcs.forEach(t=>{if(t.group==props.data.group&&t.active)return e=!0,void window.$message.error(`[ ${t.name} ]运行中,请先关闭.`)}),e)return}const loading=()=>{props.data.loading=!0,setTimeout(()=>{props.data.loading=!1},500)},switchAct=()=>{props.data.act&&(props.data.active=!props.data.active)},logic=dlg=>{try{dlg&&dlg.destroy(),loading(),eval(props.data.code),switchAct()}catch(e){}};if(props.data.act&&props.data.active){logic();return}if(props.data.tip){const e=window.$dialog.info({title:props.data.name,closable:!1,content:()=>h("span",{style:{width:"150px",backgroundColor:"transparent",color:"white"}},{default:()=>props.data.tip}),action:()=>h(ButtonGroup,{action:[{label:"确定",type:"success",callback:()=>logic(e)},{label:"关闭",type:"default",callback:()=>e.destroy()}]})});return}logic()},da_mi_callback=ref(null),showDaMi=()=>{const e=window.$dialog.info({title:props.data.name,maskClosable:!1,closeOnEsc:!1,closable:!1,content:()=>h(DaMiJing,{callback:da_mi_callback}),action:()=>h(ButtonGroup,{action:[{label:"开始",type:"success",callback:()=>{}},{label:"关闭",type:"default",callback:()=>e.destroy()}]})})};return ref(null),ref(null),ref(null),ref(null),ref(null),ref(null),ref(!1),ref(!1),Evt.on("EVT_KILL_BOSS",Handler.alloc(null,()=>{const e=ref(null),t=window.$dialog.info({title:props.data.name,maskClosable:!1,closable:!1,content:()=>h(KillBoss,{call:e}),action:()=>h(ButtonGroup,{action:[{label:"击杀",type:"error",callback:()=>e.value&&e.value()},{label:"关闭",type:"default",callback:()=>t.destroy()}]})})}),!0),ref(),(e,t)=>(openBlock(),createElementBlock("div",_hoisted_1$1,[createVNode(unref(NButton),{onClick:exec,loading:props.data.loading,strong:"",secondary:!props.data.active,type:props.data.active?"success":"error",size:"small"},{default:withCtx(()=>[createTextVNode(toDisplayString(props.data.name),1)]),_:1},8,["loading","secondary","type"])]))}}),Function$1=_export_sfc(_sfc_main$1,[["__scopeId","data-v-a142c50c"]]);var clipboard={exports:{}};/*!
 * clipboard.js v2.0.11
 * https://clipboardjs.com/
 *
 * Licensed MIT © Zeno Rocha
 */(function(e,t){(function(r,n){e.exports=n()})(commonjsGlobal,function(){return function(){var o={686:function(i,a,l){l.d(a,{default:function(){return ve}});var s=l(279),d=l.n(s),u=l(370),f=l.n(u),m=l(817),v=l.n(m);function g(te){try{return document.execCommand(te)}catch{return!1}}var x=function(Q){var G=v()(Q);return g("cut"),G},C=x;function y(te){var Q=document.documentElement.getAttribute("dir")==="rtl",G=document.createElement("textarea");G.style.fontSize="12pt",G.style.border="0",G.style.padding="0",G.style.margin="0",G.style.position="absolute",G.style[Q?"right":"left"]="-9999px";var oe=window.pageYOffset||document.documentElement.scrollTop;return G.style.top="".concat(oe,"px"),G.setAttribute("readonly",""),G.value=te,G}var I=function(Q,G){var oe=y(Q);G.container.appendChild(oe);var U=v()(oe);return g("copy"),oe.remove(),U},N=function(Q){var G=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{container:document.body},oe="";return typeof Q=="string"?oe=I(Q,G):Q instanceof HTMLInputElement&&!["text","search","url","tel","password"].includes(Q==null?void 0:Q.type)?oe=I(Q.value,G):(oe=v()(Q),g("copy")),oe},E=N;function P(te){"@babel/helpers - typeof";return typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?P=function(G){return typeof G}:P=function(G){return G&&typeof Symbol=="function"&&G.constructor===Symbol&&G!==Symbol.prototype?"symbol":typeof G},P(te)}var F=function(){var Q=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},G=Q.action,oe=G===void 0?"copy":G,U=Q.container,le=Q.target,$e=Q.text;if(oe!=="copy"&&oe!=="cut")throw new Error('Invalid "action" value, use either "copy" or "cut"');if(le!==void 0)if(le&&P(le)==="object"&&le.nodeType===1){if(oe==="copy"&&le.hasAttribute("disabled"))throw new Error('Invalid "target" attribute. Please use "readonly" instead of "disabled" attribute');if(oe==="cut"&&(le.hasAttribute("readonly")||le.hasAttribute("disabled")))throw new Error(`Invalid "target" attribute. You can't cut text from elements with "readonly" or "disabled" attributes`)}else throw new Error('Invalid "target" value, use a valid Element');if($e)return E($e,{container:U});if(le)return oe==="cut"?C(le):E(le,{container:U})},S=F;function $(te){"@babel/helpers - typeof";return typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?$=function(G){return typeof G}:$=function(G){return G&&typeof Symbol=="function"&&G.constructor===Symbol&&G!==Symbol.prototype?"symbol":typeof G},$(te)}function D(te,Q){if(!(te instanceof Q))throw new TypeError("Cannot call a class as a function")}function j(te,Q){for(var G=0;G<Q.length;G++){var oe=Q[G];oe.enumerable=oe.enumerable||!1,oe.configurable=!0,"value"in oe&&(oe.writable=!0),Object.defineProperty(te,oe.key,oe)}}function K(te,Q,G){return Q&&j(te.prototype,Q),G&&j(te,G),te}function z(te,Q){if(typeof Q!="function"&&Q!==null)throw new TypeError("Super expression must either be null or a function");te.prototype=Object.create(Q&&Q.prototype,{constructor:{value:te,writable:!0,configurable:!0}}),Q&&ee(te,Q)}function ee(te,Q){return ee=Object.setPrototypeOf||function(oe,U){return oe.__proto__=U,oe},ee(te,Q)}function ae(te){var Q=q();return function(){var oe=ne(te),U;if(Q){var le=ne(this).constructor;U=Reflect.construct(oe,arguments,le)}else U=oe.apply(this,arguments);return se(this,U)}}function se(te,Q){return Q&&($(Q)==="object"||typeof Q=="function")?Q:Z(te)}function Z(te){if(te===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return te}function q(){if(typeof Reflect>"u"||!Reflect.construct||Reflect.construct.sham)return!1;if(typeof Proxy=="function")return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],function(){})),!0}catch{return!1}}function ne(te){return ne=Object.setPrototypeOf?Object.getPrototypeOf:function(G){return G.__proto__||Object.getPrototypeOf(G)},ne(te)}function he(te,Q){var G="data-clipboard-".concat(te);if(Q.hasAttribute(G))return Q.getAttribute(G)}var ge=function(te){z(G,te);var Q=ae(G);function G(oe,U){var le;return D(this,G),le=Q.call(this),le.resolveOptions(U),le.listenClick(oe),le}return K(G,[{key:"resolveOptions",value:function(){var U=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};this.action=typeof U.action=="function"?U.action:this.defaultAction,this.target=typeof U.target=="function"?U.target:this.defaultTarget,this.text=typeof U.text=="function"?U.text:this.defaultText,this.container=$(U.container)==="object"?U.container:document.body}},{key:"listenClick",value:function(U){var le=this;this.listener=f()(U,"click",function($e){return le.onClick($e)})}},{key:"onClick",value:function(U){var le=U.delegateTarget||U.currentTarget,$e=this.action(le)||"copy",ce=S({action:$e,container:this.container,target:this.target(le),text:this.text(le)});this.emit(ce?"success":"error",{action:$e,text:ce,trigger:le,clearSelection:function(){le&&le.focus(),window.getSelection().removeAllRanges()}})}},{key:"defaultAction",value:function(U){return he("action",U)}},{key:"defaultTarget",value:function(U){var le=he("target",U);if(le)return document.querySelector(le)}},{key:"defaultText",value:function(U){return he("text",U)}},{key:"destroy",value:function(){this.listener.destroy()}}],[{key:"copy",value:function(U){var le=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{container:document.body};return E(U,le)}},{key:"cut",value:function(U){return C(U)}},{key:"isSupported",value:function(){var U=arguments.length>0&&arguments[0]!==void 0?arguments[0]:["copy","cut"],le=typeof U=="string"?[U]:U,$e=!!document.queryCommandSupported;return le.forEach(function(ce){$e=$e&&!!document.queryCommandSupported(ce)}),$e}}]),G}(d()),ve=ge},828:function(i){var a=9;if(typeof Element<"u"&&!Element.prototype.matches){var l=Element.prototype;l.matches=l.matchesSelector||l.mozMatchesSelector||l.msMatchesSelector||l.oMatchesSelector||l.webkitMatchesSelector}function s(d,u){for(;d&&d.nodeType!==a;){if(typeof d.matches=="function"&&d.matches(u))return d;d=d.parentNode}}i.exports=s},438:function(i,a,l){var s=l(828);function d(m,v,g,x,C){var y=f.apply(this,arguments);return m.addEventListener(g,y,C),{destroy:function(){m.removeEventListener(g,y,C)}}}function u(m,v,g,x,C){return typeof m.addEventListener=="function"?d.apply(null,arguments):typeof g=="function"?d.bind(null,document).apply(null,arguments):(typeof m=="string"&&(m=document.querySelectorAll(m)),Array.prototype.map.call(m,function(y){return d(y,v,g,x,C)}))}function f(m,v,g,x){return function(C){C.delegateTarget=s(C.target,v),C.delegateTarget&&x.call(m,C)}}i.exports=u},879:function(i,a){a.node=function(l){return l!==void 0&&l instanceof HTMLElement&&l.nodeType===1},a.nodeList=function(l){var s=Object.prototype.toString.call(l);return l!==void 0&&(s==="[object NodeList]"||s==="[object HTMLCollection]")&&"length"in l&&(l.length===0||a.node(l[0]))},a.string=function(l){return typeof l=="string"||l instanceof String},a.fn=function(l){var s=Object.prototype.toString.call(l);return s==="[object Function]"}},370:function(i,a,l){var s=l(879),d=l(438);function u(g,x,C){if(!g&&!x&&!C)throw new Error("Missing required arguments");if(!s.string(x))throw new TypeError("Second argument must be a String");if(!s.fn(C))throw new TypeError("Third argument must be a Function");if(s.node(g))return f(g,x,C);if(s.nodeList(g))return m(g,x,C);if(s.string(g))return v(g,x,C);throw new TypeError("First argument must be a String, HTMLElement, HTMLCollection, or NodeList")}function f(g,x,C){return g.addEventListener(x,C),{destroy:function(){g.removeEventListener(x,C)}}}function m(g,x,C){return Array.prototype.forEach.call(g,function(y){y.addEventListener(x,C)}),{destroy:function(){Array.prototype.forEach.call(g,function(y){y.removeEventListener(x,C)})}}}function v(g,x,C){return d(document.body,g,x,C)}i.exports=u},817:function(i){function a(l){var s;if(l.nodeName==="SELECT")l.focus(),s=l.value;else if(l.nodeName==="INPUT"||l.nodeName==="TEXTAREA"){var d=l.hasAttribute("readonly");d||l.setAttribute("readonly",""),l.select(),l.setSelectionRange(0,l.value.length),d||l.removeAttribute("readonly"),s=l.value}else{l.hasAttribute("contenteditable")&&l.focus();var u=window.getSelection(),f=document.createRange();f.selectNodeContents(l),u.removeAllRanges(),u.addRange(f),s=u.toString()}return s}i.exports=a},279:function(i){function a(){}a.prototype={on:function(l,s,d){var u=this.e||(this.e={});return(u[l]||(u[l]=[])).push({fn:s,ctx:d}),this},once:function(l,s,d){var u=this;function f(){u.off(l,f),s.apply(d,arguments)}return f._=s,this.on(l,f,d)},emit:function(l){var s=[].slice.call(arguments,1),d=((this.e||(this.e={}))[l]||[]).slice(),u=0,f=d.length;for(u;u<f;u++)d[u].fn.apply(d[u].ctx,s);return this},off:function(l,s){var d=this.e||(this.e={}),u=d[l],f=[];if(u&&s)for(var m=0,v=u.length;m<v;m++)u[m].fn!==s&&u[m].fn._!==s&&f.push(u[m]);return f.length?d[l]=f:delete d[l],this}},i.exports=a,i.exports.TinyEmitter=a}},r={};function n(i){if(r[i])return r[i].exports;var a=r[i]={exports:{}};return o[i](a,a.exports,n),a.exports}return function(){n.n=function(i){var a=i&&i.__esModule?function(){return i.default}:function(){return i};return n.d(a,{a}),a}}(),function(){n.d=function(i,a){for(var l in a)n.o(a,l)&&!n.o(i,l)&&Object.defineProperty(i,l,{enumerable:!0,get:a[l]})}}(),function(){n.o=function(i,a){return Object.prototype.hasOwnProperty.call(i,a)}}(),n(686)}().default})})(clipboard);var clipboardExports=clipboard.exports;const Clipboard=getDefaultExportFromCjs(clipboardExports),useClipboard=e=>{const t=(e==null?void 0:e.appendToBody)===void 0?!0:e.appendToBody;return{toClipboard(o,r){return new Promise((n,i)=>{const a=document.createElement("button"),l=new Clipboard(a,{text:()=>o,action:()=>"copy",container:r!==void 0?r:document.body});l.on("success",s=>{l.destroy(),n(s)}),l.on("error",s=>{l.destroy(),i(s)}),t&&document.body.appendChild(a),a.click(),t&&document.body.removeChild(a)})}}},_hoisted_1={key:1,style:{width:"100%","text-align":"center"}},_hoisted_2=createBaseVNode("div",{id:"patch"},null,-1),_sfc_main=defineComponent({__name:"App",setup(__props){const game=useGameStore(),osTheme=useOsTheme(),{toClipboard}=useClipboard(),theme=ref(darkTheme);Evt.on("THEME_CHANGE",Handler.alloc(null,e=>{const t=e==="dark";theme.value=t?darkTheme:void 0}),!1);const themeAuto=ref(!0);watch(osTheme,e=>{themeAuto.value&&Evt.emit("THEME_CHANGE",e)},{immediate:!0});const waitId=setInterval(()=>{if(typeof window.DASHBOARD_EXEC<"u"){themeAuto.value=!!window.DASHBOARD_EXEC("GetThemeAuto()");const e=!!window.DASHBOARD_EXEC("GetThemeType()");Evt.emit("THEME_CHANGE",e?"light":"dark"),clearInterval(waitId)}},100),gameFrame=ref(null);window.callFromSubWindow=e=>{window.subWindow=gameFrame.value.contentWindow},Object.defineProperty(window,"subWindow",{get(){return gameFrame.value.contentWindow}}),onMounted(()=>{isMacOS.value=navigator.userAgent.indexOf("Macintosh")!=-1,window.copy=(e,t)=>{(async r=>{try{t?await toClipboard(r,t):await toClipboard(r),window.$message.success("复制成功!")}catch(n){console.error(n)}})(e)},window.base64Encode=function(e){let t=new TextEncoder().encode(e),o="",r=t.byteLength;for(let n=0;n<r;n++)o+=String.fromCharCode(t[n]);return btoa(o)},window.base64Decode=function(e){let t=atob(e);return new TextDecoder("utf-8").decode(new Uint8Array([...t].map(r=>r.charCodeAt(0))))},window.Events=window.Events||{},window.Events.onMsg=game.onMsg,window.request=async e=>new Promise((t,o)=>{fetch(e).then(async r=>{if(r.status!==200)return o(r.statusText),void window.$message.error("资源下载失败!请重启后重试",{duration:1e4});const n=await r.body.getReader().read();return btoa(String.fromCharCode.apply(null,n.value))}).then(r=>{t(r)})}),scrollAnimation();const function_listener=setInterval(async()=>{if(window.REQ_JAVA_LOAD_FUNCTION!=null){let data=await window.REQ_JAVA_LOAD_FUNCTION();data=JSON.parse(data);for(const _ of data)_.visible&&!eval(_.visible)||game.funcs.push(_);clearInterval(function_listener)}},100);window.showTip=e=>{const t=document.getElementById("patch");t&&(t.style.display="inline-block",render$2(e,t))},window.hideTip=()=>{const e=document.getElementById("patch");e&&(e.style.display="none")}});const scrollAnimation=()=>{setInterval(()=>{buttons.value!=null&&(animCnt.value>20||(buttons.value.scrollLeft+=left.value?-10:10,animCnt.value+=1))},16)},isMacOS=ref(!1),left=ref(!1),animCnt=ref(21),buttons=ref(null),onScrollReq=e=>{isMacOS.value||(e.preventDefault(),left.value=e.deltaY<0,animCnt.value=0)},EVT_RELOAD=Handler.alloc(null,evl=>{const dlg=window.$dialog.info({title:"刷新页面？",closable:!1,autoFocus:!1,content:()=>"",action:()=>h(ButtonGroup,{action:[{label:"确定",type:"success",callback:()=>{dlg.destroy(),eval(evl)}},{label:"取消",type:"default",callback:()=>dlg.destroy()}]})})}),EVT_BACK_TO_CITY=Handler.alloc(null,()=>{if(window._world&&!window._world.isEnterGame())return void window.$message.error("请先进入游戏");eval("City.doEnterCity(xself.getId());")});Evt.on("EVT_RELOAD",EVT_RELOAD,!0),Evt.on("EVT_BACK_TO_CITY",EVT_BACK_TO_CITY,!0);const zoom=ref(1);return watchEffect(()=>{}),window.ON_ZOOM_EVT=e=>{zoom.value!=e&&(zoom.value=e,window.X_SCALE=e,setTimeout(()=>{try{Main.instance.stage.setContentSize(640,960),nato.initialize(640,960,!0)}catch{window.$message.error("缩放出错，请刷新页面！")}},1e3))},(e,t)=>{const o=__unplugin_components_0,r=NDialogProvider,n=__unplugin_components_2,i=__unplugin_components_3$1,a=__unplugin_components_4;return openBlock(),createBlock(a,{theme:unref(theme)},{default:withCtx(()=>[createVNode(o,null,{default:withCtx(()=>[createVNode(_sfc_main$4)]),_:1}),createVNode(r,null,{default:withCtx(()=>[createVNode(_sfc_main$5)]),_:1}),createBaseVNode("iframe",{ref_key:"gameFrame",ref:gameFrame,src:"./game-only.html",frameborder:"0",class:"game-iframe"},null,512),unref(game).inject?(openBlock(),createElementBlock("div",{key:0,ref_key:"buttons",ref:buttons,id:"btn-container",onWheel:onScrollReq},[(openBlock(!0),createElementBlock(Fragment,null,renderList(unref(game).funcs,(l,s)=>(openBlock(),createBlock(Function$1,{data:l,index:s,class:"function",size:"small"},{default:withCtx(()=>[createTextVNode(toDisplayString(l.name),1)]),_:2},1032,["data","index"]))),256))],544)):(openBlock(),createElementBlock("div",_hoisted_1,[createVNode(n,{id:"tip",class:"gradientText"},{default:withCtx(()=>[createTextVNode(" 正在加载功能列表，请稍等。。。")]),_:1})])),createVNode(i),_hoisted_2]),_:1},8,["theme"])}}}),app=createApp(_sfc_main);app.use(createPinia());app.mount("#app")});export default Lt();

</script>
  <style>
:root{--vt-c-white: #ffffff;--vt-c-white-soft: #f8f8f8;--vt-c-white-mute: #f2f2f2;--vt-c-black: #181818;--vt-c-black-soft: #222222;--vt-c-black-mute: #282828;--vt-c-indigo: #2c3e50;--vt-c-divider-light-1: rgba(60, 60, 60, .29);--vt-c-divider-light-2: rgba(60, 60, 60, .12);--vt-c-divider-dark-1: rgba(84, 84, 84, .65);--vt-c-divider-dark-2: rgba(84, 84, 84, .48);--vt-c-text-light-1: var(--vt-c-indigo);--vt-c-text-light-2: rgba(60, 60, 60, .66);--vt-c-text-dark-1: var(--vt-c-white);--vt-c-text-dark-2: rgba(235, 235, 235, .64)}:root{--color-background: var(--vt-c-white);--color-background-soft: var(--vt-c-white-soft);--color-background-mute: var(--vt-c-white-mute);--color-border: var(--vt-c-divider-light-2);--color-border-hover: var(--vt-c-divider-light-1);--color-heading: var(--vt-c-text-light-1);--color-text: var(--vt-c-text-light-1);--section-gap: 160px}@media (prefers-color-scheme: dark){:root{--color-background: var(--vt-c-black);--color-background-soft: var(--vt-c-black-soft);--color-background-mute: var(--vt-c-black-mute);--color-border: var(--vt-c-divider-dark-2);--color-border-hover: var(--vt-c-divider-dark-1);--color-heading: var(--vt-c-text-dark-1);--color-text: var(--vt-c-text-dark-2)}}*,*:before,*:after{box-sizing:border-box;margin:0;font-weight:400}body{min-height:100vh;color:var(--color-text);background:var(--color-background);transition:color .5s,background-color .5s;line-height:1.6;font-family:Inter,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Oxygen,Ubuntu,Cantarell,Fira Sans,Droid Sans,Helvetica Neue,sans-serif;font-size:15px;text-rendering:optimizeLegibility;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale}body,.container[data-v-ea45a382]{overflow:hidden}.bottom-left-radius[data-v-ea45a382]{border-bottom-left-radius:20px}.bottom-right-radius[data-v-ea45a382]{border-bottom-right-radius:20px}.button-group[data-v-ea45a382]{text-align:center;padding:12px;font-size:16px;font-weight:600;width:100%;user-select:none;-webkit-user-select:none;transition-property:background;transition-duration:.5s;height:48px;border-radius:0}.button-group-half[data-v-ea45a382]{width:50%!important;float:left}.cs-full-width[data-v-ea45a382]{width:100%}.cs-t0[data-v-ea45a382]{border-top:1px solid var(--button-group-border-color, hsla(240, 1%, 72%, .25))!important}.cs-t1[data-v-ea45a382]{border-right:1px solid var(--button-group-border-color, hsla(240, 1%, 72%, .25))!important}.cs-t2[data-v-ea45a382]{border-left:1px solid var(--button-group-border-color, hsla(240, 1%, 72%, .25))!important}.n-modal-container{z-index:2047!important}.n-drawer-container{z-index:2048!important}.n-dialog{border-radius:25px;backdrop-filter:blur(16px);-webkit-backdrop-filter:blur(16px);box-shadow:#0000004d 8px 8px 8px 8px;border:0 rgba(255,255,255,.4) solid;border-bottom:0 rgba(40,40,40,.35) solid;border-right:0 rgba(40,40,40,.35) solid;padding:0!important;background-color:var(--dialog-bg-color, rgba(255, 255, 255, .5));zoom:var(--dialog-scale, 1)}.n-dialog__title{justify-content:center;letter-spacing:2px;padding:20px}.n-dialog__content{text-align:center;letter-spacing:2px;display:flex;flex-direction:row;flex-wrap:nowrap;justify-content:center;align-items:center;align-content:center}.n-message-container{z-index:2048!important}.n-message{color:#fff!important;backdrop-filter:blur(16px);-webkit-backdrop-filter:blur(16px);box-shadow:#0000004d 8px 8px 8px 8px;border:0 rgba(255,255,255,.4) solid;border-bottom:0 rgba(40,40,40,.35) solid;border-right:0 rgba(40,40,40,.35) solid;position:relative;background:#000c;border-radius:20px;display:flex;justify-content:center;align-items:center;overflow:hidden}.n-message:before{content:"";position:absolute;width:120%;height:40%;background:linear-gradient(#0cf,#d500f9);animation:rotate 4s linear infinite}.n-message:after{content:"";position:absolute;background:#0e1538;backdrop-filter:blur(16px);-webkit-backdrop-filter:blur(16px);top:1px;right:1px;bottom:1px;left:1px;border-radius:20px}@keyframes rotate{0%{transform:rotate(0)}to{transform:rotate(360deg)}}@-webkit-keyframes rotate{0%{transform:rotate(0)}to{transform:rotate(360deg)}}.n-message__icon,.n-message__content{z-index:1;user-select:none;-webkit-user-select:none}input[data-v-c0238878]{width:160px;background-color:transparent;border:1px solid hsla(240,1%,72%,.25);color:#fff}.line-space[data-v-c0238878]{width:100%;margin-top:4px}.n-text[data-v-c0238878]{font-weight:bolder}.white-text[data-v-d5d0cdbc]{color:#fff!important}.green-text[data-v-d5d0cdbc]{color:#2ecc71!important}.blue-text[data-v-d5d0cdbc]{color:#3eb8fa!important}.orange-text[data-v-d5d0cdbc]{color:#ffb302!important}.purple-text[data-v-d5d0cdbc]{color:#be71f4!important}.red-text[data-v-d5d0cdbc]{color:#c23616!important}input[data-v-341baa98]{width:160px;background-color:transparent;border:1px solid hsla(240,1%,72%,.25);color:#fff}.line-space[data-v-341baa98]{width:100%;margin-top:4px}.n-text[data-v-341baa98]{color:#fff;font-weight:bolder}#full[data-v-ce7d50c7]{height:100vh;width:100vw;position:absolute;left:0;top:0;z-index:2099;backdrop-filter:blur(5px);-webkit-backdrop-filter:blur(1px);background-color:#21202080;box-shadow:#0000004d 8px 8px 8px 8px;border:0 rgba(255,255,255,.4) solid;border-bottom:0 rgba(40,40,40,.35) solid;border-right:0 rgba(40,40,40,.35) solid}#identify-div[data-v-8a0c648a]{max-height:12rem;overflow-y:scroll}#mask-top[data-v-8a0c648a]{width:100%;position:absolute;top:0;left:0;transform:rotate(180deg);transition:all .3s ease}#mask-bottom[data-v-8a0c648a]{width:100%;position:absolute;bottom:0;left:0;transition:all .3s ease}#mask-top[data-v-8a0c648a]:before{background-image:linear-gradient(180deg,#e9edf700,#16171afa);content:"";height:10px;opacity:1;pointer-events:none;position:absolute;bottom:0;left:0;width:100%}#mask-bottom[data-v-8a0c648a]:before{background-image:linear-gradient(180deg,#e9edf700,#16171afa);content:"";height:10px;opacity:1;pointer-events:none;position:absolute;bottom:0;left:0;width:100%}input[data-v-8a0c648a]{width:160px;background-color:transparent;border:1px solid hsla(240,1%,72%,.25);color:#fff}.line-space[data-v-8a0c648a]{width:100%;margin-top:4px}.n-text[data-v-8a0c648a]{color:#fff;font-weight:bolder}#full[data-v-e2439492]{height:100vh;width:100vw;position:absolute;left:0;top:0;z-index:19;backdrop-filter:blur(1px);-webkit-backdrop-filter:blur(1px);background-color:#21202080;box-shadow:#0000004d 8px 8px 8px 8px;border:0 rgba(255,255,255,.4) solid;border-bottom:0 rgba(40,40,40,.35) solid;border-right:0 rgba(40,40,40,.35) solid;display:flex;justify-content:center;align-items:center}.n-data-table-th__title{font-size:12px!important}.bag-clear-span-text{color:#95a5a6!important;font-size:14px!important}.white-text{color:#fff!important}.green-text{color:#2ecc71!important}.blue-text{color:#3eb8fa!important}.orange-text{color:#ffb302!important}.purple-text{color:#be71f4!important}.red-text{color:#c23616!important}.time-text{color:#f7eead!important}.row-selected{background-color:red!important}.n-data-table-th__title{color:#fff!important}.n-data-table .n-data-table-table tr:hover>td{background-color:#f006!important}.n-data-table-table,.n-data-table-thead,.n-data-table-tr,.n-data-table-th,.n-data-table-tbody{background-color:transparent!important}.n-data-table-td{background-color:transparent!important;color:#fff!important}input[data-v-852cf801]{width:160px;background-color:transparent;border:1px solid hsla(240,1%,72%,.25);color:#fff}.line-space[data-v-852cf801]{width:100%;margin-top:4px}.n-text[data-v-852cf801]{color:#fff;font-weight:bolder}[data-v-a142c50c]:root{--btn-color: #fff}*[data-v-a142c50c]{margin:0;padding:0;box-sizing:border-box}button[data-v-a142c50c]{color:#fff!important;letter-spacing:1px;padding-left:2px;padding-right:2px;border:1px solid rgba(255,255,255,0)!important}button[data-v-a142c50c]:hover{border:1px solid rgba(255,255,255,.9)!important}.active[data-v-a142c50c]{border-color:#2ed573e6!important;color:#2ed573e6!important}button.loading[data-v-a142c50c]:after{content:"";position:absolute;left:-1px;top:-1px;right:-1px;bottom:-1px;border-radius:inherit}button.loading[data-v-a142c50c]:before{content:"";display:inline-block;width:14px;height:14px;margin-left:2px;color:#fff;border:1px solid #fff;border-radius:50%;vertical-align:-20%;clip-path:polygon(0 0,100% 0,100% 100%,0 60%);animation:loading-a142c50c 2s linear infinite}@keyframes loading-a142c50c{0%{transform:rotate(0)}to{transform:rotate(360deg)}}*{-webkit-user-select:none;user-select:none}#app{width:100vw;height:100vh;overflow:hidden}::-webkit-scrollbar{width:1px;height:2px;display:none}::-webkit-scrollbar-track{background:#fff}::-webkit-scrollbar-thumb{border-radius:1px;background-color:#0452d9}#tip{line-height:48px;font-weight:bolder;transition:color .5s}.game-iframe{position:fixed;top:0;left:0;width:100vw;height:calc(100vh - 40px);border:none;z-index:999}@keyframes gradient{0%{color:red}50%{color:#00f}to{color:green}}.gradientText{animation:gradient 3s infinite alternate}#btn-container{position:absolute;bottom:2px;width:100vw;height:var(--btn-container-height, 36px);display:flex;flex-direction:row;flex-wrap:nowrap;align-content:center;justify-content:flex-start;align-items:center;gap:10px;backdrop-filter:blur(16px);-webkit-backdrop-filter:blur(16px);box-shadow:#0000004d 8px 8px 8px 8px;border:0 rgba(255,255,255,.4) solid;border-bottom:0 rgba(40,40,40,.35) solid;border-right:0 rgba(40,40,40,.35) solid;background-color:#111111b3;color:#fff!important;overflow-x:auto;overflow-y:hidden}#btn-container:first-child{padding-left:10px;padding-right:10px}.function{color:#fff!important}.btn-cbs{background:linear-gradient(90deg,#8bdeda,#43add0,#998ee0,#e17dc2,#ef9393,#8bdeda);background-size:400%}.btn-cbs:before{content:"";position:absolute;background:inherit;top:-5px;left:-5px;right:-5px;bottom:-5px;border-radius:6px;filter:blur(20px);opacity:0;transition:opacity .5s;z-index:-1}.btn-cbs{z-index:1;animation:glow 8s linear infinite}@keyframes glow{0%{background-position:0}to{background-position:400%}}

</style>
</head>

<body>
    <div id="app" class="btn-cbs"> </div>
</body>

</html>