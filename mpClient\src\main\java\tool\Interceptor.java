package tool;

import com.teamdev.jxbrowser.deps.com.google.protobuf.ByteString;
import com.teamdev.jxbrowser.deps.com.google.protobuf.CodedInputStream;
import com.teamdev.jxbrowser.net.HttpHeader;
import com.teamdev.jxbrowser.net.HttpStatus;
import com.teamdev.jxbrowser.net.UrlRequestJob;
import com.teamdev.jxbrowser.net.callback.InterceptUrlRequestCallback;
import mgr.NetManager;

import javax.annotation.CheckForNull;
import java.io.IOException;
import java.nio.charset.StandardCharsets;


// 用来拦截小程序的js文件  实现动态注入,因为 NetworkDelegate只能实现请求发起前拦截，响应只能在这里修改
public class Interceptor implements InterceptUrlRequestCallback {

    @CheckForNull
    @Override
    public Response on(Params params) {
        String url = params.urlRequest().url();
        if (!url.contains("resgood/js/") || !url.endsWith(".js")) {
            return InterceptUrlRequestCallback.Response.proceed();
        }
        UrlRequestJob.Options.Builder builder = UrlRequestJob.Options
                .newBuilder(HttpStatus.OK);
        params.httpHeaders().forEach(builder::addHttpHeader);
        UrlRequestJob job = params.newUrlRequestJob(builder.build());
        try {
            String content = NetManager.reqString(url);
            int logic_a = content.indexOf(",ib(ib");
            if (logic_a != -1) {
                content = this.inject(content, logic_a, ",window.xglobal = ib");
            }
            int logic_b = content.indexOf(",t(t.s");
            if (logic_b != -1) {
                content = this.inject(content, logic_b, ",window.xglobal = t");
            }
            job.write(content.getBytes(StandardCharsets.UTF_8));
            job.complete();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return InterceptUrlRequestCallback.Response.intercept(job);
    }

    private String inject(String content, int logic_a, String code) {
        String first = content.substring(0, logic_a);
        String last = content.substring(logic_a);
        return first + code + last;
    }

}
