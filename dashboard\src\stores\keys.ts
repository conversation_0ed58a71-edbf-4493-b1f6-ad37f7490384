import {ref, computed} from 'vue'
import {defineStore} from 'pinia'
import type {Key} from "@/core/z";


/**
 * 因为n-grid渲染不能使用v-for一次性渲染两个元素，所以这里将两个元素手动拆分代码意义上的两个部分
 * 奇数的只做名字显示
 * 偶数的要做名字和快捷键的显示  同时需要有status标注 对应渠道是否支持
 *
 */
const defaultKeys: Key[] = [
    {sort: 1, name: '页面刷新', tip: '', modify: false, status: []},
    {sort: 2, name: 'F5', tip: '刷新界面操作', modify: false, status: [true, true, true]},
    {sort: 3, name: '地图工具', tip: '', modify: true, status: []},
    {sort: 4, name: 'F6', tip: '打开地图工具', modify: true, status: [true, true, false]},
    {sort: 5, name: '快速回城', tip: '', modify: true, status: []},
    {sort: 6, name: 'F7', tip: '返回个人城市', modify: true, status: [true, true, false]},
]

export const useKeyStore = defineStore('keys', () => {

    const keyData = ref<Key[]>([])
    const listenerInputKey = ref(false)
    const changeKeyEvent = ref<((targetKey: string) => void) | null>()

    const getKeyData = () => localStorage.getItem("_dashboard_keys") || ""
    const setKeyData = () => localStorage.setItem("_dashboard_keys", JSON.stringify(keyData.value))

    function initKeyData() {
        keyData.value.length = 0
        const str = getKeyData()
        if (str) {
            keyData.value = JSON.parse(str)
        }
        keyData.value = keyData.value.filter((item) => !!defaultKeys.find((key) => key.sort === item.sort))

        defaultKeys.forEach((item) => {
            let add = true
            keyData.value.forEach((key) => {
                if (key.sort === item.sort) {
                    key.status = item.status
                    add = false
                }
            })
            if (add) {
                keyData.value.push(item)
                add = false
            }
        })
    }

    window.ON_UNKNOWN_KEY = (keyCode: string) => {
        const target = keyCode.replace("KEY_CODE_", "")
        const func = changeKeyEvent.value
        if (func && listenerInputKey.value) {
            if (!!keyData.value.find(k => k.name == target)) {
                return void window.$message.error(`按键${target}已经被占用`)
            }
            window.$message.success(`修改按键为：${target}`)
            listenerInputKey.value = false
            func(target)
            setKeyData()
            changeKeyEvent.value = null
            return
        }
        // dashboard 可能不需要触发key事件
        // console.log("需要触发key事件", keyCode)
    }


    return {keyData, initKeyData, setKeyData, listenerInputKey, changeKeyEvent}
})
