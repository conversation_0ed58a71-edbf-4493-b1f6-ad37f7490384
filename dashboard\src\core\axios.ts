//导入axios
import axios from 'axios'
// @ts-ignore
import CryptoJ<PERSON> from 'crypto-js';
// @ts-ignore
window["CryptoJS"] = CryptoJS

type RequestWait = {
    url: string,
    args: any,
    resolve: any
}

// window.DEBUG = false;
const dev_url = "http://localhost:9001"
const pro_url = "https://h5.javaelf.cn/"

let API: any
// API创建之前的请求放入这个列表
const waitQueue: RequestWait[] = []

const sid = setInterval(() => {
    if (typeof window.DEBUG == "undefined") return
    API = axios.create({
        baseURL: window.DEBUG ? dev_url : pro_url, //请求后端数据的基本地址，自定义
        timeout: 60000                   //请求超时设置，单位ms
    })
    API.defaults.headers.common["Access-Control-Allow-Origin"] = "*"
    API.defaults.headers.common["Access-Control-Allow-Methods"] = "POST, GET"
    API.defaults.headers.common["Access-Control-Allow-Headers"] = "Content-Type, Authorization"
    clearInterval(sid)
    // @ts-ignore
    window["API"] = API
    sendWaitQueue()
}, 100)

const sendWaitQueue = async () => {
    // 发送等待队列
    console.log("http wait queue 开始发送:", waitQueue.length)
    for (let requestWait of waitQueue) {
        const data = await post(requestWait.url, requestWait.args);
        requestWait.resolve(data)
    }
    waitQueue.length = 0
}

const encrypt = (data: any, reqId: number) => {
    // const xor = Math.floor(Math.random() * 127) + 1;
    if (typeof data != "object") {
        return console.error("数据格式错误，必须是对象data")
    }
    const obj = {
        key: "",
        reqId,
        data: "",
    }
    const random = new Random()
    random.setSeed(BigInt(obj.reqId))
    let key = random.nextInt() + ""
    // 取最后两位
    key = key.substring(key.length - 2, key.length)
    const xor = Number(key)
    const sData = JSON.stringify(data)
    if (sData != "") {
        const sMd5 = CryptoJS.MD5(sData).toString()
        obj.key = btoa(sMd5)
        let buffer = new TextEncoder().encode(sData)
        buffer = buffer.map(i => i ^ xor)
        const fs = new TextDecoder().decode(buffer);
        obj.data = btoa(fs)
    }
    return obj
}

const decrypt = (sendReqId: number, resp: { data: string, key: string, reqId: number }): null | {
    data: any,
    code: number,
    notify: string
} => {
    if (resp == null) return null
    if (sendReqId != resp.reqId) {
        return null
    }
    const random = new Random()
    random.setSeed(BigInt(sendReqId))
    let key = random.nextInt() + ""
    // 取最后两位
    key = key.substring(key.length - 2, key.length)
    const xorData = atob(resp.data)
    let bytes = new Uint8Array(xorData.length);
    let iv = Number(key)
    for (let i = 0; i < xorData.length; i++) {
        bytes[i] = xorData.charCodeAt(i) ^ iv;
    }
    let str = new TextDecoder().decode(bytes);
    let originalData = JSON.parse(str);
    const md5 = CryptoJS.enc.Base64.parse(resp.key).toString(CryptoJS.enc.Utf8)
    if (CryptoJS.MD5(str).toString() != md5) {
        window.$message.error("消息验证失败!")
        return null
    }
    return originalData
}

export const post = async function <T>(url: string, args?: any): Promise<T> {
    return new Promise<T>((resolve, reject) => {
        let params = null
        const reqId = Date.now()
        if (args) {
            params = encrypt(args, reqId)
        }
        const token = localStorage.getItem("_dashboard_token") || ""
        try {
            if (typeof API == "undefined") {
                const l: RequestWait = {url, args, resolve}
                waitQueue.push(l)
                return
            }
            API.request({
                url,
                data: params,
                method: 'POST',
                headers: {
                    Authorization: token
                }
            }).then(async (response: any) => {
                const data = decrypt(reqId, response.data)
                if (data == null) {
                    resolve(null as T)
                    return
                }
                if (data.code != 0 && data.notify) {
                    window.$message.error(data.notify)
                    resolve(null as T)
                }
                if (data.code == 0 && data.notify) {
                    window.$message.success(data.notify)
                }
                if (data.code == 0 && data.data && data.data.machine) {
                    const code = await window.getMachine()
                    if (data.data.machine != code) {
                        window.$message.error("消息验证失败!")
                        return null as T
                    }
                }
                resolve(data.data as T)
            }).catch((e: any) => {
                window.$message.error("网络错误!")
                resolve(null as T)
            })
        } catch (e: any) {
            const {code} = e
            if (code == "ERR_NETWORK") {
                window.$message.error("网络错误!")
            }
            resolve(null as T)
        }
    })
}


export class Random {
    private static readonly RANDOM_SEED_MASTER = BigInt(25214903917);
    private static readonly ADDEND = BigInt(11);
    private static readonly RANDOM_SEED_MASK = (BigInt(1) << BigInt(48)) - BigInt(1);
    private static readonly MOD = BigInt(1) << BigInt(64);

    private seed: bigint;

    constructor() {
        this.seed = BigInt(0);
        this.setSeed(BigInt(1));
    }

    setSeed(t: bigint) {
        this.seed = (t ^ Random.RANDOM_SEED_MASTER) & Random.RANDOM_SEED_MASK;
        this.seed %= Random.MOD;
    }

    next(t: number) {
        this.seed = (this.seed * Random.RANDOM_SEED_MASTER + Random.ADDEND) & Random.RANDOM_SEED_MASK;
        this.seed %= Random.MOD;
        let i = Number(this.seed >> BigInt(48 - t));
        return this.unsignedInt2int(i);
    }

    nextInt() {
        return this.next(32);
    }

    private unsignedInt2int(e: number) {
        let t = 4294967295 & e;
        if (t > 2147483647) {
            return -((4294967295 ^ t) + 1);
        }
        return t;
    }
}

// @ts-ignore
window["Random"] = Random
