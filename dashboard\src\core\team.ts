import Evt from "@/core/evt";
import Handler from "@/core/Handler";

type Member = {
    id: number,
    gameId: string
}

export class Team {
    readonly _members: Member[];
    _leader: Member | null;

    constructor() {
        this._members = [];
        this._leader = null;
    }

    public setLeader(leaderId: number, gameId: string) {
        this._leader = {id: leaderId, gameId: gameId};
        return this.putMember(leaderId, gameId);
    }

    public putMember(memberId: number, gameId: string) {
        const leaderId = this._leader?.id
        const exists = this._members.find(v => v.id === memberId)
        if (exists) {
            exists.gameId = gameId
        } else {
            Evt.off(`ON_TEAM_MSG_${leaderId}_${memberId}`)
            this._members.push({id: memberId, gameId});
        }
        Evt.on(`ON_TEAM_MSG_${leaderId}_${memberId}`, Handler.alloc(null, (sender: number, msg: string) => {
            window.GAME_EXEC(gameId, `typeof window.onTeamMsg === 'function' && window.onTeamMsg(${leaderId}, ${memberId}, ${sender}, '${msg}')`)
        }), true)
        return this
    }

    public removeMember(memberId: number, gameId: string) {
        const leaderId = this._leader?.id
        const index = this._members.findIndex(v => v.id === memberId && v.gameId == gameId)
        if (index === -1) return null
        Evt.off(`ON_TEAM_MSG_${leaderId}_${memberId}`)
        return this._members.splice(index, 1)
    }

    public getMemberSize() {
        return this._members ? this._members.length : 0;
    }

    public destroy() {
        for (const {id, gameId} of this._members) {
            Evt.off(`ON_TEAM_MSG_${this._leader}_${id}`)
        }
        this._leader = null
        this._members.length = 0
    }

}

export class Checker {
    public static teams: { [key: number]: Team } = {};

    public static newTeam(leaderId: number, gameId: string) {
        const team = new Team()
        this.teams[leaderId] = team
        team.setLeader(leaderId, gameId)
        return team
    }

    public static destroyTeam(leaderId: number) {
        const team = this.teams[leaderId]
        if (team) {
            team.destroy()
            delete this.teams[leaderId]
        }
    }

    public static onTeamMsg(leaderId: number, sender: number, gameId: string, msg: string) {
        const team = this.teams[leaderId]
        if (!team) return
        for (const {id, gameId} of team._members) {
            Evt.emit(`ON_TEAM_MSG_${leaderId}_${id}`, sender, msg)
        }
        return ""
    }

    public static subscribeTeam(leaderId: number, selfId: number, gameId: string) {
        let team = this.teams[leaderId]
        if (!team) {
            team = this.newTeam(leaderId, gameId)
        }
        if (!team._leader?.gameId && leaderId == selfId) {
            team.setLeader(selfId, gameId)
        }
        team.putMember(selfId, gameId)
        return ""
    }

    public static unSubscribeTeam(selfId: number, gameId: string) {
        for (const leader in this.teams) {
            const team = this.teams[leader]
            const removeMember = team.removeMember(selfId, gameId);
            if (team.getMemberSize() == 0) {
                team.destroy()
                delete this.teams[leader]
            }
        }
        return ""
    }

}
