/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

declare module 'vue' {
  export interface GlobalComponents {
    ButtonGroup: typeof import('./src/components/button-group/index.vue')['default']
    Dashboard: typeof import('./src/components/dashboard.vue')['default']
    DialogApi: typeof import('./src/components/dialog-api.vue')['default']
    Download: typeof import('./src/components/download.vue')['default']
    GameSetting: typeof import('./src/components/game-setting.vue')['default']
    Login: typeof import('./src/components/login.vue')['default']
    MessageApi: typeof import('./src/components/message-api.vue')['default']
    My: typeof import('./src/components/my.vue')['default']
    NButton: typeof import('naive-ui')['NButton']
    NConfigProvider: typeof import('naive-ui')['NConfigProvider']
    NDialogProvider: typeof import('naive-ui')['NDialogProvider']
    NEllipsis: typeof import('naive-ui')['NEllipsis']
    NForm: typeof import('naive-ui')['NForm']
    NFormItemGi: typeof import('naive-ui')['NFormItemGi']
    NGi: typeof import('naive-ui')['NGi']
    NGlobalStyle: typeof import('naive-ui')['NGlobalStyle']
    NGradientText: typeof import('naive-ui')['NGradientText']
    NGrid: typeof import('naive-ui')['NGrid']
    NIcon: typeof import('naive-ui')['NIcon']
    NInput: typeof import('naive-ui')['NInput']
    NInputNumber: typeof import('naive-ui')['NInputNumber']
    NMessageProvider: typeof import('naive-ui')['NMessageProvider']
    NProgress: typeof import('naive-ui')['NProgress']
    NSpace: typeof import('naive-ui')['NSpace']
    NSwitch: typeof import('naive-ui')['NSwitch']
    NText: typeof import('naive-ui')['NText']
    NTooltip: typeof import('naive-ui')['NTooltip']
    Register: typeof import('./src/components/register.vue')['default']
    Setting: typeof import('./src/components/setting.vue')['default']
    Tip: typeof import('./src/components/tip.vue')['default']
  }
}
